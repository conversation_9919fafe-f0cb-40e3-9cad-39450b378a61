const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const { walletService } = require('../services');
const { Vendor } = require('../models');
const { Op } = require('sequelize');
const logger = require('../config/logger');

/**
 * Create a wallet (admin route)
 */
const createWallet = catchAsync(async (req, res) => {
  // If user is creating their own wallet, use their ID
  if (!req.body.userId && !req.body.vendorId) {
    req.body.userId = req.user.id;
  }

  const wallet = await walletService.createWallet(req.body);
  res.status(httpStatus.CREATED).json({
    status: true,
    data: wallet,
    message: 'Wallet created successfully',
  });
});

/**
 * Create a wallet for the current user
 */
const createUserWallet = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { currency } = req.body;

  const wallet = await walletService.createUserWallet(userId, currency);

  res.status(httpStatus.CREATED).json({
    status: true,
    data: wallet,
    message: 'User wallet created successfully',
  });
});

/**
 * Create a wallet for the current vendor
 */
const createVendorWallet = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { currency } = req.body;

  logger.info(`Creating vendor wallet for userId: ${userId}`);

  const vendor = await Vendor.findOne({ where: { userId } });
  if (!vendor) {
    logger.warn(`Vendor not found for userId: ${userId}`);
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
  }

  try {
    const wallet = await walletService.createVendorWallet(userId, currency);
    logger.info(`Vendor wallet created successfully for userId: ${userId}, walletId: ${wallet.id}`);

    res.status(httpStatus.CREATED).json({
      status: true,
      data: wallet,
      message: 'Vendor wallet created successfully',
    });
  } catch (error) {
    logger.error(`Error creating vendor wallet for userId: ${userId}`, { error: error.message });
    throw error;
  }
});


/**
 * Get current user's wallet
 */
const getCurrentUserWallet = catchAsync(async (req, res) => {
  const userId = req.user.id;

  const wallet = await walletService.getWalletByUserId(userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: wallet,
    message: 'Wallet retrieved successfully',
  });
});

/**
 * Get current vendor's wallet
 */
const getCurrentVendorWallet = catchAsync(async (req, res) => {
  const userId = req.user.id;

  // First check if the user is a vendor
  const vendor = await Vendor.findOne({ where: { userId } });

  if (!vendor) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
  }

  try {
    const wallet = await walletService.getWalletByUserId(userId, 'vendor');

    res.status(httpStatus.OK).json({
      status: true,
      data: wallet,
      message: 'Wallet retrieved successfully',
    });
  } catch (error) {
    if (error.statusCode === httpStatus.NOT_FOUND) {
      // If wallet doesn't exist, return a clear message
      throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found for this vendor. Please create a wallet first.');
    }
    throw error;
  }
});

/**
 * Top up wallet
 */
const topUpWallet = catchAsync(async (req, res) => {
  const { walletId } = req.params;
  const userId = req.user.id;
  const { amount, metadata, referenceId } = req.body;

  logger.info(`Processing wallet top-up - walletId: ${walletId}, amount: ${amount}, referenceId: ${referenceId}`);

  // Check if the user is authorized to top up this wallet
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to top up this wallet');
  }

  const result = await walletService.topUpWallet(walletId, amount, metadata, referenceId);
  
  logger.info(`Wallet top-up successful - walletId: ${walletId}, transactionId: ${result.transaction.id}`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Wallet topped up successfully',
  });
});

/**
 * Debit from wallet
 */
const debitFromWallet = catchAsync(async (req, res) => {
  const { walletId } = req.params;
  const userId = req.user.id;
  const { amount, type, description, metadata, referenceId } = req.body;

  // Check if the user is authorized to debit from this wallet
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to debit from this wallet');
  }

  const result = await walletService.debitFromWallet(walletId, amount, type, description, metadata, referenceId);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Amount debited from wallet successfully',
  });
});



/**
 * Get wallet transactions
 */
const getWalletTransactions = catchAsync(async (req, res) => {
  const { walletId } = req.params;
  const userId = req.user.id;
  const { type, status, startDate, endDate, page, limit, sortBy, sortOrder } = req.query;

  logger.debug('Fetching wallet transactions', {
    walletId,
    filters: { type, status, startDate, endDate },
    pagination: { page, limit, sortBy, sortOrder }
  });

  // Check if the user is authorized to view this wallet's transactions
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view transactions for this wallet');
  }

  // Build filter object
  const filter = {};
  if (type) filter.type = type;
  if (status) filter.status = status;

  // Add date range filter if provided
  if (startDate || endDate) {
    filter.createdAt = {};
    if (startDate) filter.createdAt[Op.gte] = new Date(startDate);
    if (endDate) filter.createdAt[Op.lte] = new Date(endDate);
  }

  const options = {
    page,
    limit,
    sortBy,
    sortOrder
  };

  const result = await walletService.getWalletTransactions(walletId, filter, options);

  logger.debug(`Retrieved ${result.transactions.length} transactions for walletId: ${walletId}`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Transactions retrieved successfully',
  });
});

/**
 * Get transaction by ID
 */
const getTransactionById = catchAsync(async (req, res) => {
  const { walletId, transactionId } = req.params;
  const userId = req.user.id;

  // Check if the user is authorized to view this wallet's transactions
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this transaction');
  }

  // Fetch the transaction and verify it belongs to the specified wallet
  const transaction = await walletService.getTransactionById(transactionId);

  res.status(httpStatus.OK).json({
    status: true,
    data: transaction,
    message: 'Transaction retrieved successfully',
  });
});

module.exports = {
  createWallet,
  createUserWallet,
  createVendorWallet,
  getCurrentUserWallet,
  getCurrentVendorWallet,
  topUpWallet,
  debitFromWallet,
  getWalletTransactions,
  getTransactionById,
};


