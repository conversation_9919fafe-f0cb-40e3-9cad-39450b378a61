const { Chat, ChatMessage, User, Admin } = require('../models');
const { sendToTopic } = require('../microservices/notification.service');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const { RECIPIENT_TYPE } = require('../constants');
const createChat = async (userId, topic, bookingId) => {
  const firebaseTopicId = `chat_${userId}`;
  
  const existingChat = await Chat.findOne({
    where: { userId, topic, status: 'active' }
  });

  if (existingChat) {
    return existingChat; // Return existing chat instead of creating a new one
  }

  const chat = await Chat.create({ userId, topic, firebaseTopicId, bookingId });

  await ChatMessage.create({
    chatId: chat.id,
    senderId: userId, 
    message: `New support chat initiated regarding: ${topic}`,
    isSystem: true,
    firebaseMessageId: `system_${Date.now()}`
  });

  // Send notification to the chat topic (both admin and user will receive)
  await sendToTopic(
    "admin_support_channel",
    {
      title: 'New Support Request',
      body: `New chat request regarding: ${topic}`
    }, 
    {
      chatId: chat.id.toString(),
      userId: userId.toString(),
      topic,
      type: 'new_support_request',
      timestamp: Date.now().toString(),
      notificationType: 'SUPPORT_REQUEST',
      referenceId: chat.id,
      referenceType: 'chat'
    },
    RECIPIENT_TYPE.BOTH // Both admin and user should receive this notification
  );

  return chat;
};

const sendMessage = async (chatId, senderId, message, isAdmin = false) => {
  // Find chat with a single query and validate it exists
  const chat = await Chat.findByPk(chatId);
  if (!chat) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
  }

  // Generate a more descriptive message ID
  const messageType = isAdmin ? 'admin' : 'user';
  const firebaseMessageId = `${messageType}_${chatId}_${Date.now()}`;
  
  // Create message and update chat in parallel
  const [chatMessage, _] = await Promise.all([
    ChatMessage.create({
      chatId,
      senderId,
      message,
      isAdmin,
      firebaseMessageId,
    }),
    
    // Update last message timestamp
    chat.update({ 
      lastMessageAt: new Date(),
      status: 'active'
    })
  ]);

  // Determine which topic to send to based on sender
  const notificationTopic = isAdmin ? chat.firebaseTopicId : 'admin_support_channel';

  // Send notification to the appropriate topic
  await sendToTopic(
    notificationTopic,
    {
      title: isAdmin ? 'Support Response' : 'New Message',
      body: message.length > 100 ? message.substring(0, 97) + '...' : message
    }, 
    {
      chatId: chatId.toString(),
      messageId: chatMessage.id.toString(),
      senderId: senderId.toString(),
      isAdmin: isAdmin.toString(),
      timestamp: Date.now().toString(),
      userId: chat.userId.toString(),
      type: isAdmin ? 'admin_message' : 'user_message',
      referenceId: chatId,
      referenceType: 'chat'
    },
    isAdmin ?  RECIPIENT_TYPE.USER  : RECIPIENT_TYPE.ADMIN // Send to user if admin is sending, send to admin if user is sending
  );

  return chatMessage;
};

const getChatMessages = async (chatId, userId, page = 1, limit = 20, isAdmin = false) => {
  // If user is not an admin, verify chat ownership
  if (!isAdmin) {
    const chat = await Chat.findOne({
      where: { id: chatId, userId },
    });

    if (!chat) {
      throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to access this chat.');
    }
  } else {
    // For admin, just verify chat exists
    const chat = await Chat.findByPk(chatId);
    if (!chat) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
    }
  }

   const messages = await ChatMessage.findAndCountAll({
    where: { chatId },
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'name', 'profilePicUrl'],
        required: false,
      },
      {
        model: Admin,
        as: 'adminSender',
        attributes: ['id', 'name'],
        required: false,
      }
    ],
    order: [['createdAt', 'DESC']],
    limit,
    offset: (page - 1) * limit,
  });

  // Transform the messages to include the correct sender based on isAdmin flag
  const transformedMessages = messages.rows.map(msg => {
    const sender = msg.isAdmin ? msg.adminSender : msg.sender;
    return {
      ...msg.toJSON(),
      sender: sender || null
    };
  });

  return {
    messages: transformedMessages,
    totalPages: Math.ceil(messages.count / limit),
    currentPage: page,
  };
};

const closeChat = async (chatId, adminId) => {
  // Verify the user is an admin
  const admin = await User.findOne({
    where: { 
      id: adminId,
      // isAdmin: true  // Assuming you have an isAdmin field in your User model
    }
  });
  
  if (!admin) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Only administrators can close chats');
  }

  // Find the chat
  const chat = await Chat.findByPk(chatId);
  
  if (!chat) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Chat not found');
  }

  // Check if chat is already closed
  if (chat.status === 'closed') {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Chat is already closed');
  }

  // Add a system message about chat closure
  await ChatMessage.create({
    chatId,
    senderId: adminId,
    message: 'Chat closed by support',
    isSystem: true,
    isAdmin: true,
    firebaseMessageId: `system_${chatId}_${Date.now()}`
  });

  // Update chat status
  await chat.update({ status: 'closed' });

  // Send notification to the chat topic (both admin and user will receive)
  await sendToTopic(
    chat.firebaseTopicId,
    {
      title: 'Chat Closed',
      body: 'This support conversation has been closed'
    }, 
    {
      chatId: chatId.toString(),
      status: 'closed',
      timestamp: Date.now().toString(),
      type: 'chat_closed',
      userId: chat.userId.toString(),
      referenceId: chatId,
      referenceType: 'chat'
    },
    RECIPIENT_TYPE.BOTH // Both admin and user should receive this notification
  );

  return chat;
};

module.exports = {
  createChat,
  sendMessage,
  getChatMessages,
  closeChat,
};