const Joi = require('joi');

const createPolicy = {
  body: Joi.object().keys({
    type: Joi.string()
      .required()
      .valid('privacy', 'subscription', 'penalty', 'cancellation', 'terms')
      .description('Policy type'),
    userType: Joi.string()
      .required()
      .valid('all', 'user', 'vendor')
      .description('Target user type'),
    title: Joi.string().required().description('Policy title'),
    description: Joi.string().required().description('Policy content/description'),
    isActive: Joi.boolean().default(true).description('Whether the policy is active')
  })
};

const getPolicies = {
  query: Joi.object().keys({
    type: Joi.string()
      .valid('privacy', 'subscription', 'penalty', 'cancellation', 'terms')
      .description('Filter by policy type'),
    userType: Joi.string()
      .valid('all', 'user', 'vendor')
      .description('Filter by target user type'),
    page: Joi.number().integer().min(1).default(1).description('Page number'),
    limit: Joi.number().integer().min(1).max(100).default(10).description('Number of items per page'),
    sortBy: Joi.string().valid('createdAt', 'updatedAt', 'title').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  })
};

const getPolicy = {
  params: Joi.object().keys({
    policyId: Joi.number().integer().required().description('Policy ID')
  })
};

const updatePolicy = {
  params: Joi.object().keys({
    policyId: Joi.number().integer().required().description('Policy ID')
  }),
  body: Joi.object().keys({
    title: Joi.string().description('Policy title'),
    description: Joi.string().description('Policy content/description'),
    type: Joi.string()
      .required()
      .valid('privacy', 'subscription', 'penalty', 'cancellation', 'terms')
      .description('Policy type'),
    userType: Joi.string()
      .required()
      .valid('all', 'user', 'vendor')
      .description('Target user type')
  }).min(1)
};

const togglePolicyStatus = {
  params: Joi.object().keys({
    policyId: Joi.number().integer().required().description('Policy ID')
  })
};

const deletePolicy = {
  params: Joi.object().keys({
    policyId: Joi.number().integer().required().description('Policy ID')
  })
};

module.exports = {
  createPolicy,
  getPolicies,
  getPolicy,
  updatePolicy,
  togglePolicyStatus,
  deletePolicy
};