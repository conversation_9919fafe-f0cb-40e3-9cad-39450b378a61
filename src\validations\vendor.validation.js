const Joi = require('joi');

const addVendor = {
  body: Joi.object().keys({
    businessName: Joi.string().required(),
    email: Joi.string().email().required(),
    phone: Joi.string().required(),
  })
};

const setupVendorProfile = {
  body: Joi.object().keys({
    stationName: Joi.string().required(),
    stationAddress: Joi.string().required(),
    country: Joi.string().required(),
  })
};

const getVendorStations = {
  query: Joi.object({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
  }),
};

const updateVendor = {
  body: Joi.object().keys({
    businessName: Joi.string().min(3).max(100),
    stationName: Joi.string().min(3).max(100),
    stationAddress: Joi.string().min(3).max(100),
    country:  Joi.string().min(3).max(100),
  }).min(1) // Require at least one field to be present
};

module.exports = {
  addVendor,
  setupVendorProfile,
  getVendorStations,
  updateVendor
}; 