'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.removeConstraint('chat_messages', 'chat_messages_senderId_fkey'); 
   
    await queryInterface.changeColumn('chat_messages', 'senderId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      comment: 'ID of sender (user or admin based on isAdmin flag)',
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.changeColumn('chat_messages', 'senderId', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });

    await queryInterface.addConstraint('chat_messages', {
      fields: ['senderId'],
      type: 'foreign key',
      name: 'chat_messages_senderId_fkey', // name it explicitly
      references: {
        table: 'users',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  }
};
