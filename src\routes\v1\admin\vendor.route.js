const express = require('express');
const router = express.Router();
const {adminAuth, superAdminAuth} = require('../../../middlewares/adminAuth');
const vendorController = require('../../../controllers/admin/vendor.controller');
const validate = require('../../../middlewares/validate');
const vendorValidation = require('../../../validations/admin/vendor.validation');
const userController = require('../../../controllers/admin/user.controller');

//get Vendors
router.route('/').get(adminAuth, validate(vendorValidation.getVendors), vendorController.getVendors);


/*
 * @api {get} v1/admin/vendors/:userId Get vendor by ID
 * @apiDescription Get vendor details by user ID
 * @apiName GetVendorById
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {Number} userId User's unique ID
 *
 * @apiSuccess {Object} data Vendor details
 */
router.route('/:userId').get(adminAuth, validate(vendorValidation.getVendorById), vendorController.getVendorById);

/*
 * @api {get} v1/admin/vendors/:userId/business Get vendor business details
 * @apiDescription Get vendor business details by user ID
 * @apiName GetVendorBusiness
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {Number} userId User's unique ID
 *
 * @apiSuccess {Object} data Vendor business details
 */
router.route('/:userId/business').get(adminAuth, validate(vendorValidation.getVendorBusiness), vendorController.getVendorBusiness);

//get Vendor Stations
router
  .route('/:userId/stations')
  .get(adminAuth, validate(vendorValidation.getVendorStations), vendorController.getVendorStations);

//get station details
router
  .route('/stations/:stationId')
  .get(adminAuth, validate(vendorValidation.getStationDetails), vendorController.getStationDetails);

// Create User
router.route('/').post(superAdminAuth, validate(vendorValidation.createVendor), vendorController.createVendor);

//Add charger to station
router.route('/stations/:stationId/chargers').post(adminAuth, validate(vendorValidation.addChargers), vendorController.addChargerToStation);

//Delete vendor
router.route('/:userId').delete(adminAuth, validate(vendorValidation.deleteUser), vendorController.deleteVendor);

//reset password
router.route('/reset-password').patch(adminAuth, validate(vendorValidation.resetPassword), vendorController.resetPassword);
/**
 * @api {get} v1/admin/vendors/stats Get vendor statistics
 * @apiDescription Get statistics about vendors including total count, new vendors, free/paid vendors with YoY comparison
 * @apiName GetVendorStats
 * @apiGroup AdminVendor
 * @apiPermission admin
 *
 * @apiSuccess {Object} data Vendor statistics
 * @apiSuccess {Object} data.totalVendors Total vendors count and growth
 * @apiSuccess {Object} data.newVendors New vendors count and growth
 * @apiSuccess {Object} data.freeVendors Free vendors count and growth
 * @apiSuccess {Object} data.paidVendors Paid vendors count and growth
 */
router.route('/stats').get(adminAuth, vendorController.getVendorStats);

//toggle-status
router
  .route('/:userId/toggle-status')
  .patch(adminAuth, validate(vendorValidation.toggleVendorStatus), vendorController.toggleVendorStatus);
module.exports = router;
