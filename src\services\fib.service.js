const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { walletService } = require('./index');
const config = require('../config/config');
const WalletTransaction = require('../models/walletTransaction.model');
const { WALLET_TRANSACTION_TYPE, WALLET_TRANSACTION_STATUS } = require('../constants');
const logger = require('../config/logger');
const { PaymentSDK } = require('@first-iraqi-bank/sdk/payment');

// FIB SDK client initialization
const clientId = process.env.FIB_CLIENT_ID || 'your_fib_client_id';
const clientSecret = process.env.FIB_CLIENT_SECRET || 'your_fib_client_secret';
const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';

// Initialize the FIB SDK client
const FIB = PaymentSDK.getClient(clientId, clientSecret, environment);

/**
 * Initialize a FIB payment for wallet top-up
 * @param {Object} paymentData - Payment data
 * @param {number} paymentData.amount - Amount to pay (in IQD)
 * @param {number} paymentData.walletId - Wallet ID to credit
 * @param {string} paymentData.userId - User ID making the payment
 * @returns {Promise<Object>} - Payment URL and transaction ID
 */
const initializePayment = async (paymentData) => {
  const { amount, walletId, userId } = paymentData;

  logger.info('Initializing FIB payment', { amount, walletId, userId });

  if (!amount || amount < 250) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be at least 250 IQD');
  }

  if (!walletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Wallet ID is required');
  }

  // Verify wallet ownership
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to top up this wallet');
  }

  // Generate a unique order ID (combination of wallet ID and timestamp)
  const orderId = `wallet_${walletId}_${Date.now()}`;

  try {
    // Authenticate with FIB
    logger.info('Attempting FIB authentication', { clientId, environment });
    const authRes = await FIB.authenticate();
    
    if (!authRes.ok) {
      const errorData = await authRes.json();
      logger.error('FIB authentication failed', { error: errorData });
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `FIB authentication failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
      );
    }

    const authData = await authRes.json();
    const { access_token } = authData;


    const paymentInput = {
      monetaryValue: {
        amount: amount.toFixed(2), // Ensure 2 decimal places
        currency: "IQD"
      },
      statusCallbackUrl: new URL(`${config.clientUrl}v1/payments/wallet/fib/callback`).toString(),
      // statusCallbackUrl: "https://webhook.site/06606600-0000-4000-8000-000000000000",
      description: `Wallet top-up for wallet ID: ${walletId}`,
      expiresIn: "PT47H5M12.345S",
      category: "POS",
      refundableFor: "PT24H"
    };

    // Create payment using direct API call
    const apiUrl = 'https://fib.dev.fib.iq/protected/v1/payments';
    const paymentRes = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(paymentInput)
    });

    if (!paymentRes.ok) {
      const errorData = await paymentRes.json();
      logger.error('FIB payment creation failed', { error: errorData });
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `FIB payment creation failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
      );
    }

    const paymentData = await paymentRes.json();

    // Create a pending transaction record
    await WalletTransaction.create({
      walletId,
      userId,
      amount,
      balanceAfter: 0,
      type: WALLET_TRANSACTION_TYPE.TOPUP,
      status: WALLET_TRANSACTION_STATUS.PENDING,
      referenceId: paymentData.paymentId,
      metadata: {
        paymentProvider: 'fib',
        orderId:paymentData.paymentId,
        fibPaymentId: paymentData.paymentId
      }
    });

    logger.info(`FIB payment initialized - paymentId: ${paymentData.paymentId}, orderId: ${orderId}, amount: ${amount}`);

    // Return the payment details
    return {
      paymentUrl: paymentData.personalAppLink, // Use the personal app link as the primary payment URL
      qrCode: paymentData.qrCode, // QR code for scanning
      readableCode: paymentData.readableCode, // Code for manual entry
      personalAppLink: paymentData.personalAppLink, // Deep link for personal FIB app
      businessAppLink: paymentData.businessAppLink, // Deep link for business FIB app
      corporateAppLink: paymentData.corporateAppLink, // Deep link for corporate FIB app
      validUntil: paymentData.validUntil, // Expiration time
      transactionId: paymentData.paymentId,
      amount,
      walletId
    };
  } catch (error) {
    logger.error('Error initializing FIB payment', { error: error.message, walletId, amount });
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      httpStatus.BAD_GATEWAY,
      `FIB payment initialization failed: ${error.message}`
    );
  }
};

/**
 * Verify a FIB payment callback
 * @param {Object} callbackData - Callback data from FIB
 * @param {string} callbackData.paymentId - Payment ID
 * @param {string} callbackData.status - Payment status
 * @returns {Promise<Object>} - Verified payment data
 */
const verifyPayment = async (callbackData) => {
  if (!callbackData || !callbackData.paymentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid callback data');
  }

  const { paymentId } = callbackData;
  logger.info('Verifying FIB payment', { paymentId });

  // If we received status directly in the callback, we can use it instead of fetching
  // This is useful for the webhook callback which includes the full status object
  const statusFromCallback = callbackData.status;

  try {
    // If we have status from the callback, use it directly
    let statusData;

    if (statusFromCallback) {
      logger.info('Using status from callback', { paymentId, status: statusFromCallback });
      statusData = statusFromCallback;
    } else {
      // Otherwise, fetch the status from FIB API
      logger.info('Fetching status from FIB API', { paymentId });

      // Authenticate with FIB
      logger.info('Attempting FIB authentication for payment verification', { clientId, environment });
      const authRes = await FIB.authenticate();
      
      if (!authRes.ok) {
        const errorData = await authRes.json();
        logger.error('FIB authentication failed', { error: errorData });
        throw new ApiError(
          httpStatus.BAD_GATEWAY,
          `FIB authentication failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
        );
      }

      const authData = await authRes.json();
      const { access_token } = authData;

      // Get payment status from FIB
      logger.info('Fetching payment status from FIB', { paymentId });
      const statusRes = await FIB.getPaymentStatus(paymentId, access_token);

      if (!statusRes.ok) {
        const errorData = await statusRes.json();
        logger.error('FIB payment status check failed', { error: errorData });
        throw new ApiError(
          httpStatus.BAD_GATEWAY,
          `FIB payment status check failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
        );
      }

      statusData = await statusRes.json();
      logger.info('Payment status retrieved', { paymentId, status: statusData.status });
    }

    // Find the pending transaction
    const pendingTransaction = await WalletTransaction.findOne({
      where: {
        referenceId: paymentId,
        status: WALLET_TRANSACTION_STATUS.PENDING
      }
    });

    if (!pendingTransaction) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Original transaction not found');
    }

    // Check payment status
    if (statusData.status !== 'PAID') {
      // Update transaction for failed payment
      await pendingTransaction.update({
        status: WALLET_TRANSACTION_STATUS.FAILED,
        description: `FIB payment failed: ${statusData.decliningReason || 'Unknown reason'}`,
        metadata: {
          ...pendingTransaction.metadata,
          paymentStatus: statusData.status,
          decliningReason: statusData.decliningReason,
          declinedAt: statusData.declinedAt
        },
        processedAt: new Date()
      });

      throw new ApiError(
        httpStatus.PAYMENT_REQUIRED,
        `Payment failed: ${statusData.decliningReason || 'Unknown reason'}`
      );
    }

    // Use walletService to handle the successful top-up
    const result = await walletService.topUpWallet(
      pendingTransaction.walletId,
      pendingTransaction.amount,
      {
        paymentMethod: 'fib',
        paymentId: paymentId,
        paidAt: statusData.paidAt,
        paidBy: statusData.paidBy,
        paymentStatus: WALLET_TRANSACTION_STATUS.COMPLETED
      },
      pendingTransaction.referenceId
    );

    return {
      success: true,
      walletId: pendingTransaction.walletId,
      amount: pendingTransaction.amount,
      transactionId: paymentId,
      orderId: pendingTransaction.referenceId,
      wallet: result.wallet,
      transaction: result.transaction
    };
  } catch (error) {
    logger.error('Error verifying FIB payment', { error: error.message, paymentId });
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to verify FIB payment: ${error.message}`
    );
  }
};

module.exports = {
  initializePayment,
  verifyPayment
};
