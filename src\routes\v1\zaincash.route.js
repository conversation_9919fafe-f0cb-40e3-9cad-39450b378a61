const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const { zaincashController } = require('../../controllers');
const zaincashValidation = require('../../validations/zaincash.validation');

// Initialize ZainCash payment
router.post(
  '/initialize',
  // firebaseAuth,
  validate(zaincashValidation.initializePayment),
  zaincashController.initializePayment
);

// Handle ZainCash payment callback (no auth required as this is called by ZainCash)
router.get(
  '/callback',
  validate(zaincashValidation.handlePaymentCallback),
  zaincashController.handlePaymentCallback
);

module.exports = router;
