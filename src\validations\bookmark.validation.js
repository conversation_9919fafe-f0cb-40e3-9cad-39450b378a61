const Joi = require('joi');

const getBookmarks = {
    query: Joi.object().keys({
        page: Joi.number().integer().min(1).optional().default(1),
        limit: Joi.number().integer().min(1).max(100).optional().default(10),
    }),
};

const addBookmark  = {
    params: Joi.object().keys({
        stationId: Joi.number().integer().required(),
    }),
};
const removeBookmark = {
    params: Joi.object().keys({
        stationId: Joi.number().integer().required(),
    }),
};
module.exports = {
    getBookmarks,
    addBookmark,
    removeBookmark
};