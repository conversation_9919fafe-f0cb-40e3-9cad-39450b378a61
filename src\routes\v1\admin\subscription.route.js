const express = require('express');
const router = express.Router();
const { adminAuth } = require('../../../middlewares/adminAuth');
const validate = require('../../../middlewares/validate');
const subscriptionValidation = require('../../../validations/subscription.validation');
const subscriptionController = require('../../../controllers/admin/subscription.controller');

// Get all subscription plans (including inactive ones)
router.get(
  '/plans',
  adminAuth,
  validate(subscriptionValidation.getSubscriptionPlans),
  subscriptionController.getSubscriptionPlans
);

// Add new subscription plan
router.post(
  '/plans',
  adminAuth,
  validate(subscriptionValidation.addSubscriptionPlan),
  subscriptionController.addSubscriptionPlan
);

// // Update subscription plan
// router.put(
//   '/plans/:planId',
//   adminAuth,
//   validate(subscriptionValidation.updateSubscriptionPlan),
//   subscriptionController.updateSubscriptionPlan
// );

// // Delete subscription plan
router.delete(
  '/plans/:planId',
  adminAuth,
  validate(subscriptionValidation.deleteSubscriptionPlan),
  subscriptionController.deleteSubscriptionPlan
);

//toggle plan status
router.patch(
  '/plans/:planId/toggle-status',
  adminAuth,
  validate(subscriptionValidation.togglePlanStatus),
  subscriptionController.togglePlanStatus
);


// Get vendors by subscription plan ID
router.get(
  '/plans/:planId/vendors',
  adminAuth,
  validate(subscriptionValidation.getVendorsByPlanId),
  subscriptionController.getVendorsByPlanId
);

module.exports = router; 
