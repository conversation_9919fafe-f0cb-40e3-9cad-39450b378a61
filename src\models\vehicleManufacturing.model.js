const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');


const ManufacturingUnit = sequelize.define(
    'ManufacturingUnit',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      location: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      tableName: 'manufacturing_units',
      timestamps: false,
    }
  );
  
  ManufacturingUnit.paginate = paginate();

  module.exports = ManufacturingUnit;