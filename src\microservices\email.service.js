const NotifmeSdk = require('notifme-sdk').default;
const config = require('../config/config');
const logger = require('../config/logger');

// Initialize Notifme SDK with SMTP configuration
const notifme = new NotifmeSdk({
  channels: {
    email: {
      providers: [{
        type: 'smtp',
        host: config.email.host,
        port: config.email.port,
        secure: config.email.secure,
        auth: {
          user: config.email.user,
          pass: config.email.password
        }
      }]
    }
  }
});

/**
 * Strips HTML tags to generate a plain text version of the email.
 * @param {string} html - HTML content
 * @returns {string} - Plain text
 */
function stripHtml(html) {
  return html.replace(/<[^>]*>?/gm, '')
             .replace(/&nbsp;/g, ' ')
             .replace(/\s+/g, ' ')
             .trim();
}

/**
 * Validates required email fields.
 * @param {Object} emailData
 */
function validateEmailData(emailData) {
  if (!emailData.to || !emailData.subject || !emailData.html) {
    throw new Error('Email "to", "subject", and "html" fields are required.');
  }
}

/**
 * Sends an email using the Notifme SDK.
 * @param {Object} emailData
 * @returns {Promise<Object>}
 */
async function sendEmail(emailData) {
  validateEmailData(emailData);

  const payload = {
    email: {
      from: emailData.from || config.email.from,
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
      text: emailData.text || stripHtml(emailData.html)
    }
  };

  try {
    logger.info(`Sending email → To: ${emailData.to}, Subject: "${emailData.subject}"`);
    
    const result = await notifme.send(payload);

    logger.info(`Email sent → To: ${emailData.to}, Response: ${JSON.stringify(result)}`);
    return result;
  } catch (error) {
    logger.error(`Failed to send email → To: ${emailData.to}, Error: ${error.message}`, { error });
    throw new Error('Email sending failed');
  }
}

module.exports = {
  sendEmail
};
