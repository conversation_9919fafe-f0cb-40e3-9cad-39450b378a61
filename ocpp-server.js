require('dotenv').config();
const WebSocket = require('ws');
const express = require('express');
const { Charger, sequelize } = require('./models');
const rateLimit = require('express-rate-limit');
const { Op } = require('sequelize');
const { getUserIdFromIdTag } = require('./utils/userUtils');

// Import Services
const SessionManager = require('./services/SessionManager');
const MeterDataService = require('./services/MeterDataService');
const ChargerStateManager = require('./services/ChargerStateManager');

// Import additional models
const { ChargingSession, ChargerConnector, MeterValue, SessionEvent, Transaction } = require('./models');

// Initialize Services
const sessionManager = new SessionManager();
const meterDataService = new MeterDataService();
const chargerStateManager = new ChargerStateManager();

// OCPP Message Types
const MESSAGE_TYPES = {
    CALL: 2,
    CALLRESULT: 3,
    CALLERROR: 4
};

// OCPP Actions
const ACTIONS = {
    BOOT_NOTIFICATION: "BootNotification",
    HEARTBEAT: "Heartbeat",
    STATUS_NOTIFICATION: "StatusNotification",
    METER_VALUES: "MeterValues",
    START_TRANSACTION: "StartTransaction",
    STOP_TRANSACTION: "StopTransaction",
    REMOTE_START_TRANSACTION: "RemoteStartTransaction",
    REMOTE_STOP_TRANSACTION: "RemoteStopTransaction",
    DATA_TRANSFER: "DataTransfer",
    RESET: "Reset",
    UNLOCK_CONNECTOR: "UnlockConnector",
    CHANGE_AVAILABILITY: "ChangeAvailability",
    CHANGE_CONFIGURATION: "ChangeConfiguration",
    GET_CONFIGURATION: "GetConfiguration",
    DIAGNOSTICS_STATUS_NOTIFICATION: "DiagnosticsStatusNotification",
    FIRMWARE_STATUS_NOTIFICATION: "FirmwareStatusNotification"
};

const app = express();
const PORT = process.env.PORT || 8080;

// Rate limiting middleware
const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
});

app.use(limiter);
app.use(express.json());

// Basic authentication middleware
const basicAuth = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        return res.status(401).json({ error: 'Authentication required' });
    }
    const auth = Buffer.from(authHeader.split(' ')[1], 'base64').toString().split(':');
    const user = auth[0];
    const pass = auth[1];

    // Replace with proper authentication
    if (user === process.env.ADMIN_USER && pass === process.env.ADMIN_PASS) {
        next();
    } else {
        res.status(401).json({ error: 'Invalid credentials' });
    }
};

// Initialize database and start server
async function startServer() {
    try {
        await sequelize.sync();
        console.log('Database synchronized successfully');

        const wss = new WebSocket.Server({ port: PORT }, () => {
            console.log(`OCPP Server running on ws://localhost:${PORT}`);
        });
        wss.on('connection', (ws, req) => {
            console.log("New connection established.");

            // Extract chargePointId from the URL
            const url = new URL(req.url, `ws://${req.headers.host}`);
            console.log("url",url);
            const chargePointId = url.pathname.split('/')[1];
            console.log(`ChargePoint ID: ${chargePointId}`);

            ws.on('message', async (message) => {
                console.log("Received:", message);

                let parsedMessage;
                try {
                    parsedMessage = JSON.parse(message);
                    console.log("Parsed message:", parsedMessage);
                    console.log("meterValues:", parsedMessage[3].meterValue);
                    if(parsedMessage[3].meterValue && parsedMessage[3].meterValue.length > 0)
                    console.log("sampleValue:", parsedMessage[3].meterValue[0].sampledValue);


                    // Validate message format
                    if (!Array.isArray(parsedMessage) || parsedMessage.length < 3) {
                        throw new Error('Invalid message format');
                    }

                    const [messageTypeId, messageId, action, payload] = parsedMessage;

                    if (messageTypeId === MESSAGE_TYPES.CALL) {
                        await handleOCPPMessage(ws,chargePointId, messageId, action, payload);
                    } else {
                        console.warn(`Unsupported message type: ${messageTypeId}`);
                        sendErrorResponse(ws, messageId, "NotSupported", `Message type ${messageTypeId} is not supported`);
                    }
                } catch (error) {
                    console.error("Error processing message:", error);
                    // If we have a messageId, send an error response
                    if (parsedMessage && parsedMessage[1]) {
                        sendErrorResponse(ws, parsedMessage[1], "MessageProcessingError", error.message);
                    }
                }
            });

            ws.on('close', () => console.log("Charger disconnected."));
            ws.on('error', (err) => console.error("WebSocket error:", err));
        });
    } catch (error) {
        console.error('Error starting server:', error);
    }
}

// Handle OCPP Messages
async function handleOCPPMessage(ws, chargePointId, messageId, action, payload) {
    console.log(`Handling action: ${action} for charger: ${chargePointId}`);

    try {
        switch (action) {
            case ACTIONS.BOOT_NOTIFICATION:
                console.log('Boot Notification:', payload);

                if (!payload.chargeBoxSerialNumber && !payload.chargePointSerialNumber) {
                    throw new Error('Missing chargeBoxSerialNumber in BootNotification');
                }

                // Register charger using ChargerStateManager
                const registrationResult = await chargerStateManager.registerCharger(chargePointId, payload);

                if (registrationResult.success) {
                    sendResponse(ws, messageId, {
                        status: "Accepted",
                        currentTime: new Date().toISOString(),
                        interval: 300
                    });
                } else {
                    sendResponse(ws, messageId, {
                        status: "Rejected",
                        currentTime: new Date().toISOString(),
                        interval: 300
                    });
                }
                break;

            case ACTIONS.HEARTBEAT:
                console.log(`Heartbeat from ${chargePointId}`);

                const heartbeatResult = await chargerStateManager.updateHeartbeat(chargePointId);

                sendResponse(ws, messageId, {
                    currentTime: heartbeatResult.currentTime || new Date().toISOString()
                });
                break;

            case ACTIONS.STATUS_NOTIFICATION:
                console.log('Status Notification:', payload);

                const { connectorId, status, errorCode, info } = payload;

                await chargerStateManager.updateConnectorStatus(
                    chargePointId,
                    connectorId,
                    status,
                    errorCode,
                    info
                );

                sendResponse(ws, messageId, { status: "Accepted" });
                break;

            case ACTIONS.METER_VALUES:
                console.log('Meter Values:', payload);

                if (!payload?.connectorId || !payload?.meterValue) {
                    throw new Error('Missing required fields in MeterValues');
                }

                // Find active session for this connector
                const activeSession = await findActiveSessionForConnector(chargePointId, payload.connectorId);

                if (activeSession) {
                    // Get charger ID
                    const charger = await Charger.findOne({ where: { chargerId: chargePointId } });

                    if (charger) {
                        await meterDataService.processMeterValues(payload, activeSession.id, charger.id);
                    }
                }

                sendResponse(ws, messageId, { status: "Accepted" });
                break;

            case ACTIONS.START_TRANSACTION:
                console.log('Start Transaction:', payload);

                if (!payload?.connectorId || !payload?.idTag) {
                    throw new Error('Missing required fields in StartTransaction');
                }

                try {
                    const sessionResult = await sessionManager.startSession({
                        chargePointId,
                        connectorId: payload.connectorId,
                        idTag: payload.idTag,
                        meterStart: payload.meterStart || 0,
                        userId: getUserIdFromIdTag(payload.idTag)
                    });

                    if (sessionResult.success) {
                        sendResponse(ws, messageId, {
                            transactionId: sessionResult.transactionId,
                            idTagInfo: { status: "Accepted" }
                        });
                    } else {
                        sendResponse(ws, messageId, {
                            transactionId: 0,
                            idTagInfo: { status: "Rejected" }
                        });
                    }
                } catch (error) {
                    console.error('Error starting transaction:', error);
                    sendResponse(ws, messageId, {
                        transactionId: 0,
                        idTagInfo: { status: "Rejected" }
                    });
                }
                break;

            case ACTIONS.STOP_TRANSACTION:
                console.log('Stop Transaction:', payload);

                if (!payload?.transactionId) {
                    throw new Error('Missing required fields in StopTransaction');
                }

                try {
                    const stopResult = await sessionManager.stopSession({
                        transactionId: payload.transactionId,
                        meterStop: payload.meterStop || 0,
                        stopReason: payload.reason || 'Local',
                        transactionData: payload.transactionData || []
                    });

                    if (stopResult.success) {
                        sendResponse(ws, messageId, {
                            idTagInfo: { status: "Accepted" }
                        });
                    } else {
                        sendResponse(ws, messageId, {
                            idTagInfo: { status: "Rejected" }
                        });
                    }
                } catch (error) {
                    console.error('Error stopping transaction:', error);
                    sendResponse(ws, messageId, {
                        idTagInfo: { status: "Rejected" }
                    });
                }
                break;

            case ACTIONS.DATA_TRANSFER:
                console.log("Data Transfer:", payload);
                sendResponse(ws, messageId, {
                    status: "Accepted",
                    data: payload.data
                });
                break;

            default:
                sendErrorResponse(ws, messageId, "NotImplemented", `Action ${action} is not implemented`);
        }
    } catch (error) {
        console.error(`Error handling ${action}:`, error);
        sendErrorResponse(ws, messageId, "InternalError", error.message);
    }
}

// Helper function to find active session for a connector
async function findActiveSessionForConnector(chargePointId, connectorId) {
    try {
        const charger = await Charger.findOne({
            where: { chargerId: chargePointId }
        });

        if (!charger) {
            return null;
        }

        const connector = await ChargerConnector.findOne({
            where: {
                chargerId: charger.id,
                connectorNumber: connectorId
            }
        });

        if (!connector) {
            return null;
        }

        const activeSession = await ChargingSession.findOne({
            where: {
                connectorId: connector.id,
                status: {
                    [Op.in]: ['Starting', 'Active', 'Suspended']
                }
            },
            order: [['startTime', 'DESC']]
        });

        return activeSession;

    } catch (error) {
        console.error('Error finding active session:', error);
        return null;
    }
}

// ==================== API ENDPOINTS ====================

// Get all chargers with their status
app.get('/api/chargers', async (req, res) => {
    try {
        const onlineOnly = req.query.online === 'true';
        const chargers = await chargerStateManager.getAllChargers(onlineOnly);
        res.json({ success: true, data: chargers });
    } catch (error) {
        console.error('Error getting chargers:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get specific charger status
app.get('/api/chargers/:chargePointId', async (req, res) => {
    try {
        const { chargePointId } = req.params;
        const result = await chargerStateManager.getChargerStatus(chargePointId);

        if (result.success) {
            res.json({ success: true, data: result.charger });
        } else {
            res.status(404).json({ success: false, error: result.error });
        }
    } catch (error) {
        console.error('Error getting charger status:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get active charging sessions
app.get('/api/sessions/active', async (req, res) => {
    try {
        const { chargePointId } = req.query;
        const sessions = await sessionManager.getActiveSessions(chargePointId);
        res.json({ success: true, data: sessions });
    } catch (error) {
        console.error('Error getting active sessions:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get session details
app.get('/api/sessions/:sessionId', async (req, res) => {
    try {
        const { sessionId } = req.params;

        const session = await ChargingSession.findOne({
            where: { sessionId },
            include: [
                { model: Charger, as: 'charger' },
                { model: ChargerConnector, as: 'connector' },
                { model: Transaction, as: 'transaction' },
                { model: SessionEvent, as: 'events', limit: 10, order: [['timestamp', 'DESC']] }
            ]
        });

        if (!session) {
            return res.status(404).json({ success: false, error: 'Session not found' });
        }

        res.json({ success: true, data: session });
    } catch (error) {
        console.error('Error getting session details:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get meter values for a session
app.get('/api/sessions/:sessionId/meter-values', async (req, res) => {
    try {
        const { sessionId } = req.params;
        const { measurand, startTime, endTime } = req.query;

        const session = await ChargingSession.findOne({
            where: { sessionId }
        });

        if (!session) {
            return res.status(404).json({ success: false, error: 'Session not found' });
        }

        const meterValues = await meterDataService.getSessionMeterValues(
            session.id,
            measurand,
            startTime ? new Date(startTime) : null,
            endTime ? new Date(endTime) : null
        );

        res.json({ success: true, data: meterValues });
    } catch (error) {
        console.error('Error getting meter values:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Get charger statistics
app.get('/api/statistics', async (req, res) => {
    try {
        const { chargePointId } = req.query;
        const stats = await chargerStateManager.getChargerStatistics(chargePointId);
        res.json({ success: true, data: stats });
    } catch (error) {
        console.error('Error getting statistics:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Remote start transaction
app.post('/api/chargers/:chargePointId/start', async (req, res) => {
    try {
        const { chargePointId } = req.params;
        const { connectorId, idTag } = req.body;

        if (!connectorId || !idTag) {
            return res.status(400).json({
                success: false,
                error: 'connectorId and idTag are required'
            });
        }

        // This would typically send a RemoteStartTransaction command to the charger
        // For now, we'll just return a success response
        res.json({
            success: true,
            message: 'Remote start command would be sent to charger',
            chargePointId,
            connectorId,
            idTag
        });
    } catch (error) {
        console.error('Error starting remote transaction:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Remote stop transaction
app.post('/api/transactions/:transactionId/stop', async (req, res) => {
    try {
        const { transactionId } = req.params;

        // This would typically send a RemoteStopTransaction command to the charger
        // For now, we'll just return a success response
        res.json({
            success: true,
            message: 'Remote stop command would be sent to charger',
            transactionId
        });
    } catch (error) {
        console.error('Error stopping remote transaction:', error);
        res.status(500).json({ success: false, error: error.message });
    }
});

// Send Response to Charger
function sendResponse(ws, messageId, payload) {
    ws.send(JSON.stringify([3, messageId, payload]));
}

// Send Error Response
function sendErrorResponse(ws, messageId, errorCode, errorMessage) {
    ws.send(JSON.stringify([4, messageId, errorCode, errorMessage]));
}

startServer();
