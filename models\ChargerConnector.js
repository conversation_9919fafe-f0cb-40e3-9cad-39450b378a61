const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ChargerConnector = sequelize.define(
    'ChargerConnector',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      chargerId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'chargers',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      connectorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'connectors',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      connectorNumber: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 1,
        comment: 'Connector number as per OCPP (starts from 1)'
      },
      maxPower: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Maximum power in kW'
      },
      pricePerKwh: {
        type: DataTypes.DECIMAL(10, 4),
        allowNull: true,
        comment: 'Price per kWh'
      },
      pricePerMinute: {
        type: DataTypes.DECIMAL(10, 4),
        allowNull: true,
        comment: 'Price per minute'
      },
      status: {
        type: DataTypes.ENUM(
          'Available',
          'Preparing',
          'Charging',
          'SuspendedEVSE',
          'SuspendedEV',
          'Finishing',
          'Reserved',
          'Unavailable',
          'Faulted'
        ),
        allowNull: false,
        defaultValue: 'Available'
      },
      errorCode: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'OCPP error code if faulted'
      },
      info: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Additional status information'
      },
      lastStatusUpdate: {
        type: DataTypes.DATE,
        allowNull: true,
      }
    },
    {
      tableName: 'charger_connectors',
      timestamps: true,
    }
  );

module.exports = ChargerConnector;

