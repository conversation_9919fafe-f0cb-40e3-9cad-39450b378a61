const moment = require('moment');
const { PaymentTransaction, Station, Vendor, User,Booking } = require('../../models');
const {TRANSACTION_TYPE,PAYMENT_STATUS,STATION_APPROVAL_STATUS,BOOKING_STATUS} = require('../../constants');
const { Op, fn, col } = require('sequelize');
const { getYearStartAndEnd, getGrowthPercentage, countRecordsInYear } = require('../../utils/analytics.utils');
const { sequelize } = require('../../config/database');
const logger = require('../../config/logger');

// Sum of all successful credit transactions for a year
const calculateAnnualRevenue = async (year) => {
  const { startDate, endDate } = getYearStartAndEnd(year);
  const result = await PaymentTransaction.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('amount')), 0), 'totalRevenue']],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Get dashboard KPIs
 * @returns {Object} Dashboard KPIs
 * @description Get dashboard KPIs
 * @throws {Error} If something goes wrong while fetching dashboard KPIs
 * @returns {Object} Dashboard KPIs
 */
const getDashboardKPIs = async () => {
  const currentYear = moment.utc().year();
  const previousYear = currentYear - 1;

  const [
    currentRevenue, previousRevenue,
    currentStationCount, previousStationCount,
    currentVendorCount, previousVendorCount,
    currentUserCount, previousUserCount
  ] = await Promise.all([
    calculateAnnualRevenue(currentYear),
    calculateAnnualRevenue(previousYear),
    countRecordsInYear(Station, currentYear),
    countRecordsInYear(Station, previousYear),
    countRecordsInYear(Vendor, currentYear),
    countRecordsInYear(Vendor, previousYear),
    countRecordsInYear(User, currentYear),
    countRecordsInYear(User, previousYear)
  ]);

  return {
    revenueCollected: {
      value: currentRevenue,
      yearlyGrowth: getGrowthPercentage(currentRevenue, previousRevenue)
    },
    newStationsAdded: {
      value: currentStationCount,
      yearlyGrowth: getGrowthPercentage(currentStationCount, previousStationCount)
    },
    newVendorsAdded: {
      value: currentVendorCount,
      yearlyGrowth: getGrowthPercentage(currentVendorCount, previousVendorCount)
    },
    newUsersAdded: {
      value: currentUserCount,
      yearlyGrowth: getGrowthPercentage(currentUserCount, previousUserCount)
    }
  };
};

/**
 * Get daily data for stations, vendors, and users for the current month or year
 * @param {'month' | 'year'} filterType - Time range filter
 * @returns {Object} Overview chart data
 * @description Get daily/monthly data for stations, vendors, and users for the current month/year
 * @throws {Error} If something goes wrong while fetching overview chart data
 */
const getOverviewChartData = async (filterType = 'month') => {
  const now = moment.utc();
  const currentMonth = now.month();
  const currentYear = now.year();
  
  let startDate, endDate, groupByFormat, labels = [], timeKey;

  if (filterType === 'month') {
    // Month view - daily data for current month
    startDate = moment.utc([currentYear, currentMonth]).startOf('month').toDate();
    endDate = moment.utc([currentYear, currentMonth]).endOf('month').toDate();
    groupByFormat = 'DD'; // PostgreSQL format for day
    timeKey = 'day';
    
    // Create labels for all days in the month
    const daysInMonth = now.daysInMonth();
    labels = Array.from({ length: daysInMonth }, (_, i) => 
      (i + 1).toString().padStart(2, '0')
    );
  } else {
    // Year view - monthly data for current year
    startDate = moment.utc([currentYear, 0]).startOf('month').toDate();
    endDate = moment.utc([currentYear, 11]).endOf('month').toDate();
    groupByFormat = 'MM'; // PostgreSQL format for month
    timeKey = 'month';
    
    // Create labels for all months
    labels = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
  }
  
  // Get counts for stations, vendors, and users
  const [stationData, vendorData, userData] = await Promise.all([
    getCreationCounts(Station, startDate, endDate, groupByFormat),
    getCreationCounts(Vendor, startDate, endDate, groupByFormat),
    getCreationCounts(User, startDate, endDate, groupByFormat)
  ]);
  
  // Create a data point for each time period (day or month)
  const chartData = labels.map(label => {
    const formattedLabel = filterType === 'year' ? 
      months[parseInt(label, 10) - 1] : // Convert month number to name
      label; // Keep day as is
    
    return {
      [timeKey]: formattedLabel,
      newStations: stationData[label] || 0,
      newVendors: vendorData[label] || 0,
      newUsers: userData[label] || 0
    };
  });
  
  // Calculate totals
  const totals = chartData.reduce((acc, item) => {
    return {
      stations: acc.stations + item.newStations,
      vendors: acc.vendors + item.newVendors,
      users: acc.users + item.newUsers
    };
  }, { stations: 0, vendors: 0, users: 0 });
  
  return {
    chartData,
    totals,
    currentMonth: months[currentMonth],
    currentYear,
    filterType
  };
};

/**
 * Get creation counts for a model within a date range
 * @param {Model} Model - Sequelize model
 * @param {Date} startDate - Start date
 * @param {Date} endDate - End date
 * @param {String} groupByFormat - Format for grouping (DD for days, MM for months)
 * @returns {Object} Counts by time period
 */
const getCreationCounts = async (Model, startDate, endDate, groupByFormat) => {
  const results = await Model.findAll({
    attributes: [
      [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`), 'timePeriod'],
      [fn('COUNT', col('id')), 'count']
    ],
    where: {
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`)],
    order: [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`)],
    raw: true
  });

  // Format output to { timePeriod: count }
  return results.reduce((acc, item) => {
    acc[item.timePeriod] = parseInt(item.count, 10);
    return acc;
  }, {});
};

const months = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];

/**
 * Get revenue chart data for current month or year
 * @param {'month' | 'year'} filterType - Time range filter
 * @returns {Object} Revenue chart data
 */
const getRevenueChartData = async (filterType = 'year') => {
  const now = moment.utc();

  let startDate, endDate, groupByFormat, labels = [], timeKey;

  if (filterType === 'month') {
    startDate = now.clone().startOf('month').toDate();
    endDate = now.clone().endOf('month').toDate();
    groupByFormat = 'DD'; // PostgreSQL format for day
    timeKey = 'day';
    const daysInMonth = now.daysInMonth();
    labels = Array.from({ length: daysInMonth }, (_, i) =>
      (i + 1).toString().padStart(2, '0')
    );
  } else {
    startDate = now.clone().startOf('year').toDate();
    endDate = now.clone().endOf('year').toDate();
    groupByFormat = 'MM'; // PostgreSQL format for month
    timeKey = 'month';
    labels = ['01','02','03','04','05','06','07','08','09','10','11','12'];
  }

  const rawData = await PaymentTransaction.findAll({
    attributes: [
      [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`), timeKey],
      [fn('COALESCE', fn('SUM', col('amount')), 0), 'revenue']
    ],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    },
    group: [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`)],
    order: [sequelize.literal(`TO_CHAR("createdAt", '${groupByFormat}')`)],
    raw: true
  });

  // Normalize chart data
  const chartData = labels.map(label => {
    const match = rawData.find(item => item[timeKey] === label);
    return {
      [timeKey]: filterType === 'year' ? moment(label, 'MM').format('MMM') : label,
      revenue: match ? parseFloat(match.revenue) : 0
    };
  });

  const totalRevenue = chartData.reduce((sum, item) => sum + item.revenue, 0);
  const highestRevenue = Math.max(...chartData.map(item => item.revenue));

  // Growth Calculation
  let currentIndex = filterType === 'year' ? now.month() : now.date() - 1;
  let previousIndex = currentIndex === 0 ? chartData.length - 1 : currentIndex - 1;

  const currentRevenue = chartData[currentIndex]?.revenue ?? 0;
  const previousRevenue = chartData[previousIndex]?.revenue ?? 0;

  const growth = getGrowthPercentage(currentRevenue, previousRevenue);

  return {
    totalRevenue,
    chartData,
    highestRevenue,
    growth,
    filterType
  };
};

/**
 * Get user growth data with monthly/daily breakdown
 * @param {'month' | 'year'} filterType - Time range filter
 * @returns {Object} Users chart data
 * @description Get user growth data with monthly/daily breakdown
 * @throws {Error} If something goes wrong while fetching users chart data
 */
const getUsersChartData = async (filterType = 'year') => {
  const now = moment.utc();
  const currentMonth = now.month();
  const currentYear = now.year();
  
  let startDate, endDate, groupByFormat, labels = [], timeKey;
  let previousPeriodStart, previousPeriodEnd;

  if (filterType === 'month') {
    // Month view - daily data for current month
    startDate = moment.utc([currentYear, currentMonth]).startOf('month').toDate();
    endDate = moment.utc([currentYear, currentMonth]).endOf('month').toDate();
    groupByFormat = 'DD'; // PostgreSQL format for day
    timeKey = 'day';
    
    // Previous month for comparison
    const previousMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const previousYear = currentMonth === 0 ? currentYear - 1 : currentYear;
    previousPeriodStart = moment.utc([previousYear, previousMonth]).startOf('month').toDate();
    previousPeriodEnd = moment.utc([previousYear, previousMonth]).endOf('month').toDate();
    
    // Create labels for all days in the month
    const daysInMonth = now.daysInMonth();
    labels = Array.from({ length: daysInMonth }, (_, i) => 
      (i + 1).toString().padStart(2, '0')
    );
  } else {
    // Year view - monthly data for current year
    startDate = moment.utc([currentYear, 0]).startOf('month').toDate();
    endDate = moment.utc([currentYear, 11]).endOf('month').toDate();
    groupByFormat = 'MM'; // PostgreSQL format for month
    timeKey = 'month';
    
    // Previous year for comparison
    previousPeriodStart = moment.utc([currentYear - 1, 0]).startOf('month').toDate();
    previousPeriodEnd = moment.utc([currentYear - 1, 11]).endOf('month').toDate();
    
    // Create labels for all months
    labels = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];
  }
  
  // Run all database queries in parallel
  const [
    userData,
    previousUserData,
    totalUsers
  ] = await Promise.all([
    getCreationCounts(User, startDate, endDate, groupByFormat),
    getCreationCounts(User, previousPeriodStart, previousPeriodEnd, groupByFormat),
    User.count()
  ]);
  
  // Create a data point for each time period (day or month)
  const chartData = labels.map(label => {
    const formattedLabel = filterType === 'year' ? 
      months[parseInt(label, 10) - 1] : // Convert month number to name
      label; // Keep day as is
    
    const newUsers = userData[label] || 0;
    
    return {
      [timeKey]: formattedLabel,
      newUsers: newUsers
    };
  });
  
  // Calculate total new users for current period
  const totalNewUsers = chartData.reduce((acc, item) => acc + item.newUsers, 0);
  
  // Calculate total new users for previous period
  const totalPreviousNewUsers = Object.values(previousUserData).reduce((acc, count) => acc + count, 0);
  
  // Calculate growth percentage
  const growth = getGrowthPercentage(totalNewUsers, totalPreviousNewUsers);
  
  // Get current period label
  const currentPeriodLabel = filterType === 'month' 
    ? `${months[currentMonth]} ${currentYear}`
    : currentYear.toString();
  
  // Get previous period label
  const previousPeriodLabel = filterType === 'month'
    ? currentMonth === 0 
      ? `${months[11]} ${currentYear - 1}` 
      : `${months[currentMonth - 1]} ${currentYear}`
    : (currentYear - 1).toString();
  
  return {
    totalUsers,
    newUsers: totalNewUsers,
    growth,
    chartData,
    currentPeriod: currentPeriodLabel,
    previousPeriod: previousPeriodLabel,
    filterType
  };
};

/**
 * Get list of stations pending approval
 * @returns {Promise<Array>} List of pending stations
 * @description Get list of stations pending approval with name and address details
 * @throws {Error} If something goes wrong while fetching pending stations
 */
const getPendingStations = async () => {
  const pendingStations = await Station.findAll({
    where: {
      approvalStatus: STATION_APPROVAL_STATUS.PENDING
    },
    attributes: [
      'id',
      'stationName',
      'address',
      'city',
      'state', 
      'pincode',
      'createdAt'
    ],
    include: [
      {
        model: Vendor,
        attributes: ['id', 'businessName'],
        required: true
      }
    ],
    order: [['createdAt', 'DESC']]
  });

  return pendingStations.map(station => ({
    id: station.id,
    stationName: station.stationName,
    address: station.address,
    city: station.city,
    state: station.state,
    pinCode: station.pinCode,
    createdAt: station.createdAt,
    vendor: {
      id: station.Vendor.id,
      businessName: station.Vendor.businessName
    }
  }));
};

/**
 * Get booking statistics including percentages of confirmed, cancelled by user, and cancelled by vendor
 * @param {string} timeFilter - Time filter (thisMonth, thisYear)
 * @returns {Promise<Object>} - Booking statistics
 */
const getBookingStats = async (timeFilter) => {
  logger.info('Calculating booking statistics', { timeFilter });
  
  // Build where clause based on time filter
  const where = {};
  const now = moment();
  
  if (timeFilter === 'thisMonth') {
    where.startTime = {
      [Op.gte]: now.clone().startOf('month').toDate()
    };
  } else {
    where.startTime = {
      [Op.gte]: now.clone().startOf('year').toDate()
    };
  }

  // Get all counts in a single query
  const [result] = await Booking.findAll({
    attributes: [
      [sequelize.fn('COUNT', sequelize.col('id')), 'total'],
      [sequelize.fn('SUM', sequelize.literal(`CASE WHEN status = '${BOOKING_STATUS.UPCOMING}' THEN 1 ELSE 0 END`)), 'confirmed'],
      [sequelize.fn('SUM', sequelize.literal(`CASE WHEN status = '${BOOKING_STATUS.CANCELED}' AND "cancelledBy" = 'user' THEN 1 ELSE 0 END`)), 'cancelledByUser'],
      [sequelize.fn('SUM', sequelize.literal(`CASE WHEN status = '${BOOKING_STATUS.CANCELED}' AND "cancelledBy" = 'vendor' THEN 1 ELSE 0 END`)), 'cancelledByVendor']
      // [sequelize.fn('SUM', sequelize.literal(`CASE WHEN status = '${BOOKING_STATUS.PENDING}' THEN 1 ELSE 0 END`)), 'pending']
    ],
    where: where,
    raw: true
  });

  const {
    total: totalBookings = 0,
    confirmed: confirmedBookings = 0,
    cancelledByUser: cancelledByUserBookings = 0,
    cancelledByVendor: cancelledByVendorBookings = 0
    // pending: pendingBookings = 0
  } = result || {};

  if (totalBookings === 0) {
    logger.info('No bookings found for the selected time period');
    return {
      totalBookings: 0,
      confirmedPercentage: 0,
      cancelledByUserPercentage: 0,
      cancelledByVendorPercentage: 0,
      // pendingPercentage: 0,
      counts: {
        confirmed: 0,
        cancelledByUser: 0,
        cancelledByVendor: 0,
        // pending: 0
      }
    };
  }

  // Calculate percentages
  const calculatePercentage = (count) => parseFloat(((count / totalBookings) * 100).toFixed(2));
  
  const stats = {
    totalBookings,
    confirmedPercentage: calculatePercentage(confirmedBookings),
    cancelledByUserPercentage: calculatePercentage(cancelledByUserBookings),
    cancelledByVendorPercentage: calculatePercentage(cancelledByVendorBookings),
    // pendingPercentage: calculatePercentage(pendingBookings),
    counts: {
      confirmed: confirmedBookings,
      cancelledByUser: cancelledByUserBookings,
      cancelledByVendor: cancelledByVendorBookings,
      // pending: pendingBookings
    }
  };

  logger.info('Booking statistics calculated successfully', stats);
  return stats;
};

module.exports = { 
  getDashboardKPIs,
  getRevenueChartData,
  getOverviewChartData,
  getUsersChartData,
  getPendingStations,
  getBookingStats
};
