const Joi = require('joi');
const {objectId, validateMediaExtension
} = require('./custom.validation');


const addProduct = {
  body: Joi.object().keys({
    name: Joi.string().trim().required(),
    description: Joi.string().trim().required(),
    type: Joi.string().custom(objectId),
    benefits: Joi.array().items(Joi.string().trim()),
    testimonials: Joi.array().items(Joi.object({
      user: Joi.string().custom(objectId),
      title: Joi.string().trim().required(),
      description: Joi.string().trim().required(),
    })),
  }),
};

const updateProduct = {
  body: Joi.object().keys({
    product: Joi.string().custom(objectId),
    name: Joi.string().trim().required(),
    description: Joi.string().trim().required(),
    type: Joi.string().custom(objectId),
    benefits: Joi.array().items(Joi.string().trim()),
    testimonials: Joi.array().items(Joi.object({
      user: Joi.string().custom(objectId),
      title: Joi.string().trim().required(),
      description: Joi.string().trim().required(),
    })),
  }),
};

const addUpdateProductMedia = {
  body: Joi.object().keys({
    product: Joi.string().custom(objectId),
    mode: Joi.string().valid('add', 'update').required(),
    productImage: Joi.object().keys({
      key: Joi.string().custom((value, helpers) => validateMediaExtension(value, helpers, 'image')),
      url: Joi.string(),
    }),
    productVideo: Joi.object().keys({
      key: Joi.string().custom((value, helpers) => validateMediaExtension(value, helpers, 'video')),
      url: Joi.string(),
    }),
    productResources: Joi.array().items(Joi.object().keys({
      key: Joi.string().custom((value, helpers) => validateMediaExtension(value, helpers, 'image')),
      url: Joi.string(),
    }, {
      key: Joi.string().custom((value, helpers) => validateMediaExtension(value, helpers, 'video')),
      url: Joi.string(),
    })),
  }),
};

const addProductType = {
    body: Joi.object().keys({
      name: Joi.string().trim().required(),
      icon: Joi.string().trim().required(),
    }),
};

module.exports = {
  addProduct,
  updateProduct,
  addUpdateProductMedia,
  addProductType
};
