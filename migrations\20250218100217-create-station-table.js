'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('stations', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
      },
      stationName:{
        type: Sequelize.STRING,
        allowNull: false,
      },
      location: {
        type: Sequelize.JSONB, // Store city, pin code, etc., in a structured format
        allowNull: false,
      },
      images: {
        type: Sequelize.JSONB, // Store an array of image URLs or file keys
        allowNull: true,
      },
      approvalStatus: {
        type: Sequelize.ENUM('pending', 'approved', 'rejected'),
        defaultValue: 'pending',
      },
      isEnabled: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      vendorId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'vendors', // Foreign key references the `Vendor` table
          key: 'id',
        },
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('stations');
  }
};
