const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const vendorSubscriptionService = require('../services/vendorSubscription.service');
const logger = require('../config/logger');
const config = require('../config/config');
const ApiError = require('../utils/ApiError');

/**
 * Initialize a payment for vendor subscription purchase
 */
const initializeSubscriptionPayment = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { provider, vendorId, subscriptionPlanId, autoRenew } = req.body;

  logger.info(`Initializing subscription payment - userId: ${userId}, provider: ${provider}, vendorId: ${vendorId}, planId: ${subscriptionPlanId}`);

  const paymentData = {
    userId,
    provider,
    vendorId,
    subscriptionPlanId,
    autoRenew
  };

  const result = await vendorSubscriptionService.initializeSubscriptionPayment(paymentData);

  logger.info(`Subscription payment initialized - provider: ${provider}, subscriptionId: ${result.subscriptionPlanId}`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Subscription payment initialized successfully',
  });
});

/**
 * Handle ZainCash payment callback for vendor subscription
 */
const handleZaincashCallback = catchAsync(async (req, res) => {
  const { token } = req.query;

  if (!token) {
    logger.error('ZainCash callback received without token');
    return res.status(httpStatus.BAD_REQUEST).json({
      status: false,
      message: 'Payment token is required'
    });
  }

  logger.info(`ZainCash subscription payment callback received with token`);

  try {
    const result = await vendorSubscriptionService.verifyZaincashSubscriptionPayment(token);

    logger.info(`ZainCash subscription payment verified and processed - subscriptionId: ${result.subscriptionId}`);

    // Redirect to the frontend with success status
    res.redirect(`${config.clientUrl}vendor/subscription/success?subscriptionId=${result.subscriptionId}`);
  } catch (error) {
    logger.error(`ZainCash subscription payment verification failed: ${error.message}`);

    // Redirect to the frontend with error status
    res.redirect(`${config.clientUrl}vendor/subscription/error?message=${encodeURIComponent(error.message)}`);
  }
});

/**
 * Handle FIB payment callback for vendor subscription
 */
const handleFibCallback = catchAsync(async (req, res) => {
  // FIB sends payment status updates as POST requests with JSON body
  const { id, status } = req.body;

  if (!id || !status) {
    logger.error('FIB callback received with invalid data', { body: req.body });
    return res.status(httpStatus.NOT_ACCEPTABLE).send();
  }

  logger.info(`FIB subscription payment callback received - paymentId: ${id}, status: ${status.status}`);

  try {
    // Transform the data to match our internal format
    const paymentData = {
      paymentId: id,
      ...status
    };

    const result = await vendorSubscriptionService.verifyFibSubscriptionPayment(paymentData);

    logger.info(`FIB subscription payment verified and processed - subscriptionId: ${result.subscriptionId}`);

    // Return 202 Accepted as per FIB documentation
    return res.status(httpStatus.ACCEPTED).send();
  } catch (error) {
    logger.error(`FIB subscription payment verification failed: ${error.message}`);

    if (error instanceof ApiError && error.statusCode === httpStatus.BAD_REQUEST) {
      // Return 406 Not Acceptable for validation/business rule errors
      return res.status(httpStatus.NOT_ACCEPTABLE).send();
    }

    // Return 500 for server errors
    return res.status(httpStatus.INTERNAL_SERVER_ERROR).send();
  }
});

/**
 * Handle FIB payment redirect for vendor subscription
 */
const handleFibRedirect = catchAsync(async (req, res) => {
  const { paymentId } = req.query;

  if (!paymentId) {
    logger.error('FIB redirect received without paymentId');
    return res.redirect(`${config.clientUrl}vendor/subscription/error?message=${encodeURIComponent('Payment ID is required')}`);
  }

  logger.info(`FIB subscription payment redirect received - paymentId: ${paymentId}`);

  try {
    const result = await vendorSubscriptionService.verifyFibSubscriptionPayment({ paymentId });

    logger.info(`FIB subscription payment verified and processed - subscriptionId: ${result.subscriptionId}`);

    // Redirect to the frontend with success status
    return res.redirect(`${config.clientUrl}vendor/subscription/success?subscriptionId=${result.subscriptionId}`);
  } catch (error) {
    logger.error(`FIB subscription payment verification failed: ${error.message}`);

    // Redirect to the frontend with error status
    return res.redirect(`${config.clientUrl}vendor/subscription/error?message=${encodeURIComponent(error.message)}`);
  }
});

/**
 * Get vendor subscription by ID
 */
const getSubscriptionById = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { subscriptionId } = req.params;

  logger.info(`Getting subscription by ID - userId: ${userId}, subscriptionId: ${subscriptionId}`);

  const subscription = await vendorSubscriptionService.getSubscriptionById(subscriptionId, userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: subscription,
    message: 'Subscription fetched successfully',
  });
});

/**
 * Get active subscription for a vendor
 */
const getActiveSubscription = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { vendorId } = req.params;

  logger.info(`Getting active subscription for vendor - userId: ${userId}, vendorId: ${vendorId}`);

  const subscription = await vendorSubscriptionService.getActiveSubscription(vendorId, userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: subscription,
    message: subscription ? 'Active subscription fetched successfully' : 'No active subscription found',
  });
});

/**
 * Cancel a vendor subscription
 */
const cancelSubscription = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { subscriptionId } = req.params;

  logger.info(`Cancelling subscription - userId: ${userId}, subscriptionId: ${subscriptionId}`);

  const subscription = await vendorSubscriptionService.cancelSubscription(subscriptionId, userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: subscription,
    message: 'Subscription cancelled successfully',
  });
});

module.exports = {
  initializeSubscriptionPayment,
  handleZaincashCallback,
  handleFibCallback,
  handleFibRedirect,
  getSubscriptionById,
  getActiveSubscription,
  cancelSubscription
};
