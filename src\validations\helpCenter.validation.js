const Joi = require('joi');
const moment = require('moment');

const createTicket = {
  body: Joi.object().keys({
    email: Joi.string()
      .email()
      .required(),
    query: Joi.string()
      .min(10)
      .max(1000)
      .required(),
  }),
};

const getTickets = {
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be greater than or equal to 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be greater than or equal to 1',
        'number.max': 'Limit cannot exceed 100',
      }),
    sortBy: Joi.string()
      .optional()
      .default('createdAt:asc')
      .custom((value, helpers) => {
        // Validate sort format: field:direction,field2:direction2
        const sortPattern = /^[\w]+(:(asc|desc))?(,[\w]+(:(asc|desc))?)*$/i;
        if (!sortPattern.test(value)) {
          return helpers.error('string.sortFormat');
        }

        // Validate allowed fields
        const allowedFields = ['createdAt', 'updatedAt', 'status', 'ticketNo'];

        const sortParts = value.split(',');
        for (const part of sortParts) {
          const [field] = part.split(':');
          if (!allowedFields.includes(field)) {
            return helpers.error('string.sortField', {field});
          }
        }

        return value;
      })
      .messages({
        'string.sortFormat': 'Invalid sort format. Example: field:asc,field2:desc',
        'string.sortField': '{{#field}} is not a valid sort field',
      }),
    search: Joi.string()
      .optional()
      .max(100)
      .messages({
        'string.max': 'Search filter cannot exceed 100 characters',
      }),
    fromDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'From date must be in YYYY-MM-DD format',
      }),
    toDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        // Check if toDate is after fromDate
        const fromDate = helpers.state.ancestors[0].fromDate;
        if (fromDate && moment(value).isBefore(moment(fromDate))) {
          return helpers.error('string.dateRange');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'To date must be in YYYY-MM-DD format',
        'string.dateRange': 'To date must be after from date',
      }),
  }),
};

const deleteTicket = {
  params: Joi.object().keys({
    ticketId: Joi.string()
      .required()
      .messages({
        'string.empty': 'Ticket ID is required',
        'any.required': 'Ticket ID is required',
      }),
  }),
};

const answerTicket = {
  params: Joi.object().keys({
    ticketId: Joi.string()
      .required()
      .messages({
        'string.empty': 'Ticket ID is required',
        'any.required': 'Ticket ID is required',
      }),
  }),
  body: Joi.object().keys({
    response: Joi.string()
      .required()
      .min(1)
      .max(2000)
      .messages({
        'string.base': 'Response must be a string',
        'string.empty': 'Response cannot be empty',
        'string.min': 'Response must be at least 1 character long',
        'string.max': 'Response cannot exceed 2000 characters',
        'any.required': 'Response is required'
      }),
    // status: Joi.string()
    //   .valid('resolved', 'pending', 'in-progress')
    //   .default('resolved')
    //   .messages({
    //     'string.base': 'Status must be a string',
    //     'any.only': 'Status must be one of: resolved, pending, in-progress'
    //   })
  })
};

module.exports = {
  createTicket,
  getTickets,
  deleteTicket,
  answerTicket
};

