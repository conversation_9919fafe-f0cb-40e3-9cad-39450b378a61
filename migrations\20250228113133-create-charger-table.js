'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('chargers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      stationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'stations',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      connectorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'connectors',
          key: 'id'
        }
      },
      powerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'powers',
          key: 'id'
        }
      },
      pricePerHour: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      }
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('chargers', ['stationId']);
    await queryInterface.addIndex('chargers', ['connectorId']);
    await queryInterface.addIndex('chargers', ['powerId']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('chargers');
  }
}; 