'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('bookings', 'cancelledBy', {
      type: Sequelize.ENUM('user', 'vendor'),
      allowNull: true,
    });

    await queryInterface.addColumn('bookings', 'timeSlotChangeReason', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('bookings', 'cancelledBy');
    await queryInterface.removeColumn('bookings', 'timeSlotChangeReason');

    
    if (queryInterface.sequelize.options.dialect === 'postgres') {
      await queryInterface.sequelize.query('DROP TYPE "enum_bookings_cancelledBy";');
    }
  }
};
