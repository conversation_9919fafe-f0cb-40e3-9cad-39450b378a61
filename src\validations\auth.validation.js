const Joi = require('joi');

const register = {
  body: Joi.object().keys({
    name: Joi.string().required().trim(),
    phone: Joi.string().trim(),
    hqAddress: Joi.string().trim(),
    hqCity: Joi.string().trim(),
    hqZipCode: Joi.string().trim(),
    hqState: Joi.string().trim(),
  }),
};

const resendOTP = {
  body: Joi.object().keys({
    email: Joi.string().required().trim(),
  }),
};

const verifyEmail = {
  body: Joi.object().keys({
    email: Joi.string().required().trim(),
    otp: Joi.number().integer().required().min(100000).max(999999)
  }),
};

const changePassword = {
  body: Joi.object().keys({
    oldPassword: Joi.string().required(),
    newPassword: Joi.string().required(),
  }),
};
module.exports = {
  register,
  resendOTP,
  verifyEmail,
  changePassword
};
