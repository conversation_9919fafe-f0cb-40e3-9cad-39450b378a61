const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const subscriptionService = require('../../services/admin/subscription.service');


const addSubscriptionPlan = catchAsync(async (req, res) => {
  const result = await subscriptionService.addSubscriptionPlan(req.body);
  res.status(httpStatus.CREATED).send({status: true, data: result, message: 'Subscription plan created successfully'});
});
const getSubscriptionPlans = catchAsync(async (req, res) => {
  const result = await subscriptionService.getAllSubscriptionPlans(req.query);

  res.status(httpStatus.OK).send({
    status: true,
    data: result.plans,
    pagination: result.pagination,
    message: 'Subscription plans fetched successfully'
  });
});
const updateSubscriptionPlan = catchAsync(async (req, res) => {
  const {planId} = req.params;
  const result = await subscriptionService.updateSubscriptionPlan(planId, req.body);
  res.status(httpStatus.OK).send({
    status: true,
    data: result,
    message: 'Subscription plan updated successfully',
  });
});
const deleteSubscriptionPlan = catchAsync(async (req, res) => {
  const {planId} = req.params;
  await subscriptionService.deleteSubscriptionPlan(planId);
  res.status(httpStatus.OK).send({status: true, message: 'Subscription plan deleted successfully'});
});

const togglePlanStatus = catchAsync(async (req, res) => {
    const {planId} = req.params;
    const result = await subscriptionService.togglePlanStatus(planId);
    res.status(httpStatus.OK).send({
      status: true,
      data: result,
      message: 'Subscription plan status toggled successfully',
    });
  });

const getVendorsByPlanId = catchAsync(async (req, res) => {
  const { planId } = req.params;
  const { page, limit } = req.query;

  const result = await subscriptionService.getVendorsByPlanId(planId, page, limit);

  res.status(httpStatus.OK).send({
    status: true,
    data: {
      vendors: result.vendors,
      planDetails: result.planDetails
    },
    pagination: result.pagination,
    message: 'Vendors fetched successfully'
  });
});

module.exports = {
  addSubscriptionPlan,
  getSubscriptionPlans,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  togglePlanStatus,
  getVendorsByPlanId,
};
