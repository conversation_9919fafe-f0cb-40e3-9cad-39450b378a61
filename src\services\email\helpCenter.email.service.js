const emailService = require('../../microservices/email.service');
const { generateTicketResponseEmail } = require('../../templates/emails/ticketResponse.template');

/**
 * Send an email notification to the user when their ticket is answered
 * @param {Object} ticket - The ticket object
 * @param {string} adminResponse - Admin's response to the ticket
 * @returns {Promise<Object>} Email sending result
 */
const sendTicketResponseEmail = async (ticket, adminResponse) => {
  const subject = `[Ticket #${ticket.ticketNo}] Response to Your Support Request`;
  
  // Generate HTML content using the template
  const html = generateTicketResponseEmail(ticket, adminResponse);
  
  // Send the email
  return emailService.sendEmail({
    to: ticket.email,
    subject,
    html
  });
};

module.exports = {
  sendTicketResponseEmail
};