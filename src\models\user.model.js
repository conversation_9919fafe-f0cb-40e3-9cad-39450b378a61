const { DataTypes, Sequelize } = require('sequelize');
const { paginate } = require('./plugins/paginate'); // Custom pagination plugin
const { sequelize } = require('../config/database'); // Import sequelize instance

const User = sequelize.define(
  'User',
  {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true, // Ensures a valid email format
      },
    },
    profilePicKey: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    profilePicUrl: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    dob: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    firebaseUid: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true, // Ensures unique Firebase UID for each user
    },
    firebaseSignInProvider: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    gender:{
      type: DataTypes.ENUM('male', 'female', 'other'),
      allowNull: true,
    },
    country:{
      type: DataTypes.STRING,
      allowNull: true,
    },
    isVendor: {
      type: DataTypes.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    },  
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    suspendedReason: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    suspendedComment: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  },
  {
    tableName: 'users',
    timestamps: true, // Enables `createdAt` and `updatedAt` fields
    paranoid: true, // Enables soft deletion (adds deletedAt field)
  }
);

// Attach the custom pagination plugin to the model
User.paginate = paginate();

module.exports = User;
