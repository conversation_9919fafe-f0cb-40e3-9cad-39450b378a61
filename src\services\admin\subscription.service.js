const httpStatus = require('http-status');
const { SubscriptionPlan, VendorSubscription, Vendor } = require('../../models');
const { Op } = require('sequelize');
const moment = require('moment');
const ApiError = require('../../utils/ApiError');
const logger = require('../../config/logger');

/**
 * Add a new subscription plan
 * @param {Object} planData
 * @returns {Promise<SubscriptionPlan>}
 */
const addSubscriptionPlan = async (planData) => {
  // Check if plan with same name exists
  const existingPlan = await SubscriptionPlan.findOne({
    where: { name: planData.name }
  });

  if (existingPlan) {

    throw new ApiError(httpStatus.BAD_REQUEST, 'Subscription plan with this name already exists');
  }

  // Get the highest displayOrder
  const highestOrder = await SubscriptionPlan.max('displayOrder') || 0;

    // Create new subscription plan with auto-incremented displayOrder

    const subscriptionPlan = await SubscriptionPlan.create({
      ...planData,
      displayOrder: highestOrder + 1
    });

    logger.info('New subscription plan created', {
      planId: subscriptionPlan.id,
      planName: subscriptionPlan.name
    });

    return subscriptionPlan;
};

/**
 * Get all subscription plans with pagination and filtering
 * @param {Object} options - Pagination and filtering options
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Number of items per page
 * @param {string} [options.sortBy='displayOrder:asc,createdAt:desc'] - Sort order
 * @param {string} [options.name] - Filter by name (partial match)
 * @param {boolean} [options.isActive] - Filter by active status
 * @param {number} [options.priceMin] - Filter by minimum price
 * @param {number} [options.priceMax] - Filter by maximum price
 * @param {string} [options.fromDate] - Filter by creation date (from) - ISO date string
 * @param {string} [options.toDate] - Filter by creation date (to) - ISO date string
 * @returns {Promise<Object>} - Paginated result with plans and metadata
 */
const getAllSubscriptionPlans = async (options = {}) => {
    const {
      page = 1,
      limit = 10,
      sortBy = 'displayOrder:asc,createdAt:desc',
      name,
      isActive,
      priceMin,
      priceMax,
      fromDate,
      toDate
    } = options;

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build where clause for filtering
    const where = {};

    // Add name filter (case-insensitive partial match)
    if (name) {
      where.name = { [Op.iLike]: `%${name}%` };
    }

    // Add isActive filter
    if (isActive !== undefined) {
      where.isActive = isActive;
    }

    // Add price range filter
    if (priceMin !== undefined || priceMax !== undefined) {
      where.price = {};

      if (priceMin !== undefined) {
        where.price[Op.gte] = priceMin;
      }

      if (priceMax !== undefined) {
        where.price[Op.lte] = priceMax;
      }
    }

    // Add date range filter for createdAt
    if (fromDate || toDate) {
      where.createdAt = {};

      if (fromDate) {
        // Set the time to the start of the day for fromDate
        const startDate = moment(fromDate).startOf('day').toDate();
        where.createdAt[Op.gte] = startDate;
      }

      if (toDate) {
        // Set the time to the end of the day for toDate
        const endDate = moment(toDate).endOf('day').toDate();
        where.createdAt[Op.lte] = endDate;
      }
    }

    // Parse sort options
    const order = [];
    const sortOptions = sortBy.split(',');

    for (const option of sortOptions) {
      const [field, direction = 'asc'] = option.split(':');
      order.push([field, direction.toUpperCase()]);
    }

    // Get total count for pagination
    const count = await SubscriptionPlan.count({ where });

    // Get plans with pagination, filtering, and sorting
    const plans = await SubscriptionPlan.findAll({
      where,
      order,
      limit,
      offset
    });

    // Calculate total pages
    const totalPages = Math.ceil(count / limit);

    // Return paginated result
    return {
      plans,
      pagination: {
        totalItems: count,
        totalPages,
        currentPage: page,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1
      }
    };
};

/**
 * Update a subscription plan
 * @param {number} planId
 * @param {Object} updateData
 * @returns {Promise<SubscriptionPlan>}
 */
const updateSubscriptionPlan = async (planId, updateData) => {
  const plan = await SubscriptionPlan.findByPk(planId);

  if (!plan) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription plan not found');
  }

  // If name is being updated, check for duplicates
  if (updateData.name && updateData.name !== plan.name) {
    
    const existingPlan = await SubscriptionPlan.findOne({
      where: { name: updateData.name }
    });

    if (existingPlan) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Subscription plan with this name already exists');
    }

  }


    const updatedPlan = await plan.update(updateData);

    logger.info('Subscription plan updated', {
      planId,
      planName: updatedPlan.name
    });

    return updatedPlan;
};

/**
 * Delete a subscription plan
 * @param {number} planId
 * @returns {Promise<void>}
 */
const deleteSubscriptionPlan = async (planId) => {
  const plan = await SubscriptionPlan.findByPk(planId);

  if (!plan) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription plan not found');
  }


    await plan.destroy();

    // Reorder remaining plans
    const remainingPlans = await SubscriptionPlan.findAll({
      order: [['displayOrder', 'ASC']]
    });

    // Update display orders to be sequential
    await Promise.all(
      remainingPlans.map((plan, index) =>
        plan.update({ displayOrder: index + 1 })
      )
    );

    logger.info('Subscription plan deleted', { planId });
};

/**
 * Toggle subscription plan status
 * @param {number} planId
 * @returns {Promise<SubscriptionPlan>}
 */
const togglePlanStatus = async (planId) => {
  const plan = await SubscriptionPlan.findByPk(planId);

  if (!plan) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription plan not found');
  }


    const updatedPlan = await plan.update({
      isActive: !plan.isActive
    });

    logger.info('Subscription plan status toggled', {
      planId,
      planName: plan.name,
      newStatus: updatedPlan.isActive
    });

    return updatedPlan;
};

const getVendorsByPlanId = async (planId, page = 1, limit = 10) => {
  // Check if subscription plan exists
  const plan = await SubscriptionPlan.findByPk(planId, {
    attributes: ['id', 'name', 'description', 'durationValue', 'durationType', 'price', 'currency', 'benefits']
  });
  if (!plan) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription plan not found');
  }
  

  const offset = (page - 1) * limit;

  // Get vendors with active subscriptions for the given plan
  const { count, rows: vendors } = await VendorSubscription.findAndCountAll({
    where: {
      subscriptionPlanId: planId,
      isActive: true
    },
    include: [
      {
        model: Vendor,
        attributes: ['id', 'businessName', 'email', 'phone', 'createdAt']
      }
    ],
    attributes: ['id', 'startDate', 'endDate', 'autoRenew'],
    limit,
    offset,
    distinct: true
  });

  // Transform the response to flatten the structure
  const transformedVendors = vendors.map(subscription => ({
    subscriptionId: subscription.id,
    subscriptionStartDate: subscription.startDate,
    subscriptionEndDate: subscription.endDate,
    autoRenew: subscription.autoRenew,
    ...subscription.Vendor.dataValues
  }));

  
  return {
    planDetails: {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      durationValue: plan.durationValue,
      durationType: plan.durationType,
      price: Number(plan.price),
      currency: plan.currency,
      benefits: plan.benefits
    },
    vendors: transformedVendors,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      totalItems: count,
      totalPages: Math.ceil(count / limit)
    }
  };
};

module.exports = {
  addSubscriptionPlan,
  getAllSubscriptionPlans,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  togglePlanStatus,
  getVendorsByPlanId
};
