'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('payment_transactions', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Transaction ID',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',  // Make sure to change this if 'users' table is different
          key: 'id',
        },
        comment: 'Who made the payment',
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        comment: 'Positive for credit, negative for debit',
      },
      paymentMethod: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Method used for payment',
      },
      source: {
        type: Sequelize.ENUM('wallet', 'external'),
        allowNull: false,
        comment: 'Source of the payment',
      },
      purpose: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Purpose of the payment',
      },
      status: {
        type: Sequelize.ENUM('success', 'pending', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Status of the payment',
      },
      referenceId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Link to UserSubscription / Session etc.',
      },
      referenceType: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Type of reference (e.g., "subscription", "booking")',
      },
      remarks: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Optional notes',
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
        comment: 'Additional data related to the transaction',
      },
      transactionType: {
        type: Sequelize.ENUM('credit', 'debit'),
        allowNull: false,
        comment: 'Defines if the transaction is credit or debit',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
      }
    });

    // Add indexes
    await queryInterface.addIndex('payment_transactions', ['userId']);
    await queryInterface.addIndex('payment_transactions', ['status']);
    await queryInterface.addIndex('payment_transactions', ['createdAt']);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('payment_transactions');
  }
};
