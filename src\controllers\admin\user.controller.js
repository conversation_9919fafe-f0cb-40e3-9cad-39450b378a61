const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const userService = require('../../services/admin/user.service');
const { changePassword } = require('../../services/auth.service');
const logger = require('../../config/logger');

/**
 * Get all users with optional filters
 */
const getUsers = catchAsync(async (req, res) => {
  const { page, limit, sortBy, name, email, phone, isActive, fromDate, toDate } = req.query;

  logger.info('Admin requesting users list with filters', {
    adminId: req.admin.id,
    filters: {
      page,
      limit,
      sortBy,
      name,
      email,
      phone,
      isActive,
      fromDate,
      toDate,
    },
  });

  const filters = {
    page,
    limit,
    sortBy,
    name,
    email,
    phone,
    isActive,
    fromDate,
    toDate,
  };

  const result = await userService.getAllUsers(filters);

  logger.info(`Retrieved ${result.users.length} users out of ${result.pagination.totalItems} total`);

  res.status(httpStatus.OK).send({
    status: true,
    data: result.users,
    pagination: result.pagination,
    message: 'Users fetched successfully',
  });
});

/**
 * Toggle user active status
 */
const toggleUserStatus = catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { isActive, suspendedReason, suspendedComment } = req.body;

  logger.info(`Admin ${req.admin.id} is toggling status for user ${userId} to ${isActive}`);

  const updatedUser = await userService.toggleUserStatus(
    userId,
    isActive,
    suspendedReason,
    suspendedComment
  );

  res.status(httpStatus.OK).json({
    status: true,
    data: updatedUser,
    message: `User ${isActive ? 'activated' : 'suspended'} successfully`,
  });
});


/**
 * Delete a user
 */
const deleteUser = catchAsync(async (req, res) => {
  const { userId } = req.params;

  logger.info(`Admin ${req.admin.id} is deleting user ${userId}`);

  const deletedUser = await userService.deleteUser(userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: deletedUser,
    message: 'User deleted successfully',
  });
});

const createUser = catchAsync(async (req, res) => {
  const { email, password, ...userData } = req.body;

  // Create user in Firebase
  const firebaseUser = await userService.createFirebaseUser(email, password);

  // Prepare user data for database
  const userObj = {
    email: firebaseUser.email,
    firebaseUid: firebaseUser.uid,
    profilePicUrl: firebaseUser.photoURL || null,
    phone: firebaseUser.phoneNumber || null,
    isEmailVerified: firebaseUser.emailVerified,
    firebaseSignInProvider: 'password',
    ...userData,
  };

  // Create user in database
  const user = await userService.createUser(userObj);

  return res.status(200).json({
    status: true,
    data: user,
    message: 'User created successfully',
  });
});

const resetPassword = catchAsync(async (req, res) => {
  const { userId, email, oldPassword, newPassword } = req.body;

  const result = await changePassword(userId, email, oldPassword, newPassword);

  return res.status(httpStatus.OK).json({ status: true, data: result, message: 'Password changed successfully.' });
});

/**
 * Get user statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} User statistics
 * @throws {Error} If something goes wrong while fetching user statistics
 */
const getUserStats = catchAsync(async (req, res) => {
  const stats = await userService.getUserStats();

  res.status(httpStatus.OK).json({
    status: true,
    data: stats,
    message: 'User statistics fetched successfully'
  });
});

/**
 * Get user by id
 * @param {Object} req
 * @param {Object} res
 * @returns {Promise<User>}
 */
const getUserById = catchAsync(async (req, res) => {
  const user = await userService.getUserById(req.params.userId);
  logger.info(`Admin ${req.admin.id} fetched details for user ${user.id}`);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }
  res.status(httpStatus.OK).json({
    status: true,
    data: user,
    message: 'User fetched successfully'
  });
});

const getUserBookings = catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page, limit, status, fromDate, toDate, region, minPrice,
    maxPrice } = req.query;

  const result = await userService.getUserBookings({
    userId,
    page,
    limit,
    status,
    fromDate,
    toDate,
    region,
    minPrice,
    maxPrice
  });

  res.status(httpStatus.OK).json({
    status: true,
    data: result.bookings,
    pagination: result.pagination,
    message: 'User bookings fetched successfully'
  });
});

/**
 * Get detailed booking information by booking ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBookingDetails = catchAsync(async (req, res) => {
  const booking = await userService.getBookingDetails(req.params.bookingId);
  res.send({
    status: 'success',
    data: booking
  });
});

module.exports = {
  getUsers,
  toggleUserStatus,
  deleteUser,
  createUser,
  resetPassword,
  getUserStats,
  getUserById,
  getUserBookings,
  getBookingDetails
};
