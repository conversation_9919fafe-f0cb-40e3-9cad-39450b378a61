const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

const Policy = sequelize.define(
  'Policy',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    type: {
      type: DataTypes.ENUM('privacy', 'subscription', 'penalty', 'cancellation', 'terms'),
      allowNull: false,
      comment: 'Type of policy document'
    },
    userType: {
      type: DataTypes.ENUM('all', 'user', 'vendor'),
      allowNull: false,
      comment: 'Target user type for this policy'
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Policy title'
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: false,
      comment: 'Policy content/description'
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      comment: 'Whether this policy is currently active'
    }
  },
  {
    tableName: 'policies',
    timestamps: true,
  }
);

module.exports = Policy;