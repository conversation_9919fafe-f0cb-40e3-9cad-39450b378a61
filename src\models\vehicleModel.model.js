const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');

const VehicleModel = sequelize.define(
  'VehicleModel',
  {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    brandId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicle_brands',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    tableName: 'vehicle_models',
    timestamps: false, // No createdAt/updatedAt fields needed
  }
);

// Attach the custom pagination plugin
VehicleModel.paginate = paginate();

module.exports = VehicleModel;