'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('help_center_tickets', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users', // Make sure this table exists
          key: 'id'
        },
        comment: 'Reference to the user submitting the ticket'
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          isEmail: true
        },
        comment: 'Email to which the ticket resolution will be sent'
      },
      ticketNo: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true,
        comment: 'Unique ticket number like #123-4567'
      },
      query: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'User message or issue description'
      },
      status: {
        type: Sequelize.ENUM('resolved', 'pending', 'on-hold'),
        allowNull: false,
        defaultValue: 'pending',
        comment: 'Status of the ticket'
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
          defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
         defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      }
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
      await queryInterface.dropTable('help_center_tickets');
  }
};
