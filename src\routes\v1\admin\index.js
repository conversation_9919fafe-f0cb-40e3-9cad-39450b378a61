const express = require('express');
const authRoute = require('./auth.route');
const userRoute = require('./user.route');
const policyRoute = require('./policy.route');
const helpCenterRoute = require('./helpCenter.route');
const faqRoute = require('./faq.route');
const dashboardRoute = require('./dashboard.route');
const vendorRoute = require('./vendor.route');
// Import other admin routes as needed

const router = express.Router();

const defaultRoutes = [
  {
    path: '/auth',
    route: authRoute,
  },
  {
    path: '/users',
    route: userRoute,
  },
  {
    path: '/policies',
    route: policyRoute,
  },
  {
    path: '/help-center',
    route: helpCenterRoute,
  },
  {
    path: '/faqs',
    route: faqRoute,
  },
  {
    path: '/dashboard',
    route: dashboardRoute,
  },
  {
    path: '/vendors',
    route: vendorRoute,
  },
  // Add other admin routes as needed
];

defaultRoutes.forEach((route) => {
  router.use(route.path, route.route);
});

module.exports = router; 
