const Joi = require('joi');

const login = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
    password: Joi.string().required()
  })
};

const createAdmin = {
  body: Joi.object().keys({
    name: Joi.string().required(),
    email: Joi.string().required().email(),
    password: Joi.string().required().min(8),
    role: Joi.string().valid('admin', 'super_admin').default('admin')
  })
};

module.exports = {
  login,
  createAdmin
}; 