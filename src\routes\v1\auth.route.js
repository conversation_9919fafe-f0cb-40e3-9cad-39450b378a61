const express = require('express');
const router = express.Router();

const validate = require('../../middlewares/validate');
const {firebaseAuth, generateToken} = require('../../middlewares/firebaseAuth');
const { subscribeToTopic, unsubscribeFromTopic } = require('../../microservices/notification.service');
const authValidation = require('../../validations/auth.validation');
const {authController} = require('../../controllers');
const notificationValidation = require('../../validations/notification.validation')
router.post("/login", firebaseAuth, authController.login);
router.post("/register", firebaseAuth, authController.register);
router.post("/genrateToken/:uid", generateToken);
router.post("/subscribe-to-topic",firebaseAuth,validate(notificationValidation.subscribeToTopic), subscribeToTopic);
router.post("/unsubscribe-from-topic",firebaseAuth,validate(notificationValidation.unsubscribeFromTopic), unsubscribeFromTopic);

router.put("/change-password",firebaseAuth,validate(authValidation.changePassword), authController.changePassword);


module.exports = router;
