const moment = require('moment');
const { Op } = require('sequelize');

// Get UTC start and end of a given year
const getYearStartAndEnd = (year) => ({
  startDate: moment.utc(`${year}-01-01`).startOf('day').toDate(),
  endDate: moment.utc(`${year}-12-31`).endOf('day').toDate()
});

// Calculate growth rate percentage
const getGrowthPercentage = (current, previous) => {
  if (previous === 0) return current > 0 ? 100 : 0;
  return parseFloat((((current - previous) / previous) * 100).toFixed(2));
};

/**
 * Count records with specific status created in a date range
 * @param {Model} Model - Sequelize model
 * @param {Object} options - Query options
 * @param {string} [options.status] - Status to filter by (optional)
 * @param {Date} options.startDate - Start date for the range
 * @param {Date} options.endDate - End date for the range
 * @param {Object} [options.additionalWhere] - Additional where conditions (optional)
 * @returns {Promise<number>} Count of records
 */
const countRecordsByStatus = async (Model, options) => {
  const { status, startDate, endDate, additionalWhere = {} } = options;
  
  const where = {
    createdAt: {
      [Op.between]: [startDate, endDate]
    },
    ...additionalWhere
  };
  
  // Add status filter if provided
  if (status) {
    where.status = status;
  }
  
  return Model.count({ where });
};

/**
 * Count records created in a year for a given model
 * @param {Model} Model - Sequelize model
 * @param {number} year - Year to count records for
 * @param {string} [status] - Status to filter by (optional)
 * @param {Object} [additionalWhere] - Additional where conditions (optional)
 * @returns {Promise<number>} Count of records
 */
const countRecordsInYear = async (Model, year, status = null, additionalWhere = {}) => {
  const { startDate, endDate } = getYearStartAndEnd(year);
  
  return countRecordsByStatus(Model, {
    status,
    startDate,
    endDate,
    additionalWhere
  });
};

module.exports = {
  getYearStartAndEnd,
  getGrowthPercentage,
  countRecordsByStatus,
  countRecordsInYear
}; 
