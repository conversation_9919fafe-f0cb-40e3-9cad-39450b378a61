const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const Review = require('../models/review.model');
const Station = require('../models/station.model');
const User = require('../models/user.model');
const addReview = async (stationId, userId, rating, comment) => {
    
    const station = await Station.findByPk(stationId);
    if (!station) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    }

    const existingReview = await Review.findOne({
        where: {
          stationId,
          userId
        }
      });
    
      if (existingReview) {
        throw new ApiError(
          httpStatus.CONFLICT,
          'You have already reviewed this station'
        );
      }

    return await Review.create({
      stationId,
      userId,
      rating,
      comment,
    });
  };

  const getReviews = async (stationId, options = {}, sortingFields ) => {
  
    const limit = options.limit || 10;
    const page = options.page || 1;
    const offset = (page - 1) * limit;


    const countPromise = Review.count({
      where: { stationId },
    });
  
    // Fetch the reviews with pagination
    const reviewsPromise = Review.findAll({
      where: { stationId },
      include: [
        {
          model: User,
          attributes: ['id', 'name', 'profilePicUrl'],
          as: 'reviewer'
        }],
      limit,
      offset,
      order:sortingFields,
      attributes: ['id', 'rating', 'comment', 'createdAt'],
    });
  
    const [count, reviews] = await Promise.all([countPromise, reviewsPromise]);
  
  
    return {
      success: true,
      data: reviews,
      pagination: {
        totalItems: count,
        totalPages: Math.ceil(count / limit),
        currentPage: page,
        pageSize: limit,
      },
    };
  };

  const deleteReview = async (reviewId, userId) => {
    const review = await Review.findOne({
      where: {
        id: reviewId,
        userId
      }
    });

    if (!review) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Review not found');
    }

    await review.destroy();
  }
  
module.exports = {
    addReview,getReviews,deleteReview
};