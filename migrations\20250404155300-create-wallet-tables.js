'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create wallets table
    await queryInterface.createTable('wallets', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      ownerId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'ID of the owner (user or vendor)',
      },
      ownerType: {
        type: Sequelize.ENUM('user', 'vendor'),
        allowNull: false,
        comment: 'Type of the owner (user or vendor)',
      },
      balance: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
        defaultValue: 0.00,
      },
      currency: {
        type: Sequelize.STRING(3),
        allowNull: false,
        defaultValue: 'USD',
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      lastUpdated: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      version: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 1,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add unique constraint to ensure a user or vendor has only one wallet
    await queryInterface.addConstraint('wallets', {
      fields: ['ownerId', 'ownerType'],
      type: 'unique',
      name: 'wallet_owner_unique',
    });

    // Create wallet_transactions table
    await queryInterface.createTable('wallet_transactions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      walletId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'wallets',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      balanceAfter: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM(
          'topup',
          'payment',
          'refund',
          'withdrawal',
          'transfer',
          'adjustment',
          'booking_payment',
          'booking_refund'
        ),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM('pending', 'completed', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
      },
      description: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      metadata: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      referenceId: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      relatedTransactionId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'wallet_transactions',
          key: 'id',
        },
      },
      processedAt: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('wallet_transactions', ['walletId']);
    await queryInterface.addIndex('wallet_transactions', ['type']);
    await queryInterface.addIndex('wallet_transactions', ['status']);
    await queryInterface.addIndex('wallet_transactions', ['referenceId']);
    await queryInterface.addIndex('wallet_transactions', ['relatedTransactionId']);
  },

  async down(queryInterface, Sequelize) {
    // Drop tables in reverse order
    await queryInterface.dropTable('wallet_transactions');
    await queryInterface.dropTable('wallets');
    await queryInterface.removeConstraint('wallets', 'wallet_owner_unique');
  }
};
