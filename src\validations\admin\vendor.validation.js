const Joi = require('joi');
const moment = require('moment');
/**
 * Validation schema for getting vendors
 */
const getVendors = {
  query: Joi.object().keys({
    name: Joi.string(),
    email: Joi.string().email(),
    phone: Joi.string(),
    isActive: Joi.boolean(),
    fromDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'From date must be in YYYY-MM-DD format',
      }),
    toDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        // Check if toDate is after fromDate
        const fromDate = helpers.state.ancestors[0].fromDate;
        if (fromDate && moment(value).isBefore(moment(fromDate))) {
          return helpers.error('string.dateRange');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'To date must be in YYYY-MM-DD format',
        'string.dateRange': 'To date must be after from date',
      }),
    page: Joi.number()
      .integer()
      .min(1)
      .default(1),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(10),
    sortBy: Joi.string()
      .optional()
      .default('createdAt:desc')
      .custom((value, helpers) => {
        // Validate sort format: field:direction,field2:direction2
        const sortPattern = /^[\w]+(:(asc|desc))?(,[\w]+(:(asc|desc))?)*$/i;
        if (!sortPattern.test(value)) {
          return helpers.error('string.sortFormat');
        }

        // Validate allowed fields
        const allowedFields = ['id', 'name', 'isActive', 'createdAt', 'updatedAt'];

        const sortParts = value.split(',');
        for (const part of sortParts) {
          const [field] = part.split(':');
          if (!allowedFields.includes(field)) {
            return helpers.error('string.sortField', {field});
          }
        }

        return value;
      })
      .messages({
        'string.sortFormat': 'Invalid sort format. Example: field:asc,field2:desc',
        'string.sortField': '{{#field}} is not a valid sort field',
      }),
    businessName: Joi.string(),
    subscriptionStatus: Joi.string().valid('active', 'expired', 'none'),
  }),
};

const toggleVendorStatus = {
  params: Joi.object().keys({
    userId: Joi.string()
      .required()
      .description('User ID'),
  }),
  body: Joi.object().keys({
    isActive: Joi.boolean()
      .required()
      .description('User active status'),
    suspendedReason: Joi.string().when('isActive', {
      is: false,
      then: Joi.required().description('Reason for suspension'),
      otherwise: Joi.forbidden(),
    }),
    suspendedComment: Joi.string().when('isActive', {
      is: false,
      then: Joi.required().description('Additional comment for suspension'),
      otherwise: Joi.forbidden(),
    }),
  }),
};

const deleteUser = {
  params: Joi.object().keys({
    userId: Joi.string()
      .required()
      .description('User ID'),
  }),
};

const createVendor = {
  body: Joi.object().keys({
    email: Joi.string()
      .required()
      .email(),
    password: Joi.string()
      .required()
      .min(6),
    name: Joi.string().required(),
    businessName: Joi.string().required(),
    email: Joi.string()
      .email()
      .required(),
    phone: Joi.string().required(),
    // Additional optional fields
    dob: Joi.date().optional(),
    gender: Joi.string()
      .valid('male', 'female', 'other')
      .optional(),
    country: Joi.string().optional(),
  }),
};

const resetPassword = {
  body: Joi.object().keys({
    userId: Joi.number().required(),
    email: Joi.string()
      .required()
      .email(),
    oldPassword: Joi.string().required(),
    newPassword: Joi.string().required(),
  }),
};

const getVendorStations = {
  params: Joi.object().keys({
    userId: Joi.string()
      .required()
      .description('User ID'),
  }),
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10),
  }),
};

const getStationDetails = {
  params: Joi.object().keys({
    stationId: Joi.string()
      .required()
      .description('Station ID'),
  }),
};

const addChargers = {
  params: Joi.object().keys({
    stationId: Joi.string()
      .required()
      .description('Station ID'),
  }),
  body: Joi.object().keys({
    connectorId: Joi.number().required(),
    powerId: Joi.number().required(),
    pricePerHour: Joi.number().required(),
  }),
};

const getVendorById = {
  params: Joi.object().keys({
    userId: Joi.number().integer().required(),
  }),
};

const getVendorBusiness = {
  params: Joi.object().keys({
    userId: Joi.number().integer().required(),
  }),
};

module.exports = {
  getVendors,
  toggleVendorStatus,
  deleteUser,
  createVendor,
  resetPassword,
  getVendorStations,
  getStationDetails,
  addChargers,
  resetPassword,
  getVendorById,
  getVendorBusiness
};
