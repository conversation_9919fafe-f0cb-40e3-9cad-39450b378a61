const {HelpCenterTicket} = require('../../models');
const {Op} = require('sequelize');
const moment = require('moment');
const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const helpCenterEmailService = require('../email/helpCenter.email.service');
const logger = require('../../config/logger');
const { getYearStartAndEnd, getGrowthPercentage, countRecordsInYear } = require('../../utils/analytics.utils');
const { TICKET_STATUS } = require('../../constants');

/**
 * Get all help center tickets with pagination and filtering
 * @param {Object} options - Filter and pagination options
 * @returns {Promise<Object>} - Paginated list of tickets
 */
const getTickets = async (options = {}) => {
  const {page, limit, sortBy, search, fromDate, toDate} = options;

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build where clause for filtering
  const where = {};



if (search) {
  where.ticketNo = { [Op.iLike]: `%${search}%` };
}

  // Add date range filter
  if (fromDate || toDate) {
    where.createdAt = {};

    if (fromDate) {
      const fromDateObj = moment(fromDate)
        .startOf('day')
        .toDate();
      where.createdAt[Op.gte] = fromDateObj;
    }

    if (toDate) {
      const toDateObj = moment(toDate)
        .endOf('day')
        .toDate();
      where.createdAt[Op.lte] = toDateObj;
    }
  }

  // Parse sort options
  const order = [];
  const sortOptions = sortBy.split(',');

  for (const option of sortOptions) {
    const [field, direction = 'asc'] = option.split(':');
    order.push([field, direction.toUpperCase()]);
  }

  // Use findAndCountAll for better performance (single query)
  const {rows: tickets, count} = await HelpCenterTicket.findAndCountAll({
    where,
    order,
    limit,
    offset,
    subQuery: false, // Optimize for complex queries
  });

  // Calculate total pages
  const totalPages = Math.ceil(count / limit);

  // Return paginated result
  return {
    tickets,
    pagination: {
      totalItems: count,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};

/**
 * Delete a help center ticket
 * @param {string} ticketId - ID of the ticket to delete
 * @returns {Promise<void>}
 */
const deleteTicket = async ticketId => {
  const ticket = await HelpCenterTicket.findOne({where: {ticketNo: ticketId}});

  if (!ticket) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Ticket not found');
  }

  await ticket.destroy();
  return true;
};

/**
 * Answer a help center ticket
 * @param {number} ticketId - ID of the ticket to answer
 * @param {Object} answerData - Data for answering the ticket
 * @param {string} answerData.response - Admin's response to the ticket
 * @param {string} answerData.status - New status for the ticket
 * @param {number} answerData.adminId - ID of the admin answering the ticket
 * @returns {Promise<Object>} - Updated ticket
 */
const answerTicket = async (ticketId, answerData) => {
  const {response} = answerData;

  // Find the ticket
  const ticket = await HelpCenterTicket.findOne({where: {ticketNo: ticketId}});
  logger.info(`Ticket found: ${ticket}`);
  
  if (!ticket) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Ticket not found');
  }

  // Optionally, send an email notification to the user
  // await emailService.sendTicketResponseEmail(ticket.email, ticket.ticketNo, response);
  await helpCenterEmailService.sendTicketResponseEmail(ticket, response);
  logger.info(`Email notification sent to ${ticket.email} for ticket ${ticket.ticketNo}`);

  return ticket;
};

/**
 * Get help center statistics including counts by status and yearly growth
 * @returns {Promise<Object>} Statistics data
 */
const getHelpCenterStatistics = async () => {
  const currentYear = moment().year();
  const previousYear = currentYear - 1;
  
  // Get date ranges for current and previous year
  const { startDate: currentYearStart, endDate: currentYearEnd } = getYearStartAndEnd(currentYear);
  
  // Get counts for current year
  const [
    currentTotalCount,
    currentResolvedCount,
    currentPendingCount,
    currentOnHoldCount
  ] = await Promise.all([
    // Total tickets in current year
    countRecordsInYear(HelpCenterTicket, currentYear),
    
    // Resolved tickets in current year
    countRecordsInYear(HelpCenterTicket, currentYear, TICKET_STATUS.RESOLVED),
    
    // Pending tickets in current year
    countRecordsInYear(HelpCenterTicket, currentYear, TICKET_STATUS.PENDING),
    
    // On-hold tickets in current year
    countRecordsInYear(HelpCenterTicket, currentYear, TICKET_STATUS.ON_HOLD)
  ]);
  
  // Get counts for previous year
  const [
    previousTotalCount,
    previousResolvedCount,
    previousPendingCount,
    previousOnHoldCount
  ] = await Promise.all([
    // Total tickets in previous year
    countRecordsInYear(HelpCenterTicket, previousYear),
    
    // Resolved tickets in previous year
    countRecordsInYear(HelpCenterTicket, previousYear, TICKET_STATUS.RESOLVED),
    
    // Pending tickets in previous year
    countRecordsInYear(HelpCenterTicket, previousYear, TICKET_STATUS.PENDING),
    
    // On-hold tickets in previous year
    countRecordsInYear(HelpCenterTicket, previousYear, TICKET_STATUS.ON_HOLD)
  ]);
  

  return {
    totalTickets: {
      value: currentTotalCount,
      yearlyGrowth: getGrowthPercentage(currentTotalCount, previousTotalCount)
    },
    resolvedTickets: {
      value: currentResolvedCount,
      yearlyGrowth: getGrowthPercentage(currentResolvedCount, previousResolvedCount)
    },
    pendingTickets: {
      value: currentPendingCount,
      yearlyGrowth: getGrowthPercentage(currentPendingCount, previousPendingCount)
    },
    onHoldTickets: {
      value: currentOnHoldCount,
      yearlyGrowth: getGrowthPercentage(currentOnHoldCount, previousOnHoldCount)
    },
  };
};

module.exports = {
  getTickets,
  deleteTicket,
  answerTicket,
  getHelpCenterStatistics
};
