const catchAsync = require('../utils/catchAsync');
const { vendorService } = require('../services');
const httpStatus = require('http-status');

const addVendor = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const vendor = await vendorService.addVendor(req.body, userId);
    res.status(httpStatus.CREATED).json({ 
        status: true,
        data: vendor,
        message: "Vendor added successfully."
     });
});

const setupVendorProfile = catchAsync(async (req, res) => {  
    const userId = req.user.id;
    const vendor = await vendorService.setupVendorProfile(req.body, userId);
    res.status(httpStatus.CREATED).json({ 
        status: true,
        data: vendor,
        message: "Vendor profile setup successfully."
     });
});

const getVendorProfile = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const vendor = await vendorService.getVendorProfile(userId);
    res.status(httpStatus.OK).json({
        status: true,
        data: vendor,
        message: "Vendor profile fetched successfully."
     });
});

const getVendorStations = catchAsync(async (req,res) =>{
    const userId = req.user.id;
    let { page = 1, limit = 10 } = req.query;

    const stations = await vendorService.getVendorStations(userId,page,limit);
    res.status(httpStatus.OK).json({
        status: true,
        data: stations,
        message: "Vendor stations fetched successfully."
    })
})

const updateVendor = catchAsync(async (req, res) => {
    const vendor = await vendorService.updateVendor(req.body, req.user.id);
    res.json({
        status: true,
        data: vendor,
        message: 'Vendor details updated successfully. KYC verification is now pending.'
    });
});

module.exports = { addVendor,setupVendorProfile,getVendorProfile,getVendorStations,updateVendor };