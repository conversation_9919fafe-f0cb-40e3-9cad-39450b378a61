'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('stations', 'totalRatingSum', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('stations', 'totalReviews', {
      type: Sequelize.INTEGER,
      allowNull: false,
      defaultValue: 0
    });

    await queryInterface.addColumn('stations', 'averageRating', {
      type: Sequelize.FLOAT,
      allowNull: false,
      defaultValue: 0
    });
    

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION "updateStationRating"()
      RETURNS TRIGGER AS $$
      BEGIN
        WITH stats AS (
          SELECT 
            COALESCE(SUM(rating), 0) AS "sum",
            COUNT(*) AS "count"
          FROM "reviews"
          WHERE "stationId" = COALESCE(NEW."stationId", OLD."stationId")
        )
        UPDATE "stations"
        SET 
          "totalRatingSum" = stats."sum",
          "totalReviews" = stats."count",
          "averageRating" = CASE 
            WHEN stats."count" > 0 THEN stats."sum"::FLOAT / stats."count" 
            ELSE 0 
          END
        FROM stats
        WHERE "id" = COALESCE(NEW."stationId", OLD."stationId");
        
        RETURN NULL;
      END;
      $$ LANGUAGE plpgsql;
    `);
    
    // Create triggers
    await queryInterface.sequelize.query(`
      CREATE TRIGGER reviewAfterInsert
      AFTER INSERT ON "reviews"
      FOR EACH ROW
      EXECUTE FUNCTION "updateStationRating"();
    `);
    
    await queryInterface.sequelize.query(`
      CREATE TRIGGER reviewAfterUpdate
      AFTER UPDATE OF "rating" ON "reviews"
      FOR EACH ROW
      WHEN (OLD."rating" IS DISTINCT FROM NEW."rating")
      EXECUTE FUNCTION "updateStationRating"();
    `);
    
    await queryInterface.sequelize.query(`
      CREATE TRIGGER reviewAfterDelete
      AFTER DELETE ON "reviews"
      FOR EACH ROW
      EXECUTE FUNCTION "updateStationRating"();
    `);
    
    // Backfill existing data
    await queryInterface.sequelize.query(`
      UPDATE "stations" s
      SET 
        "totalRatingSum" = COALESCE((SELECT SUM(rating) FROM "reviews" r WHERE r."stationId" = s."id"), 0),
        "totalReviews" = COALESCE((SELECT COUNT(*) FROM "reviews" r WHERE r."stationId" = s."id"), 0),
        "averageRating" = CASE 
          WHEN (SELECT COUNT(*) FROM "reviews" r WHERE r."stationId" = s."id") > 0
          THEN (SELECT SUM(rating) FROM "reviews" r WHERE r."stationId" = s."id")::FLOAT / 
               (SELECT COUNT(*) FROM "reviews" r WHERE r."stationId" = s."id")
          ELSE 0
        END
    `);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS review_after_insert ON reviews;
      DROP TRIGGER IF EXISTS review_after_update ON reviews;
      DROP TRIGGER IF EXISTS review_after_delete ON reviews;
    `);

    // Remove function
    await queryInterface.sequelize.query(`
      DROP FUNCTION IF EXISTS "updateStationRating"();
    `);

    await queryInterface.removeColumn('stations', 'totalRatingSum');
    await queryInterface.removeColumn('stations', 'totalReviews');
    await queryInterface.removeColumn('stations', 'averageRating');
  }
};
