const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');

const VehicleTrim = sequelize.define(
  'VehicleTrim',
  {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    modelId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'vehicle_models',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    },
  },
  {
    tableName: 'vehicle_trims',
    timestamps: false, // No createdAt/updatedAt fields needed
  }
);

// Attach the custom pagination plugin
VehicleTrim.paginate = paginate();

module.exports = VehicleTrim;
