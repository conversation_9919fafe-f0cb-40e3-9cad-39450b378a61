const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const { 
  getAdminNotifications, 
  markNotificationAsRead, 
  markAllNotificationsAsRead,
  getUnreadNotificationCount
} = require('../../microservices/notification.service');

/**
 * Get admin notifications with pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getNotifications = catchAsync(async (req, res) => {

    const { page, limit} = req.query;
    
    const result = await getAdminNotifications(page, limit);
    
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'Notification fetched successfully',
    })

});

/**
 * Mark a notification as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const markAsRead = catchAsync(async (req, res) => {

    const { notificationId } = req.params;
    
    const result = await markNotificationAsRead(notificationId, null, true);
    
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'Notification marked as read successfully',
    });
  
});

/**
 * Mark all notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const markAllAsRead = catchAsync(async (req, res) => {

    const result = await markAllNotificationsAsRead(null, true);
    
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'All notifications marked as read successfully',
    });

 
});

/**
 * Get unread notification count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getUnreadCount = catchAsync(async (req, res) => {

    const result = await getUnreadNotificationCount(null, true);
    
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'Unread notification count fetched successfully',
    });
 
});

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount
}; 