 New connection established.
 Received: [2,"00000001","BootNotification",{"chargePointModel":"tsdogjlztw_l","chargePointVendor":"tesida","firmwareVersion":"V4.5","chargeBoxSerialNumber":"tesidaBFB97932","imsi":""}]
 Handling action: BootNotification
 Sent BootNotification response: [
   3,
   '00000001',
   {
     currentTime: '2024-12-11T13:31:59.722Z',
     interval: 300,
     status: 'Accepted'
   }
 ]
 Received: [2,"00000002","Heartbeat",{}]
 Handling action: Heartbeat
 Sent error response: [
   4,
   '00000002',
   'NotImplemented',
   'Action Heartbeat is not implemented'
 ]
 Received: [2,"00000003","MeterValues",{"connectorId":1,"meterValue":[{"timestamp":"2024-12-11T13:31:59Z","sampledValue":[{"value":"0","location":"Cable","unit":"Wh","measurand":"Energy.Active.Import.Register","context":"Other"},{"value":"21","location":"Body","unit":"Celsius","measurand":"Temperature","context":"Other"},{"value":"0","location":"Cable","unit":"A","measurand":"Current.Import","phase":"L1","context":"Other"},{"value":"0","location":"Cable","unit":"A","measurand":"Current.Import","phase":"L2","context":"Other"},{"value":"0","location":"Cable","unit":"A","measurand":"Current.Import","phase":"L3","context":"Other"},{"value":"236","unit":"V","measurand":"Voltage","phase":"L1-N","context":"Other"},{"value":"236","unit":"V","measurand":"Voltage","phase":"L2-N","context":"Other"},{"value":"236","unit":"V","measurand":"Voltage","phase":"L3-N","context":"Other"}]}]}]

Received: [2,"00000005","StatusNotification",{"connectorId":1,"errorCode":"NoError","status":"Available","timestamp":"2024-12-11T13:32:03Z"}]
 Handling action: StatusNotification
 Sent error response: [
   4,
   '00000005',
   'NotImplemented',
   'Action StatusNotification is not implemented'
 ]
 Received: [2,"00000006","StatusNotification",{"connectorId":2,"errorCode":"NoError","status":"Available","timestamp":"2024-12-11T13:32:05Z"}]
 Handling action: StatusNotification
