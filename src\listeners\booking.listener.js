const eventEmitter = require('../utils/eventEmitter');
const { sendToTopic } = require('../microservices/notification.service');
const logger = require('../config/logger');
const moment = require('moment');
const { BOOKING_EVENTS } = require('../constants/bookingEvents');
const { RECIPIENT_TYPE, REFERENCE_TYPE } = require('../constants');

// Booking rescheduled listener
eventEmitter.on(BOOKING_EVENTS.RESCHEDULED, async (data) => {
    const notificationData = {
        title: 'Booking Time Changed',
        body: `Your booking time for ${data.stationName} has been changed`
    };

    const notificationPayload = {
        type: 'booking_reschedule',
        bookingId: data.bookingId,
        stationName: data.stationName,
        newStartTime: moment(data.startTime).format('MMMM Do YYYY, h:mm a'),
        newEndTime: moment(data.endTime).format('MMMM Do YYYY, h:mm a'),
        reason: data.reason,
        userId: data.userId,
        referenceId: data.bookingId,
        referenceType: REFERENCE_TYPE.BOOKING
    };

    try {
        await sendToTopic(
            `user_${data.userId}`,
            notificationData,
            notificationPayload,
            RECIPIENT_TYPE.USER
        );
        logger.info('Booking rescheduled event processed successfully');
    } catch (error) {
        logger.error('Failed to process booking rescheduled event:', error);
    }
});

// Booking confirmation listener
eventEmitter.on(BOOKING_EVENTS.CONFIRMED, async (data) => {
    logger.info(`Processing booking confirmation event for bookingId: ${data.bookingId}`);

    const notificationData = {
        title: 'Booking Confirmed',
        body: `Your booking at ${data.stationName} has been confirmed for ${moment(data.startTime).format('MMMM Do YYYY, h:mm a')}`
    };

    const notificationPayload = {
        type: 'booking_confirmed',
        bookingId: data.bookingId,
        stationName: data.stationName,
        startTime: moment(data.startTime).format('MMMM Do YYYY, h:mm a'),
        endTime: moment(data.endTime).format('MMMM Do YYYY, h:mm a'),
        userId: data.userId,
        referenceId: data.bookingId,
        referenceType: REFERENCE_TYPE.BOOKING
    };

    try {
        // Send notification to user
        await sendToTopic(
            `user_${data.userId}`,
            notificationData,
            notificationPayload,
            RECIPIENT_TYPE.USER
        );

        logger.info('Booking confirmation event processed successfully');
    } catch (error) {
        logger.error('Failed to process booking confirmation event:', error);
    }
});