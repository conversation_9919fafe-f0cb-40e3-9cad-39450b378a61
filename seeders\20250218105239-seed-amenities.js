'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    // Insert predefined amenities
    await queryInterface.bulkInsert('amenities', [
      { name: 'WiFi', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Restroom', createdAt: new Date(), updatedAt: new Date() },
      { name: 'CCTV Security', createdAt: new Date(), updatedAt: new Date() },
      { name: '24/7 Availability', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Charging Cables Provided', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Covered Parking', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Food & Beverage', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Lounge Area', createdAt: new Date(), updatedAt: new Date() },
      { name: 'Wheelchair Access', createdAt: new Date(), updatedAt: new Date() }
    ], {});
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
        // Remove all inserted amenities
        await queryInterface.bulkDelete('amenities', null, {});
  }
};
