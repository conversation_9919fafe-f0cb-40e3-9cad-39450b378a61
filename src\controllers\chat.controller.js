const catchAsync = require('../utils/catchAsync');
const { chatService } = require('../services');
const httpStatus = require('http-status');

const initializeChat = catchAsync(async (req, res) => {
  const { topic, bookingId } = req.body;
  const userId = req.user.id;

  const chat = await chatService.createChat(userId, topic,bookingId);
  res.status(httpStatus.CREATED).json({
    status: true,
    data: chat,
    message: `Your support chat is now active, regarding ${topic}. Please provide additional details regarding your issue`,
  });
});

const sendMessage = catchAsync(async (req, res) => {
  const { chatId, message } = req.body;
  const userId = req.user.id;


  const chatMessage = await chatService.sendMessage(chatId, userId, message, false);
  res.status(httpStatus.CREATED).json({
    status: true,
    data: chatMessage,
    message: 'Message sent successfully',
  });
});

const getMessages = catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  const userId = req.user.id;
  const messages = await chatService.getChatMessages(chatId,userId, page, limit);
  res.json({
    status: true,
    data: messages,
    message: 'Messages retrieved successfully',
  });
});

const closeChat = catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const adminId = req.user.id;

  const chat = await chatService.closeChat(chatId, adminId);
  res.json({
    status: true,
    data: chat,
    message: 'Chat closed successfully',
  });
});

module.exports = {
  initializeChat,
  sendMessage,
  getMessages,
  closeChat,
}; 