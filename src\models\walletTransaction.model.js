const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const WalletTransaction = sequelize.define(
  'WalletTransaction',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    walletId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wallets',
        key: 'id',
      },
    },
    amount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Positive for credit, negative for debit',
    },
    balanceAfter: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      comment: 'Wallet balance after this transaction',
    },
    type: {
      type: DataTypes.ENUM(
        'topup',
        'payment',
        'refund',
        'withdrawal',
        'transfer',
        'adjustment',
        'booking_payment',
        'booking_refund',
        'penalty'
      ),
      allowNull: false,
    },
    status: {
      type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
    },
    description: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    metadata: {
      type: DataTypes.JSONB,
      allowNull: true,
      comment: 'Additional data related to the transaction (e.g., booking ID, payment method, penalty reason)',
    },
    referenceId: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'External reference ID (e.g., payment gateway transaction ID)',
    },
    relatedTransactionId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'wallet_transactions',
        key: 'id',
      },
      comment: 'For linked transactions like refunds or transfers',
    },
    processedAt: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'When the transaction was processed',
    },
    bookingId: {  // Reference to the Booking model
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'bookings',
        key: 'id',
      },
      comment: 'The booking associated with this wallet transaction (e.g., for booking payments or refunds)',
    },
  },
  {
    tableName: 'wallet_transactions',
    timestamps: true,
    indexes: [
      {
        fields: ['walletId'],
      },
      {
        fields: ['type'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['referenceId'],
      },
      {
        fields: ['relatedTransactionId'],
      },
    ],
  }
);

module.exports = WalletTransaction;
