const httpStatus = require('http-status');
const { Policy } = require('../../models');
const ApiError = require('../../utils/ApiError');
const { Op } = require('sequelize');

/**
 * Create a new policy
 * @param {Object} policyData
 * @returns {Promise<Policy>}
 */
const createPolicy = async (policyData) => {
  // Check if a policy with the same type and userType already exists
  const existingPolicy = await Policy.findOne({
    where: {
      type: policyData.type,
      userType: policyData.userType
    }
  });

  if (existingPolicy) {
    throw new ApiError(
      httpStatus.CONFLICT,
      `A ${policyData.type} policy for ${policyData.userType} already exists`
    );
  }

  return Policy.create(policyData);
};

/**
 * Get policies with pagination and filtering
 * @param {Object} filters - Filter criteria
 * @param {Object} options - Query options
 * @returns {Promise<Object>} - Policies and pagination info
 */
const getPolicies = async (filters = {}, options = {}) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;
  
  const whereClause = {};
  if (filters.type) whereClause.type = filters.type;
  if (filters.userType) whereClause.userType = filters.userType;
  
  const { count, rows } = await Policy.findAndCountAll({
    where: whereClause,
    limit,
    offset,
    order: [[sortBy, sortOrder.toUpperCase()]],
    attributes: { exclude: ['deletedAt'] }
  });
  
  return {
    policies: rows,
    pagination: {
      totalItems: count,
      totalPages: Math.ceil(count / limit),
      currentPage: page,
      itemsPerPage: limit
    }
  };
};

/**
 * Get policy by ID
 * @param {number} id - Policy ID
 * @returns {Promise<Policy>}
 */
const getPolicyById = async (id) => {
  return Policy.findByPk(id, {
    attributes: { exclude: ['deletedAt'] }
  });
};

/**
 * Update policy
 * @param {number} id - Policy ID
 * @param {Object} updateData - Data to update
 * @returns {Promise<Policy>}
 */
const updatePolicy = async (id, updateData) => {
  const policy = await getPolicyById(id);
  
  if (!policy) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Policy not found');
  }
  
  Object.assign(policy, updateData);
  await policy.save();
  
  return policy;
};

/**
 * Toggle policy status
 * @param {number} id - Policy ID
 * @returns {Promise<Policy>}
 */
const togglePolicyStatus = async (id) => {
  const policy = await getPolicyById(id);
  
  if (!policy) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Policy not found');
  }
  
  policy.isActive = !policy.isActive;
  await policy.save();
  
  return policy;
};

/**
 * Delete policy
 * @param {number} id - Policy ID
 * @returns {Promise<boolean>}
 */
const deletePolicy = async (id) => {
  const policy = await getPolicyById(id);
  
  if (!policy) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Policy not found');
  }
  
  await policy.destroy();
  return true;
};

/**
 * Get active policies by type and user type
 * @param {string} type - Policy type
 * @param {string} userType - User type
 * @returns {Promise<Policy[]>}
 */
const getActivePoliciesByTypeAndUserType = async (type, userType) => {
  return Policy.findAll({
    where: {
      type,
      isActive: true,
      [Op.or]: [
        { userType },
        { userType: 'all' }
      ]
    },
    order: [['createdAt', 'DESC']],
    attributes: { exclude: ['deletedAt'] }
  });
};

module.exports = {
  createPolicy,
  getPolicies,
  getPolicyById,
  updatePolicy,
  togglePolicyStatus,
  deletePolicy,
  getActivePoliciesByTypeAndUserType
};