const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const VendorSubscription = sequelize.define('VendorSubscription', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  vendorId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'vendors',
      key: 'id',
    },
    comment: 'ID of the vendor',
  },
  subscriptionPlanId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'subscription_plans',
      key: 'id',
    },
    comment: 'ID of the subscribed plan',
  },
  startDate: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'When the subscription starts',
  },
  endDate: {
    type: DataTypes.DATE,
    allowNull: false,
    comment: 'When the subscription ends',
  },
  isActive: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Whether the subscription is currently active',
  },
  autoRenew: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether subscription is set to auto-renew',
  },
  inGracePeriod: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Whether the subscription is currently in grace period',
  },
  renewedFromId: {
    type: DataTypes.INTEGER,
    allowNull: true,
    references: {
      model: 'vendor_subscriptions',
      key: 'id',
    },
    comment: 'ID of the previous subscription it renewed from',
  },
  renewalAttempts: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'Number of failed renewal attempts',
  },
  status: {
    type: DataTypes.ENUM(
      'active',
      'expired',
      'canceled',
      'inGracePeriod',
      'pendingRenewal',
      'failedRenewal'
    ),
    allowNull: false,
    defaultValue: 'active',
    comment: 'Current status of the vendor subscription',
  },
}, {
  tableName: 'vendor_subscriptions',
  timestamps: true,
  indexes: [
    {
      fields: ['vendorId'],
    },
    {
      fields: ['subscriptionPlanId'],
    },
    {
      fields: ['isActive'],
    },
  ]
});

module.exports = VendorSubscription;
