const {userService} = require('../services');
const catchAsync = require('../utils/catchAsync');
const httpStatus = require('http-status');


const getDetails = catchAsync(async (req, res) => {
  const user = req.user;
  return res.status(200).json({ status:true,data:user, message: "User logged in successfully." });
});
const updateUser = catchAsync(async (req, res) => {
    // const user = req.user;
    console.log(req.file)
    const user = await userService.updateUserById(req.user.id,req.body,req.file);
    return res.status(200).json({ status:true,data:user, message: "User updated  successfully." });
});



module.exports = {
    getDetails,
    updateUser
 
};
