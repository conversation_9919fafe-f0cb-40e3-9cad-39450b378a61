const { VehicleBrand, VehicleModel, VehicleTrim, ManufacturingUnit, BatteryCapacity } = require('../models');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const UserVehicle = require('../models/userVehicle.model');
const User = require('../models/user.model');


/**
 * Add vehicle for a user
 * @param {Object} vehicleData
 * @returns {Promise<UserVehicle>}
 */
const addUserVehicle = async (vehicleData, user_id) => {
  // Validate user exists
  const user = await User.findByPk(user_id);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }

  try {
    // Handle brand - check if exists by name or ID
    let brandId = vehicleData.brandId;
    if (!brandId && vehicleData.brandName) {
      // Check if brand with this name already exists
      const existingBrand = await VehicleBrand.findOne({
        where: { name: vehicleData.brandName }
      });

      if (existingBrand) {
        brandId = existingBrand.id;
      } else {
        // Create new brand if doesn't exist
        const brand = await VehicleBrand.create({
          name: vehicleData.brandName
        });
        brandId = brand.id;
      }
    }

    // Handle model - check if exists by name or ID
    let modelId = vehicleData.modelId;
    if (!modelId && vehicleData.modelName) {
      // Check if model with this name already exists for this brand
      const existingModel = await VehicleModel.findOne({
        where: {
          name: vehicleData.modelName,
          brandId: brandId
        }
      });

      if (existingModel) {
        modelId = existingModel.id;
      } else {
        // Create new model if doesn't exist
        const model = await VehicleModel.create({
          name: vehicleData.modelName,
          brandId: brandId
        });
        modelId = model.id;
      }
    }

    // Handle trim - check if exists by name or ID
    // let trimId = vehicleData.trimId;
    // if (!trimId && vehicleData.trimName) {
    //   // Check if trim with this name already exists for this model
    //   const existingTrim = await VehicleTrim.findOne({
    //     where: {
    //       name: vehicleData.trimName,
    //       modelId: modelId
    //     }
    //   });

    //   if (existingTrim) {
    //     trimId = existingTrim.id;
    //   } else {
    //     // Create new trim if doesn't exist
    //     const trim = await VehicleTrim.create({
    //       name: vehicleData.trimName,
    //       modelId: modelId
    //     });
    //     trimId = trim.id;
    //   }
    // }

    // Check for duplicate registration number
    // const existingVehicle = await UserVehicle.findOne({
    //   where: { registrationNumber: vehicleData.registrationNumber.trim().toUpperCase() }
    // });

    // if (existingVehicle) {
    //   throw new ApiError(httpStatus.CONFLICT, 'Vehicle with this registration number already exists');
    // }

    // Create new user vehicle with normalized registration number
    const userVehicle = await UserVehicle.create({
      userId: user_id,
      // Handle brand data
      brandId: brandId,
      brandName: vehicleData.brandName ? vehicleData.brandName : null,

      // Handle model data
      modelId: modelId,
      modelName: vehicleData.modelName ? vehicleData.modelName : null,

      // Handle trim data
      // trimId: null,
      // trimName: vehicleData.trimName ? vehicleData.trimName : null,

      // Handle other fields
      manufacturingUnitId: vehicleData.manufacturingUnitId,
      batteryCapacityId: vehicleData.batteryCapacityId,
      // registrationNumber: vehicleData.registrationNumber.trim().toUpperCase()
    });

    return await UserVehicle.findByPk(userVehicle.id, {
      attributes: [
        'id',
      ],
      include: [
        {
          model: VehicleBrand,
          attributes: ['name'],
          as: 'VehicleBrand'
        },
        {
          model: VehicleModel,
          attributes: ['name'],
          as: 'VehicleModel'
        },
        {
          model: ManufacturingUnit,
          attributes: ['name', 'location'],
          as: 'ManufacturingUnit'
        },
        {
          model: BatteryCapacity,
          attributes: ['capacity', 'unit'],
          as: 'BatteryCapacity'
        }
      ]
    });

  } catch (error) {
    if (error instanceof ApiError) throw error;
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error creating user vehicle');
  }
};

const updateUserVehicle = async (vehicleData, user_id) => {
  // Validate user exists
  const user = await User.findByPk(user_id);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }
  const userVehicle = await UserVehicle.findByPk(vehicleData.id);
  if (!userVehicle) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User vehicle not found');
  }
  // Validate userVehicle belongs to user
  if (userVehicle.userId !== user_id) {
    throw new ApiError(httpStatus.FORBIDDEN, 'User vehicle does not belong to the user');
  }
  try {
    // Handle brand - check if exists by name or ID
    let brandId = vehicleData.brandId;
    if (!brandId && vehicleData.brandName) {
      // Check if brand with this name already exists
      const existingBrand = await VehicleBrand.findOne({
        where: { name: vehicleData.brandName }
      });
      if (existingBrand) {
        brandId = existingBrand.id;
      } else {
        // Create new brand if doesn't exist
        const brand = await VehicleBrand.create({
          name: vehicleData.brandName
        });
        brandId = brand.id;
      }
    }
    // Handle model - check if exists by name or ID
    let modelId = vehicleData.modelId;
    if (!modelId && vehicleData.modelName) {
      // Check if model with this name already exists for this brand
      const existingModel = await VehicleModel.findOne({
        where: {
          name: vehicleData.modelName,
          brandId: brandId
        }
      });
      if (existingModel) {
        modelId = existingModel.id;
      } else {
        // Create new model if doesn't exist
        const model = await VehicleModel.create({
          name: vehicleData.modelName,
          brandId: brandId
        });
        modelId = model.id;
      }
    }
    // Handle trim - check if exists by name or ID
    // let trimId = vehicleData.trimId;
    // if (!trimId && vehicleData.trimName) {
    //   // Check if trim with this name already exists for this model
    //   const existingTrim = await VehicleTrim.findOne({
    //     where: {
    //       name: vehicleData.trimName,
    //       modelId: modelId
    //     }
    //   });
    //   if (existingTrim) {
    //     trimId = existingTrim.id;
    //   } else {
    //     // Create new trim if doesn't exist
    //     const trim = await VehicleTrim.create({
    //       name: vehicleData.trimName,
    //       modelId: modelId
    //     });
    //     trimId = trim.id;
    //   }
    // }
    // Check for duplicate registration number
    // const existingVehicle = await UserVehicle.findOne({
    //   where: { registrationNumber: vehicleData.registrationNumber.trim().toUpperCase() }
    // });
    // if (existingVehicle && existingVehicle.id !== userVehicle.id) {
    //   throw new ApiError(httpStatus.CONFLICT, 'Vehicle with this registration number already exists');
    // }
    // Update user vehicle with normalized registration number
    userVehicle.brandId = brandId;
    userVehicle.brandName = vehicleData.brandName ? vehicleData.brandName : null;
    userVehicle.modelId = modelId;
    userVehicle.modelName = vehicleData.modelName ? vehicleData.modelName : null;
    // userVehicle.trimId = trimId;
    // userVehicle.trimName = vehicleData.trimName ? vehicleData.trimName : null;
    userVehicle.manufacturingUnitId = vehicleData.manufacturingUnitId;
    userVehicle.batteryCapacityId = vehicleData.batteryCapacityId;
    // userVehicle.registrationNumber = vehicleData.registrationNumber.trim().toUpperCase();
    await userVehicle.save();
    return await UserVehicle.findByPk(userVehicle.id, {
      attributes: [
        'id'
      ],
      include: [
        {
          model: VehicleBrand,
          attributes: ['name'],
          as: 'VehicleBrand'
        },
        {
          model: VehicleModel,
          attributes: ['name'],
          as: 'VehicleModel'
        },
        {
          model: ManufacturingUnit,
          attributes: ['name', 'location'],
          as: 'ManufacturingUnit'
        },
        {
          model: BatteryCapacity,
          attributes: ['capacity', 'unit'],
          as: 'BatteryCapacity'
        }
      ]
    });
  } catch (error) {
    if (error instanceof ApiError) throw error;
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error updating user vehicle');
  }
};
const getUserVehicles = async (userId, queryParams) => {
  const filter = {
    where: { userId },
    attributes: [
      'id',
      'isPrimary',
      'updatedAt' // Ensure updatedAt is included in the results
    ],
    include: [
      {
        model: VehicleBrand,
        attributes: ['name'],
        as: 'VehicleBrand'
      },
      {
        model: VehicleModel,
        attributes: ['name'],
        as: 'VehicleModel'
      },
      {
        model: ManufacturingUnit,
        attributes: ['name', 'location'],
        as: 'ManufacturingUnit'
      },
      {
        model: BatteryCapacity,
        attributes: ['capacity', 'unit'],
        as: 'BatteryCapacity'
      }
    ]
  };

  // Default values
  const defaultLimit = 10;
  const defaultPage = 1;
  const validSortFields = ['id', 'registrationNumber', 'isPrimary', 'updatedAt']; // Valid fields
  const validSortOrders = ['ASC', 'DESC'];

  // Handle pagination
  const limit = parseInt(queryParams.limit, 10) || defaultLimit;
  const page = parseInt(queryParams.page, 10) || defaultPage;

  // Handle sorting
  let sortField = queryParams.sortBy && validSortFields.includes(queryParams.sortBy)
    ? queryParams.sortBy
    : 'updatedAt';

  let sortOrder = queryParams.sortOrder && validSortOrders.includes(queryParams.sortOrder.toUpperCase())
    ? queryParams.sortOrder.toUpperCase()
    : 'DESC';

  // Ensure primary vehicles come first, then sort by the given field
  filter.order = [
    ['isPrimary', 'DESC'], // Primary vehicles first
    [sortField, sortOrder] // Sort by user-defined field (default: updatedAt DESC)
  ];

  // Apply pagination
  filter.limit = limit;
  filter.offset = (page - 1) * limit;

  // Query the database
  const { count, rows: userVehicles } = await UserVehicle.findAndCountAll(filter);

  return {
    results: userVehicles,
    page,
    limit,
    totalPages: Math.ceil(count / limit),
    totalResults: count,
    sortBy: sortField,
    sortOrder
  };
};



const getAllBrands = async () => {
  const brands = await VehicleBrand.findAll({
    attributes: ['id', 'name']
  });
  return brands;
};

const getModelsByBrand = async (brandId) => {
  const models = await VehicleModel.findAll({
    where: { brandId },
    attributes: ['id', 'name']
  });

  if (!models.length) {
    throw new ApiError(httpStatus.NOT_FOUND, 'No models found for this brand');
  }

  return models;
};

const deleteUserVehicle = async (id, userId) => {
  const userVehicle = await UserVehicle.findOne({
    where: {
      id,
      userId

    }
  });

  if (!userVehicle) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User vehicle not found or does not belong to the user');
  }

  const wasPrimary = userVehicle.isPrimary;

  await userVehicle.destroy();
  // Set the first remaining vehicle as primary if the deleted vehicle was primary
  if (wasPrimary) {
    const anotherVehicle = await UserVehicle.findOne({
      where: { userId },
      order: [['createdAt', 'DESC']]
    });
    if (anotherVehicle) {
      anotherVehicle.isPrimary = true;
      await anotherVehicle.save();
    }
  }


  return userVehicle;
}

const getTrimsByModel = async (modelId) => {
  const trims = await VehicleTrim.findAll({
    where: { modelId },
    attributes: ['id', 'name'],
  });

  if (!trims.length) {
    throw new ApiError(httpStatus.NOT_FOUND, 'No trims found for this model');
  }

  return trims;
};

const getAllManufacturingUnits = async () => {
  const units = await ManufacturingUnit.findAll({
    attributes: ['id', 'name', 'location']
  });
  return units;
};

const getAllBatteryCapacities = async () => {
  const batteries = await BatteryCapacity.findAll({
    attributes: ['id', 'capacity', 'unit']
  });
  return batteries;
};


const createVehiclePrimary = async (data, userId) => {
  // Find the vehicle that needs to be updated
  const vehicle = await UserVehicle.findOne({
    where: { userId, id: data.vehicleId }
  });

  if (!vehicle) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User vehicle not found or does not belong to the user');
  }

  // **Set isPrimary to true using .save()**
  vehicle.isPrimary = true;
  await vehicle.save();  // ✅ Triggers beforeUpdate hook
};





module.exports = {
  getAllBrands,
  getModelsByBrand,
  getTrimsByModel,
  getAllManufacturingUnits,
  getAllBatteryCapacities,
  addUserVehicle,
  getUserVehicles,
  deleteUserVehicle,
  createVehiclePrimary,
  updateUserVehicle
}; 