const express = require('express');
const validate = require('../../middlewares/validate');
const helpCenterController = require('../../controllers/helpCenter.controller');
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const helpCenterValidation = require('../../validations/helpCenter.validation');
const router = express.Router();

/**
 * @api {post} v1/help-center/tickets Create a new support ticket
 * @apiDescription Create a new help center support ticket
 * @apiParam {String} email User email 
 * @apiParam {String} query User query/issue description
 * @apiSuccess {Object} ticket Created ticket
 */
router.post('/tickets', firebaseAuth, validate(helpCenterValidation.createTicket), helpCenterController.createTicket);

/**
 * @api {get} v1/help-center/faqs Get all live FAQs
 * @apiDescription Get all published FAQs for mobile users
 * @apiName GetLiveFAQs
 * @apiGroup HelpCenter
 * @apiPermission public
 *
 * @apiSuccess {Object[]} data List of FAQs
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.get('/faqs', validate(helpCenterValidation.getLiveFAQs), helpCenterController.getLiveFAQs);

module.exports = router;
