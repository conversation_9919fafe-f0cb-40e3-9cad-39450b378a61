const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const { walletController } = require('../../controllers');
const walletValidation = require('../../validations/wallet.validation');

// Admin routes for wallet management
router.post(
  '/',
  firebaseAuth,
  validate(walletValidation.createWallet),
  walletController.createWallet
);

// Create wallet for current user
router.post(
  '/user',
  firebaseAuth,
  validate(walletValidation.createUserWallet),
  walletController.createUserWallet
);

// Create wallet for current vendor
router.post(
  '/vendor',
  firebaseAuth,
  validate(walletValidation.createVendorWallet),
  walletController.createVendorWallet
);

// Get current user's wallet
router.get(
  '/user/me',
  firebaseAuth,
  walletController.getCurrentUserWallet
);

// Get current vendor's wallet
router.get(
  '/vendor/me',
  firebaseAuth,
  walletController.getCurrentVendorWallet
);

// Top up wallet
router.post(
  '/:walletId/topup',
  firebaseAuth,
  validate(walletValidation.topUpWallet),
  walletController.topUpWallet
);

// Debit from wallet
router.post(
  '/:walletId/debit',
  firebaseAuth,
  validate(walletValidation.debitFromWallet),
  walletController.debitFromWallet
);

// Get wallet transactions
router.get(
  '/:walletId/transactions',
  firebaseAuth,
  validate(walletValidation.getWalletTransactions),
  walletController.getWalletTransactions
);

// Get transaction by ID
router.get(
  '/:walletId/transactions/:transactionId',
  firebaseAuth,
  validate(walletValidation.getTransaction),
  walletController.getTransactionById
);

module.exports = router;
