const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database'); // Import sequelize instance
const Booking = sequelize.define("Booking", {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true,
  },
  userId: {
    type: DataTypes.INTEGER,
    references: {
      model: 'users', // Foreign key references the `User` table
      key: 'id',
    },
    allowNull: false,
  },
  stationId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: "stations",
      key: "id",
    },
  },
  chargerId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: "chargers",
      key: "id",
    },
  },
  vehicleId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: "vehicles", // Assuming you have a Vehicle model
      key: "id",
    },
  },
  startTime: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  endTime: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  status: {
    type: DataTypes.ENUM("pending","upcoming", "cancelled", "completed"),
    defaultValue: "pending",
  },
  totalCost: {
    type: DataTypes.FLOAT,
    allowNull: true,
  },
  paymentStatus: {
    type: DataTypes.ENUM("pending", "paid", "failed", "refunded"),
    defaultValue: "pending",
  },
  cancellationReason: {
    type: DataTypes.STRING,
    allowNull: true, // Optional field
  },
  cancelledBy:{
    type: DataTypes.ENUM('user', 'vendor'),
    allowNull: true, // Optional field
  },
  timeSlotChangeReason:{
    type: DataTypes.STRING,
    allowNull: true, // Optional field
  },
  cancellationTime: {
    type: DataTypes.DATE,
    allowNull: true, // Optional field
  },
  refundAmount: {
    type: DataTypes.FLOAT,
    allowNull: true, // Optional field
  },

},
  {
    tableName: 'bookings',
    timestamps: true, // Enables `createdAt` and `updatedAt` fields
  });


module.exports = Booking;


