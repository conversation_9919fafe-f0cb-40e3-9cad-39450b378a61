const catchAsync = require('../utils/catchAsync');
const {stationService} = require('../services');
const httpStatus = require('http-status');
const logger = require('../config/logger');

const addStation = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const {stationName, city, pinCode, state, address, latitude, longitude, operationalSchedule, amenities,phoneNumber} = req.body;

  logger.info('Adding new station', {
    userId,
    stationName,
    city,
    hasFiles: !!req.files?.length,
  });

  const result = await stationService.addStation({
    city,
    pinCode,
    state,
    address,
    latitude,
    longitude,
    operationalSchedule,
    userId,
    amenities,
    stationName,
    files: req.files,
    phoneNumber
  });

  logger.info('Station added successfully', {
    stationId: result.id,
    userId,
    stationName,
  });

  res.status(httpStatus.CREATED).json({
    status: true,
    data: result,
    message: 'Station added successfully',
  });
});

const getAmenities = catchAsync(async (req, res) => {
  const amenities = await stationService.getAmenities();
  res.json({amenities});
});

const getStations = catchAsync(async (req, res) => {
  let {
    latitude,
    longitude,
    useCurrentLocation,
    stationName,
    chargingTypes,
    stationAmenities,
    vehicleId,
    page,
    limit,
  } = req.query;

  logger.debug('Fetching stations with filters', {
    filters: {
      latitude,
      longitude,
      useCurrentLocation,
      stationName,
      stationAmenities,
      vehicleId,
      page,
      limit,
    },
  });

  const result = await stationService.getStations({
    latitude,
    longitude,
    useCurrentLocation,
    stationName,
    chargingTypes,
    stationAmenities,
    vehicleId,
    page,
    limit,
  });

  logger.debug(`Retrieved ${result.stations.length} stations out of ${result.pagination.totalItems} total`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result.stations,
    pagination: result.pagination,
    message: 'Stations fetched successfully',
  });
});

const getStationById = catchAsync(async (req, res) => {
  const { stationId } = req.params;
  const station = await stationService.getStationById(stationId,req.user.id);
  res.status(httpStatus.OK).json({
    status: true,
    data: station,
    message: 'Station fetched successfully',
  });
});

const toggleStationStatus = catchAsync(async (req, res) => {
  const {stationId} = req.params;
  const {isEnabled, reason} = req.body;
  const userId = req.user.id;

  logger.info('Toggling station status', {
    stationId,
    userId,
    isEnabled,
    reason,
  });

  await stationService.toggleStationStatus(stationId, userId, isEnabled, reason);

  logger.info(`Station ${isEnabled ? 'enabled' : 'disabled'} successfully`, {
    stationId,
    userId,
  });

  res.status(httpStatus.OK).json({
    status: true,
    message: 'Station enabled/disabled successfully',
  });
});

const addChargerToStation = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const chargerData = req.body;

  logger.info('Adding charger to station', {
    userId,
    stationId: chargerData.stationId,
    chargerDetails: {
      connectorId: chargerData.connectorId,
      powerId: chargerData.powerId,
      pricePerHour: chargerData.pricePerHour,
    },
  });

  const result = await stationService.addChargerToStation(chargerData, userId);

  logger.info('Charger added successfully', {
    chargerId: result.id,
    stationId: chargerData.stationId,
    userId,
  });

  res.status(httpStatus.CREATED).json({
    status: true,
    data: result,
    message: 'Charger added successfully',
  });
});

const getStationChargers = catchAsync(async (req, res) => {
  const {stationId} = req.params;

  const result = await stationService.getStationChargers(stationId);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Station chargers fetched successfully',
  });
});

const deleteCharger = catchAsync(async (req, res) => {
  const {chargerId} = req.params;
  const userId = req.user.id;
  await stationService.deleteCharger(chargerId, userId);

  res.status(httpStatus.OK).json({
    status: true,
    message: 'Charger deleted successfully',
  });
});

const getCharger = catchAsync(async (req, res) => {
  const charger = await stationService.getChargerById(req.params.chargerId);
  res.status(httpStatus.OK).json({
    status: true,
    data: charger,
    message: 'Charger details fetched successfully',
  });
});

const blockStation = catchAsync(async (req, res) => {
  const {startDate, endDate, reason} = req.body;
  const {stationId} = req.params;
  const userId = req.user.id;

  const stationBlock = await stationService.blockStation(stationId, userId, {
    startDate,
    endDate,
    reason,
    createdBy: userId,
  });

  res.status(httpStatus.CREATED).send(stationBlock);
});

const getStationBlocks = catchAsync(async (req, res) => {
  const {stationId} = req.params;
  const result = await stationService.getStationBlocks(stationId);
  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Station block dates fetched successfully',
  });
});

const removeStationBlock = catchAsync(async (req, res) => {
  const {blockId} = req.params;
  const userId = req.user.id;
  await stationService.removeStationBlock(blockId, userId);

  res.status(httpStatus.OK).json({
    status: true,
    message: 'Station Block dates removed successfully',
  });
});

const getConnectors = catchAsync(async (req, res) => {
  console.log('getConnect');
  const connectors = await stationService.getConnectors();
  res.status(httpStatus.OK).json({
    status: true,
    data: connectors,
    message: 'Connectors fetched successfully',
  });
});

const getPowers = catchAsync(async (req, res) => {
  const powers = await stationService.getPowers();
  res.status(httpStatus.OK).json({
    status: true,
    data: powers,
    message: 'Powers fetched successfully',
  });
});

const updateStation = catchAsync(async (req, res) => {
  const {stationId} = req.params;
  const userId = req.user.id;
  const {stationName, city, pinCode, state, address, latitude, longitude, operationalSchedule, amenities, phoneNumber} = req.body;

  const result = await stationService.updateStation(stationId, {
    stationName,
    city,
    pinCode,
    state,
    address,
    latitude,
    longitude,
    operationalSchedule,
    amenities,
    userId,
    files: req.files, // Images from Multer
    phoneNumber
  });

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Station updated successfully',
  });
});

const toggleChargerStatus = catchAsync(async (req, res) => {
  const {chargerId} = req.params;
  const {isEnabled, reason} = req.body;
  const userId = req.user.id;

  const result = await stationService.toggleChargerStatus(chargerId, userId, isEnabled, reason);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Charger enabled/disabled successfully',
  });
});

const getStationVisitors = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const {stationId} = req.params;
  const {page = 1, limit = 10, timeFilter = 'all'} = req.query;

  logger.info('Fetching station visitors', {
    stationId,
    userId,
    filters: {
      timeFilter,
    },
  });

  const result = await stationService.getStationVisitors(stationId, userId, {
    page,
    limit,
    timeFilter,
  });

  logger.info('Station visitors fetched successfully', {
    stationId,
    visitorCount: result.visitors.length,
  });

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Station visitors fetched successfully',
  });
});

const checkStationCompatibility = catchAsync(async (req, res) => {
  const {vehicleId, stationId} = req.params;

  const result = await stationService.checkStationCompatibility(vehicleId, stationId);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: result.message,
  });
});

module.exports = {
  addStation,
  getAmenities,
  getStations,
  addChargerToStation,
  getStationChargers,
  deleteCharger,
  blockStation,
  getStationBlocks,
  removeStationBlock,
  toggleStationStatus,
  getStationById,
  getConnectors,
  getPowers,
  getCharger,
  updateStation,
  toggleChargerStatus,
  getStationVisitors,
  checkStationCompatibility,
};
