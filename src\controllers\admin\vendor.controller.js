const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const vendorService = require('../../services/admin/vendor.service');
const userService = require('../../services/admin/user.service');
const {changePassword} = require('../../services/auth.service');
const logger = require('../../config/logger');

/**
 * Create a vendor account
 * Either creates a new user in Firebase + vendor record, or adds vendor details to existing user
 */
const createVendor = catchAsync(async (req, res) => {
  const {email, password, ...additionalData} = req.body;

  // Create or get existing Firebase user
  const user = await vendorService.createVendorWithUser(email, password, additionalData);

  res.status(httpStatus.CREATED).json({
    status: true,
    data: user,
    message: 'Vendor account created successfully',
  });
});




/**
 * Get all vendors with optional filters
 */
const getVendors = catchAsync(async (req, res) => {
  const { 
    page, 
    limit, 
    sortBy,
    name, 
    email, 
    phone, 
    isActive, 
    fromDate, 
    toDate,
    businessName,
    subscriptionStatus
  } = req.query;

  logger.info('Admin requesting vendors list with filters', { 
    adminId: req.admin.id,
    filters: { 
      page, limit, sortBy, name, email, phone, isActive, 
      fromDate, toDate, businessName, subscriptionStatus
    }
  });

  const filters = {
    page,
    limit,
    sortBy,
    name,
    email,
    phone,
    isActive,
    fromDate,
    toDate,
    businessName,
    subscriptionStatus
  };

  const result = await vendorService.getAllVendors(filters);

  logger.info(`Retrieved ${result.vendors.length} vendors out of ${result.pagination.totalItems} total`);

  res.status(httpStatus.OK).send({
    status: true,
    data: result.vendors,
    pagination: result.pagination,
    message: 'Vendors fetched successfully',
  });
});

/*
Get vendor stations
*/
const getVendorStations = catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page, limit } = req.query;

  const result = await vendorService.getVendorStations(userId, page, limit);

  res.status(httpStatus.OK).send({
    status: true,
    data: result.stations,
    pagination: result.pagination,
    message: 'Vendor stations fetched successfully',
  });
});

/*
Get station details
*/
const getStationDetails = catchAsync(async (req, res) => {
  const { stationId } = req.params;

  const station = await vendorService.getStationDetails(stationId);

  res.status(httpStatus.OK).send({
    status: true,
    data: station,
    message: 'Station details fetched successfully',
  });
});


/*
Add charger to station
*/
const addChargerToStation = catchAsync(async (req, res) => {
  const { stationId } = req.params;
  const chargerData = req.body;

  const result = await vendorService.addChargerToStation(chargerData, stationId);

  res.status(httpStatus.CREATED).send({
    status: true,
    data: result,
    message: 'Charger added successfully',
  });
});

/**
 * Delete a vendor
 */
const deleteVendor = catchAsync(async (req, res) => {
  const {userId} = req.params;

  logger.info(`Admin ${req.admin.id} is deleting vendor with userId ${userId}`);

  const deletedUser = await userService.deleteUser(userId);

  res.status(httpStatus.OK).json({
    status: true,
    data: deletedUser,
    message: 'User deleted successfully',
  });
});

const resetPassword = catchAsync(async (req, res) => {
  const {userId, email, oldPassword, newPassword} = req.body;

  const result = await changePassword(userId, email, oldPassword, newPassword);

  return res.status(httpStatus.OK).json({status: true, data: result, message: 'Password changed successfully.'});
});

/**
 * Toggle user active status
 */
const toggleVendorStatus = catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { isActive, suspendedReason, suspendedComment } = req.body;

  logger.info(`Admin ${req.admin.id} is toggling status of vendorfor user ${userId} to ${isActive}`);

  const updatedUser = await userService.toggleUserStatus(
    userId,
    isActive,
    suspendedReason,
    suspendedComment
  );

  res.status(httpStatus.OK).json({
    status: true,
    data: updatedUser,
    message: `Vendor ${isActive ? 'activated' : 'suspended'} successfully`,
  });
});

 
/**
 * Get vendor statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Vendor statistics
 * @throws {Error} If something goes wrong while fetching vendor statistics
 */
const getVendorStats = catchAsync(async (req, res) => {
  logger.info('Admin requesting vendor statistics', { adminId: req.admin.id });
  
  const stats = await vendorService.getVendorStats();
  res.status(httpStatus.OK).json({
    status: true,
    data: stats,
    message: 'Vendor statistics fetched successfully'
  });
});

/**
 * Get vendor details by userId
 */
const getVendorById = catchAsync(async (req, res) => {
  const { userId } = req.params;
  
  logger.info(`Admin ${req.admin.id} requesting vendor details for userId: ${userId}`);
  
  const vendor = await vendorService.getVendorById(userId);
  
  res.status(httpStatus.OK).send({
    status: true,
    data: vendor,
    message: 'Vendor details fetched successfully',
  });
});

/**
 * Get vendor business details by userId
 */
const getVendorBusiness = catchAsync(async (req, res) => {
  const { userId } = req.params;
  
  logger.info(`Admin ${req.admin.id} requesting vendor business details for userId: ${userId}`);
  
  const businessDetails = await vendorService.getVendorBusiness(userId);
  
  res.status(httpStatus.OK).send({
    status: true,
    data: businessDetails,
    message: 'Vendor business details fetched successfully',
  });
});

module.exports = {
   getVendors,
  createVendor,
  getVendorStations,
  getStationDetails,
  addChargerToStation,
  deleteVendor,
  resetPassword,
  toggleVendorStatus,
  getVendorStats,
  getVendorById,
  getVendorBusiness,
};

