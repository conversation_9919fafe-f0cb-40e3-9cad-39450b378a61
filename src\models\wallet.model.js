const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Wallet = sequelize.define(
  'Wallet',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    ownerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID of the owner (user or vendor)',
    },
    ownerType: {
      type: DataTypes.ENUM('user', 'vendor'),
      allowNull: false,
      comment: 'Type of the owner (user or vendor)',
    },
    balance: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      validate: {
        min: 0, // Prevent negative balance
      },
    },
    dueAmount: {
      type: DataTypes.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Pending penalties or charges',
    },
    currency: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'USD',
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    lastUpdated: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
    },
    version: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 1,
      comment: 'Used for optimistic locking to prevent race conditions',
    },
  },
  {
    tableName: 'wallets',
    timestamps: true,
    indexes: [
      {
        unique: true,
        fields: ['ownerId', 'ownerType'],
        name: 'wallet_owner_unique',
      },
    ],
    hooks: {
      beforeUpdate: (wallet) => {
        wallet.version += 1;
        wallet.lastUpdated = new Date();
      },
    },
  }
);

// Custom method to update balance with optimistic locking
Wallet.prototype.updateBalanceWithLock = async function (amount, dueAmount = 0, transaction) {
  const ApiError = require('../utils/ApiError');
  const httpStatus = require('http-status');

  // Validate inputs
  if (typeof amount !== 'number' || typeof dueAmount !== 'number') {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount and dueAmount must be numbers');
  }

  // Calculate new values
  const newBalance = Number(this.balance) + amount;
  const newDueAmount = Math.max(Number(this.dueAmount) + dueAmount, 0);

  // Validate new balance
  if (newBalance < 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Insufficient balance');
  }

  // Update with version check to ensure no concurrent updates
  const [affectedRows] = await Wallet.update(
    {
      balance: newBalance,
      dueAmount: newDueAmount,
      version: this.version + 1,
      lastUpdated: new Date(),
    },
    {
      where: {
        id: this.id,
        version: this.version,
      },
      transaction,
    }
  );

  if (affectedRows === 0) {
    throw new ApiError(httpStatus.CONFLICT, 'Wallet was updated by another transaction. Please retry.');
  }

  // Refresh the instance with updated data
  const updatedWallet = await Wallet.findByPk(this.id, { transaction });
  Object.assign(this, {
    balance: updatedWallet.balance,
    dueAmount: updatedWallet.dueAmount,
    version: updatedWallet.version,
    lastUpdated: updatedWallet.lastUpdated,
  });

  return this;
};


// Method to get penalty history
Wallet.prototype.getPenaltyHistory = async function (options = {}) {
  const WalletTransaction = require('./walletTransaction.model');
  return await WalletTransaction.findAll({
    where: {
      walletId: this.id,
      type: 'penalty',
      ...options
    },
    order: [['createdAt', 'DESC']]
  });
};

// Method to get total penalties
Wallet.prototype.getTotalPenalties = async function (options = {}) {
  const WalletTransaction = require('./walletTransaction.model');
  const result = await WalletTransaction.sum('amount', {
    where: {
      walletId: this.id,
      type: 'penalty',
      status: 'completed',
      ...options
    }
  });
  return Math.abs(result || 0); // Return positive number
};

// Method to check for pending penalties
Wallet.prototype.hasPendingPenalties = async function () {
  return this.dueAmount > 0;
};

module.exports = Wallet;
