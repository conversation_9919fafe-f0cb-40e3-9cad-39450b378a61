{"name": "futr", "version": "1.0.0", "description": "ev.iq-backend", "main": "index.js", "scripts": {"dev": "nodemon src/index.js", "start": "node src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "format": "prettier --write \"src/**/*.js\""}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.350.0", "@aws-sdk/s3-request-presigner": "^3.350.0", "@first-iraqi-bank/sdk": "^0.1.2", "@sendgrid/mail": "^8.1.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.1.4", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "firebase": "^10.7.1", "firebase-admin": "^11.9.0", "helmet": "^7.0.0", "http-status": "^1.6.2", "joi": "^17.9.2", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^7.2.2", "mongoose-unique-validator": "^4.0.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.9.1", "nodemon": "^3.0.1", "notifme-sdk": "^1.16.25", "pg": "^8.13.1", "pg-hstore": "^2.3.4", "sequelize": "^6.37.5", "sequelize-cli": "^6.6.2", "twilio": "^4.11.2", "uuid": "^9.0.0", "validator": "^13.9.0", "winston": "^3.9.0"}, "devDependencies": {"prettier": "^1.19.1", "prettier-airbnb-config": "^1.0.0"}}