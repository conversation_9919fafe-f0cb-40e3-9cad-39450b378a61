const { ChargingSession, MeterValue, SessionEvent, Transaction, Charger, ChargerConnector } = require('../models/index');
const { Op } = require('sequelize');
const sequelize = require('../config/database');

class SessionManager {
    constructor() {
        this.activeSessions = new Map(); // In-memory cache for active sessions
    }

    /**
     * Start a new charging session
     * @param {Object} params - Session parameters
     * @param {string} params.chargePointId - OCPP Charge Point ID
     * @param {number} params.connectorId - Connector ID
     * @param {string} params.idTag - RFID tag or user identifier
     * @param {number} params.meterStart - Starting meter value
     * @param {number} params.userId - Optional user ID
     * @returns {Object} Session and transaction data
     */
    async startSession(params) {
        const { chargePointId, connectorId, idTag, meterStart, userId = null } = params;

        try {
            // Find the charger
            const charger = await Charger.findOne({
                where: { chargerId: chargePointId },
                include: [{
                    model: ChargerConnector,
                    as: 'connectors',
                    where: { connectorNumber: connectorId }
                }]
            });

            if (!charger) {
                throw new Error(`Charger ${chargePointId} not found`);
            }

            const connector = charger.connectors[0];
            if (!connector) {
                throw new Error(`Connector ${connectorId} not found on charger ${chargePointId}`);
            }

            // Check if connector is available
            if (connector.status !== 'Available') {
                throw new Error(`Connector ${connectorId} is not available. Current status: ${connector.status}`);
            }

            // Generate transaction ID
            const transactionId = await this.generateTransactionId();

            // Create charging session
            const session = await ChargingSession.create({
                chargerId: charger.id,
                connectorId: connector.id,
                transactionId,
                idTag,
                userId,
                status: 'Starting',
                startTime: new Date(),
                startMeterValue: meterStart,
                lastActivity: new Date()
            });

            // Create OCPP transaction
            const transaction = await Transaction.create({
                transactionId,
                sessionId: session.id,
                chargerId: charger.id,
                connectorId: connectorId,
                idTag,
                startTimestamp: new Date(),
                meterStart
            });

            // Update connector status
            await connector.update({
                status: 'Preparing',
                lastStatusUpdate: new Date()
            });

            // Log session event
            await this.logSessionEvent(session.id, 'SessionStarted', {
                transactionId,
                idTag,
                meterStart,
                chargePointId,
                connectorId
            });

            // Cache active session
            // this.activeSessions.set(session.sessionId, {
            //     sessionId: session.id,
            //     transactionId,
            //     chargerId: charger.id,
            //     connectorId: connector.id,
            //     status: 'Starting',
            //     startTime: session.startTime
            // });

            return {
                success: true,
                sessionId: session.sessionId,
                transactionId,
                session,
                transaction
            };

        } catch (error) {
            console.error('Error starting session:', error);
            throw error;
        }
    }

    /**
     * Stop a charging session
     * @param {Object} params - Stop parameters
     * @param {number} params.transactionId - OCPP Transaction ID
     * @param {number} params.meterStop - Ending meter value
     * @param {string} params.stopReason - Reason for stopping
     * @param {Array} params.transactionData - Optional meter values
     * @returns {Object} Session stop result
     */
    async stopSession(params) {
        const { transactionId, meterStop, stopReason = 'Local', transactionData = [] } = params;

        try {
            // Find the transaction and session
            const transaction = await Transaction.findOne({
                where: { transactionId },
                include: [{
                    model: ChargingSession,
                    as: 'session',
                    include: [{
                        model: ChargerConnector,
                        as: 'connector'
                    }]
                }]
            });

            if (!transaction) {
                throw new Error(`Transaction ${transactionId} not found`);
            }

            const session = transaction.session;
            const connector = session.connector;

            // Calculate session metrics
            const endTime = new Date();
            const duration = Math.floor((endTime - session.startTime) / 1000); // seconds
            const energyConsumed = meterStop - session.startMeterValue;

            // Update session
            await session.update({
                status: 'Completed',
                endTime,
                endMeterValue: meterStop,
                energyConsumed,
                duration,
                stopReason,
                lastActivity: endTime
            });

            // Update transaction
            await transaction.update({
                stopTimestamp: endTime,
                meterStop,
                stopReason
            });

            // Update connector status
            await connector.update({
                status: 'Available',
                lastStatusUpdate: endTime
            });

            // Store transaction data (meter values)
            if (transactionData.length > 0) {
                await this.storeMeterValues(session.id, transactionData);
            }

            // Log session event
            await this.logSessionEvent(session.id, 'SessionStopped', {
                transactionId,
                meterStop,
                stopReason,
                duration,
                energyConsumed
            });

            // Remove from active sessions cache
            // this.activeSessions.delete(session.sessionId);

            return {
                success: true,
                sessionId: session.sessionId,
                transactionId,
                energyConsumed,
                duration,
                cost: await this.calculateSessionCost(session.id)
            };

        } catch (error) {
            console.error('Error stopping session:', error);
            throw error;
        }
    }

    /**
     * Update session status
     * @param {string} sessionId - Session UUID
     * @param {string} status - New status
     * @param {Object} additionalData - Additional data
     */
    async updateSessionStatus(sessionId, status, additionalData = {}) {
        try {
            const session = await ChargingSession.findOne({
                where: { sessionId }
            });

            if (!session) {
                throw new Error(`Session ${sessionId} not found`);
            }

            await session.update({
                status,
                lastActivity: new Date()
            });

            // Update cache
            if (this.activeSessions.has(sessionId)) {
                const cachedSession = this.activeSessions.get(sessionId);
                cachedSession.status = status;
                this.activeSessions.set(sessionId, cachedSession);
            }

            // Log event
            await this.logSessionEvent(session.id, 'StatusChanged', {
                oldStatus: session.status,
                newStatus: status,
                ...additionalData
            });

            return { success: true };

        } catch (error) {
            console.error('Error updating session status:', error);
            throw error;
        }
    }

    /**
     * Get active sessions
     * @param {string} chargePointId - Optional filter by charger
     * @returns {Array} Active sessions
     */
    async getActiveSessions(chargePointId = null) {
        try {
            const whereClause = {
                status: {
                    [Op.in]: ['Starting', 'Active', 'Suspended']
                }
            };

            const includeClause = [
                {
                    model: Charger,
                    as: 'charger'
                },
                {
                    model: ChargerConnector,
                    as: 'connector'
                }
            ];

            if (chargePointId) {
                includeClause[0].where = { chargerId: chargePointId };
            }

            const sessions = await ChargingSession.findAll({
                where: whereClause,
                include: includeClause,
                order: [['startTime', 'DESC']]
            });

            return sessions;

        } catch (error) {
            console.error('Error getting active sessions:', error);
            throw error;
        }
    }

    /**
     * Generate unique transaction ID
     * @returns {number} Transaction ID
     */
    async generateTransactionId() {
        try {
            // Get a model to access sequelize
            const model = ChargingSession;
            
            // Use the sequelize instance from the model
            const [result] = await model.sequelize.query(
                "SELECT nextval('transaction_id_seq')",
                { type: model.sequelize.QueryTypes.SELECT }
            );
            
            // Add a base value (e.g., 1000) to the sequence value
            return 1000 + parseInt(result.nextval, 10)
            
        } catch (error) {
            console.error('Error generating transaction ID:', error);
            // Fallback to a timestamp-based ID if sequence fails
            const timestamp = Date.now();
            const random = Math.floor(Math.random() * 1000);
            return 1000000 + (timestamp % 100000) + random;
        }
    }

    /**
     * Log session event
     * @param {number} sessionId - Session ID
     * @param {string} eventType - Event type
     * @param {Object} eventData - Event data
     * @param {string} source - Event source
     */
    async logSessionEvent(sessionId, eventType, eventData = {}, source = 'OCPP') {
        try {
            await SessionEvent.create({
                sessionId,
                eventType,
                eventData,
                source,
                timestamp: new Date()
            });
        } catch (error) {
            console.error('Error logging session event:', error);
        }
    }

    /**
     * Store meter values for a session
     * @param {number} sessionId - Session ID
     * @param {Array} meterValues - Array of meter value objects
     */
    async storeMeterValues(sessionId, meterValues) {
        // This will be implemented in MeterDataService
        // For now, just log
        console.log(`Storing ${meterValues.length} meter values for session ${sessionId}`);
    }

    /**
     * Calculate session cost
     * @param {number} sessionId - Session ID
     * @returns {number} Cost
     */
    async calculateSessionCost(sessionId) {
        // Placeholder for cost calculation logic
        // This would integrate with pricing rules
        return 0.00;
    }

    /**
     * Get session by transaction ID
     * @param {number} transactionId - Transaction ID
     * @returns {Object} Session data
     */
    async getSessionByTransactionId(transactionId) {
        try {
            const transaction = await Transaction.findOne({
                where: { transactionId },
                include: [{
                    model: ChargingSession,
                    as: 'session',
                    include: [
                        { model: Charger, as: 'charger' },
                        { model: ChargerConnector, as: 'connector' }
                    ]
                }]
            });

            return transaction ? transaction.session : null;

        } catch (error) {
            console.error('Error getting session by transaction ID:', error);
            throw error;
        }
    }

    /**
     * Clean up inactive sessions
     * Mark sessions as faulted if no activity for specified time
     */
    async cleanupInactiveSessions(timeoutMinutes = 30) {
        try {
            const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);

            const inactiveSessions = await ChargingSession.findAll({
                where: {
                    status: {
                        [Op.in]: ['Starting', 'Active', 'Suspended']
                    },
                    lastActivity: {
                        [Op.lt]: cutoffTime
                    }
                }
            });

            for (const session of inactiveSessions) {
                await this.updateSessionStatus(session.sessionId, 'Faulted', {
                    reason: 'Session timeout',
                    timeoutMinutes
                });

                // Remove from cache
                this.activeSessions.delete(session.sessionId);
            }

            console.log(`Cleaned up ${inactiveSessions.length} inactive sessions`);
            return inactiveSessions.length;

        } catch (error) {
            console.error('Error cleaning up inactive sessions:', error);
            throw error;
        }
    }
}

module.exports = SessionManager;




