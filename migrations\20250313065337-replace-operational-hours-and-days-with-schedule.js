'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('stations', 'operationalSchedule', {
      type: Sequelize.JSONB,
      allowNull: false,
      defaultValue: {},
  });

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */

  // Drop the new operationalSchedule column
  await queryInterface.removeColumn('stations', 'operationalSchedule');
  }
};
