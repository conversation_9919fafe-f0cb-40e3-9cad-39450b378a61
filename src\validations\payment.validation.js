const Joi = require('joi');

const initializePayment = {
  body: Joi.object().keys({
    provider: Joi.string()
      .required()
      .valid('zaincash', 'fib') // Add more providers as they become available
      .messages({
        'string.empty': 'Payment provider is required',
        'any.required': 'Payment provider is required',
        'any.only': 'Unsupported payment provider',
      }),
    walletId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Wallet ID must be a number',
        'number.integer': 'Wallet ID must be an integer',
        'any.required': 'Wallet ID is required',
      }),
    amount: Joi.number()
      .positive()
      .required()
      .messages({
        'number.base': 'Amount must be a number',
        'number.positive': 'Amount must be positive',
        'any.required': 'Amount is required',
      }),
  }),
};

const handleZaincashCallback = {
  query: Joi.object().keys({
    token: Joi.string()
      .required()
      .messages({
        'string.empty': 'Payment token is required',
        'any.required': 'Payment token is required',
      }),
  }),
};

const handleFibCallback = {
  body: Joi.object().keys({
    id: Joi.string()
      .required()
      .messages({
        'string.empty': 'Payment ID is required',
        'any.required': 'Payment ID is required',
      }),
    status: Joi.object().required().messages({
      'object.base': 'Status object is required',
      'any.required': 'Status object is required',
    }),
    'status.status': Joi.string()
      .valid('UNPAID', 'DECLINED', 'PAID', 'REFUNDED', 'REFUND_REQUESTED')
      .messages({
        'any.only': 'Invalid payment status',
      }),
    'status.amount': Joi.object().optional(),
    'status.paidAt': Joi.string().optional(),
    'status.decliningReason': Joi.string().optional(),
    'status.declinedAt': Joi.string().optional(),
    'status.paidBy': Joi.object().optional(),
  }),
};

const handleFibRedirect = {
  query: Joi.object().keys({
    paymentId: Joi.string()
      .required()
      .messages({
        'string.empty': 'Payment ID is required',
        'any.required': 'Payment ID is required',
      }),
  }),
};

module.exports = {
  initializePayment,
  handleZaincashCallback,
  handleFibCallback,
  handleFibRedirect
};
