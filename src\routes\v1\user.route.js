const express = require('express');
const router = express.Router();

const validate = require('../../middlewares/validate');
const { firebaseAuth, generateToken } = require('../../middlewares/firebaseAuth');
const authValidation = require('../../validations/auth.validation');
const bookmarkController = require('../../controllers/bookmark.controller');
const bookmarkValidation = require('../../validations/bookmark.validation');
const { userController } = require('../../controllers');
const { fileUploadService } = require('../../microservices');

router.post('/update',  fileUploadService.handleMulterErrors('image', 1, true), firebaseAuth, userController.updateUser);
router.get('/details', firebaseAuth, userController.getDetails);

// Add a bookmark
router.post('/users/bookmarks/:stationId', firebaseAuth, validate(bookmarkValidation.addBookmark), bookmarkController.addBookmark);

// Remove a bookmark
router.delete(
    '/users/bookmarks/:stationId',
    firebaseAuth,
    validate(bookmarkValidation.removeBookmark),
    bookmarkController.removeBookmark
);

// Get all bookmarked stations
router.get('/users/bookmarks', firebaseAuth, validate(bookmarkValidation.getBookmarks), bookmarkController.getBookmarks);

module.exports = router;
