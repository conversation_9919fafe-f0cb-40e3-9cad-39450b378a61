const Joi = require('joi');
const { isBefore } = require('date-fns');

// Reusable validation for arrivalTime
const arrivalTimeValidation = Joi.string()
    .required()
    .custom((value, helpers) => {
        const parsedDate = new Date(value);

        // Check if the parsed date is valid
        if (!(parsedDate instanceof Date) || isNaN(parsedDate.getTime())) {
            return helpers.error('date.iso'); // Invalid format
        }

        // Check if arrivalTime is in the past
        if (isBefore(parsedDate, new Date())) {
            return helpers.error('date.min'); // Cannot be in the past
        }

        return value; // Return the original value if valid
    })
    .messages({
        'string.empty': 'Arrival time is required',
        'any.required': 'Arrival time is required',
        'date.base': 'Arrival time must be a valid date',
        'date.iso': 'Arrival time must be in ISO 8601 format (e.g., YYYY-MM-DDTHH:mm:ssZ)',
        'date.min': 'Arrival time cannot be in the past',
    });

// Reusable validation for duration
const durationValidation = Joi.number()
    .integer()
    .min(60) // Minimum duration is 60 minutes
    .required()
    .messages({
        'number.base': 'Duration must be a number',
        'number.integer': 'Duration must be an integer',
        'number.min': 'Duration must be at least 60 minutes',
        'any.required': 'Duration is required',
    });

const getStationBookingHistory = {
    params: Joi.object().keys({
        stationId: Joi.number().integer().required().messages({
            'number.base': 'Station ID must be a number',
            'number.integer': 'Station ID must be an integer',
            'any.required': 'Station ID is required',
        }),
    }),
    query: Joi.object().keys({
        page: Joi.number()
            .integer()
            .min(1)
            .optional()
            .default(1)
            .messages({
                'number.base': 'Page must be a number',
                'number.integer': 'Page must be an integer',
                'number.min': 'Page must be at least 1',
            }),
        limit: Joi.number()
            .integer()
            .min(1)
            .max(100)
            .default(10)
            .optional()
            .messages({
                'number.base': 'Limit must be a number',
                'number.integer': 'Limit must be an integer',
                'number.min': 'Limit must be at least 1',
                'number.max': 'Limit cannot exceed 100',
            }),
        sortBy: Joi.string()
            .valid('createdAt', 'startTime', 'endTime')
            .default('createdAt')
            .optional()
            .messages({
                'any.only': 'Sort by must be one of: createdAt, startTime, endTime',
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .default('desc')
            .optional()
            .messages({
                'any.only': 'Sort order must be either asc or desc',
            }),
    }),
};

const bookingValidation = {
    bookChargingSlot: {
        body: Joi.object().keys({
            vehicleId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Vehicle ID is required',
                    'any.required': 'Vehicle ID is required',
                }),
            arrivalTime: arrivalTimeValidation,
            duration: durationValidation,
        }),
    },
    getBookingDetails: {
        query: Joi.object().keys({
            status: Joi.string()
                .required()
                .valid('pending', 'upcoming', 'completed', 'cancelled')
                .messages({
                    'any.only': 'Status must be one of: pending, upcoming, completed, cancelled',
                    'any.required': 'Status is required',
                }),
            page: Joi.number()
                .integer()
                .min(1)
                .optional()
                .default(1)
                .messages({
                    'number.base': 'Page must be a number',
                    'number.integer': 'Page must be an integer',
                    'number.min': 'Page must be at least 1',
                }),
            limit: Joi.number()
                .integer()
                .min(1)
                .max(100)
                .default(10)
                .optional()
                .messages({
                    'number.base': 'Limit must be a number',
                    'number.integer': 'Limit must be an integer',
                    'number.min': 'Limit must be at least 1',
                    'number.max': 'Limit cannot exceed 100',
                }),
        }),
    },
    getBookingDetailsById: {
        params: Joi.object().keys({
            bookingId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Booking ID is required',
                    'any.required': 'Booking ID is required',
                }),
        }),
    },
    cancelBooking: {
        body: Joi.object().keys({
            cancellationReason: Joi.string()
                .required()
                .max(255)
                .messages({
                    'string.empty': 'Cancellation reason is required',
                    'any.required': 'Cancellation reason is required',
                    'string.max': 'Cancellation reason must be less than 255 characters',
                }),
        }),
        params: Joi.object().keys({
            bookingId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Booking ID is required',
                    'any.required': 'Booking ID is required',
                }),
        }),
    },
    changeTimeSlot: {
        params: Joi.object().keys({
            bookingId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Booking ID is required',
                    'any.required': 'Booking ID is required',
                }),
        }),
        body: Joi.object().keys({
            arrivalTime: arrivalTimeValidation,
            duration: durationValidation,
            timeSlotChangeReason: Joi.string()
                .required()
                .max(255)
                .messages({
                    'string.empty': 'Time slot change reason is required',
                    'any.required': 'Time slot change reason is required',
                    'string.max': 'Time slot change reason must be less than 255 characters',
                }),
        }),
    },
    getAvailableTimeSlots: {
        params: Joi.object().keys({
            stationId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Station ID is required',
                    'any.required': 'Station ID is required',
                }),
            chargerId: Joi.string()
                .optional()
                .messages({
                    'string.empty': 'Charger ID cannot be empty',
                }),
        }),
        query: Joi.object().keys({
            date: Joi.string()
                .required()
                .pattern(/^\d{4}-\d{2}-\d{2}$/)
                .messages({
                    'string.pattern.base': 'Date must be in YYYY-MM-DD format',
                    'string.empty': 'Date is required',
                    'any.required': 'Date is required',
                }),
        }),
    },
    confirmBooking:{
        params: Joi.object().keys({
            bookingId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Booking ID is required',
                    'any.required': 'Booking ID is required',
                }),
        })
    },
    getStationBookingHistory:{
        params: Joi.object().keys({
            stationId: Joi.string()
                .required()
                .messages({
                    'string.empty': 'Station ID is required',
                    'any.required': 'Station ID is required',
                }),
        }),
        query: Joi.object().keys({
            page: Joi.number()
                .integer()
                .min(1)
                .optional()
                .default(1)
                .messages({
                    'number.base': 'Page must be a number',
                    'number.integer': 'Page must be an integer',
                    'number.min': 'Page must be at least 1',
                }),
            limit: Joi.number()
                .integer()
                .min(1)
                .max(100)
                .default(10)
                .optional()
                .messages({
                    'number.base': 'Limit must be a number',
                    'number.integer': 'Limit must be an integer',
                    'number.min': 'Limit must be at least 1',
                    'number.max': 'Limit cannot exceed 100',
                }),
                timeFilter: Joi.string()
                  .valid('all', '3months', '6months')
                  .default('all')
                  .optional()
                  .messages({
                    'any.only': 'Time filter must be one of: all, 3months, 6months',
                  }),
            }),
            },
};

module.exports = bookingValidation;