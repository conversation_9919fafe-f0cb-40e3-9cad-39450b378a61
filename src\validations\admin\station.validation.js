const Joi = require('joi');

const getStations = {
  query: Joi.object().keys({
    search: Joi.string().trim().allow(''),
    status: Joi.string().valid('pending', 'approved', 'rejected').allow(''),
    sortBy: Joi.string().valid('createdAt', 'stationName').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10)
  })
};

const approveStation = {
  params: Joi.object().keys({
    stationId: Joi.number().required()
  })
};

const getStationById = {
  params:Joi.object().keys({
    stationId: Joi.number().required()
  })
}

const getChargerById = {
  params:Joi.object().keys({
    stationId: Joi.number().required(),
    chargerId: Joi.number().required()
  })
}

const getBookingHistory = {
  params:Joi.object().keys({
    stationId: Joi.number().required()
  }),
   query: Joi.object().keys({
      page: Joi.number()
        .integer()
        .min(1)
        .optional()
        .default(1)
        .messages({
          'number.base': 'Page must be a number',
          'number.integer': 'Page must be an integer',
          'number.min': 'Page must be at least 1',
        }),
      limit: Joi.number()
        .integer()
        .min(1)
        .max(100)
        .default(10)
        .optional()
        .messages({
          'number.base': 'Limit must be a number',
          'number.integer': 'Limit must be an integer',
          'number.min': 'Limit must be at least 1',
          'number.max': 'Limit cannot exceed 100',
        })
    }),
}

const getBlockDates = {
  params:Joi.object().keys({
    stationId: Joi.number().required()
  })
}

const getBookingDetail = {
  params:Joi.object().keys({
    stationId: Joi.number().required(),
    bookingId: Joi.number().required()
  })
}

const toggleStationStatus = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    isEnabled: Joi.boolean().required(),
    reason: Joi.when('isEnabled', {
      is: false, // Only apply these rules when isEnabled is false
      then: Joi.string().min(5).required().messages({
        'string.empty': 'Reason is required when disabling a station',
        'string.min': 'Reason must be at least 5 characters long',
        'any.required': 'Reason is required when disabling a station'
      }),
      otherwise: Joi.string().allow(null, '').optional() // Optional when enabling
    })
  })
};

module.exports = {
  getStations,
  approveStation,
  getStationById,
  getChargerById,
  getBookingHistory,
  getBookingDetail,
  getBlockDates,
  toggleStationStatus
};  