const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const SubscriptionPlan  = require('../models/admin/subscriptionPlan.model');

const getActiveSubscriptionPlans = catchAsync(async (req, res) => {
  const subscriptionPlans = await SubscriptionPlan.findAll({
    where: {
      isActive: true
    },
    order: [
      ['displayOrder', 'ASC'],
      ['price', 'ASC']
    ],
    attributes: {
      exclude: ['createdAt', 'updatedAt','displayOrder']
    }
  });

  res.status(httpStatus.OK).send({
    status: 'success',
    data: subscriptionPlans,
    message: 'Active subscription plans fetched successfully'
  });
});

module.exports = {
  getActiveSubscriptionPlans,
}; 