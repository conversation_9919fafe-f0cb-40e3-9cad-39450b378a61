'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('stations', 'city', {
      type: Sequelize.STRING,
      allowNull: false,
  });

  await queryInterface.addColumn('stations', 'pincode', {
      type: Sequelize.STRING,
      allowNull: false,
  });

  await queryInterface.addColumn('stations', 'address', {
      type: Sequelize.STRING,
      allowNull: false,
  });
  await queryInterface.addColumn('stations', 'state', {
    type: Sequelize.STRING,
    allowNull: false,
});
// await queryInterface.addColumn('user_vehicles', 'createdAt', {
//   type: Sequelize.DATE,
//   allowNull: false,
//   defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
// });

// await queryInterface.addColumn('user_vehicles', 'updatedAt', {
//   type: Sequelize.DATE,
//   allowNull: false,
//   defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
// });

// ✅ Indexes for faster queries (optional but recommended)
await queryInterface.addIndex('stations', ['city']);
await queryInterface.addIndex('stations', ['pincode']);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeIndex('stations', ['city']);
    await queryInterface.removeIndex('stations', ['pincode']);

    await queryInterface.removeColumn('stations', 'city');
    await queryInterface.removeColumn('stations', 'pincode');
    await queryInterface.removeColumn('stations', 'address');
    await queryInterface.removeColumn('stations', 'state');

    // await queryInterface.removeColumn('user_vehicles', 'createdAt');
    // await queryInterface.removeColumn('user_vehicles', 'updatedAt');
  }
};
