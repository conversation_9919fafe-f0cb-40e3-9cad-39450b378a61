'use strict';

const moment = require('moment');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    const now = moment.utc();
    
    await queryInterface.bulkInsert('vendor_subscriptions', [
      {
        vendorId: 1,
        subscriptionPlanId: 1,
        startDate: now.toDate(),
        endDate: now.clone().add(30, 'days').toDate(), // 1 month later
        isActive: true,
        autoRenew: true,
        inGracePeriod: false,
        renewedFromId: null,
        renewalAttempts: 0,
        createdAt: now.toDate(),
        updatedAt: now.toDate()
      },
      {
        vendorId: 2,
        subscriptionPlanId: 2,
        startDate: now.clone().subtract(15, 'days').toDate(), // 15 days ago
        endDate: now.clone().add(15, 'days').toDate(), // 15 days later
        isActive: true,
        autoRenew: false,
        inGracePeriod: false,
        renewedFromId: null,
        renewalAttempts: 0,
        createdAt: now.toDate(),
        updatedAt: now.toDate()
      },
     
    ], {});
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('vendor_subscriptions', null, {});
  }
};
