const {signInWithEmailAndPassword} = require('firebase/auth');
const {auth} = require('../../firebase-info');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
// This function verifies the old password of a user using Firebase Authentication

async function verifyOldPassword(email, password) {
    try {
      await signInWithEmailAndPassword(auth, email, password);
      return true;
    } catch (error) {
      if (error.code === 'auth/invalid-credential' || error.code === 'auth/user-not-found') {
        return false;
      }
      console.error('Password verification error:', error);
      throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to verify password');
    }
  }

  module.exports = {
    verifyOldPassword,
  };