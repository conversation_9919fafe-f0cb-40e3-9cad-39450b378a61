'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const [users] = await queryInterface.sequelize.query(`SELECT id FROM users LIMIT 1`);
    const [stations] = await queryInterface.sequelize.query(`SELECT id FROM stations LIMIT 1`);
    const [chargers] = await queryInterface.sequelize.query(`SELECT id FROM chargers LIMIT 1`);
    const [user_vehicles] = await queryInterface.sequelize.query(`SELECT id FROM user_vehicles LIMIT 1`);

    if (!users.length || !stations.length || !chargers.length || !user_vehicles.length) {
      throw new Error('Missing foreign key data');
    }

    const userId = users[0].id;
    const stationId = stations[0].id;
    const chargerId = chargers[0].id;
    const vehicleId = user_vehicles[0].id;

    const now = new Date();
    const bookings = [];

    for (let i = 0; i < 10; i++) {
      const startTime = new Date(now.getTime() + i * 3600000); // +i hours
      const endTime = new Date(startTime.getTime() + 3600000); // +1 hour
      const baseBooking = {
        userId,
        stationId,
        chargerId,
        vehicleId,
        startTime,
        endTime,
        totalCost: 10 + i,
        paymentStatus: 'paid',
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      if (i < 3) {
        // Completed
        bookings.push({
          ...baseBooking,
          status: 'completed',
        });
      } else if (i < 6) {
        // Upcoming
        bookings.push({
          ...baseBooking,
          status: 'upcoming',
        });
      } else if (i === 6 || i === 7) {
        // Cancelled by user
        bookings.push({
          ...baseBooking,
          status: 'cancelled',
          cancellationReason: 'User changed plans',
          cancelledBy: 'user',
          cancellationTime: new Date(startTime.getTime() - 1800000), // 30 min before
          refundAmount: 5.0,
        });
      } else {
        // Cancelled by vendor
        bookings.push({
          ...baseBooking,
          status: 'cancelled',
          cancellationReason: 'Station maintenance',
          cancelledBy: 'vendor',
          cancellationTime: new Date(startTime.getTime() - 3600000), // 1 hour before
          refundAmount: 10.0,
        });
      }
    }

    await queryInterface.bulkInsert('bookings', bookings, {});
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('bookings', null, {});
  }
};
