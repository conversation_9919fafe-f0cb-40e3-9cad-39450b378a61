const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Connector = sequelize.define(
  'connectors',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['chinese', 'american', 'european']]
      }
    }
  },
  {
    tableName: 'connectors',
    timestamps: false
  }
);

module.exports = Connector; 