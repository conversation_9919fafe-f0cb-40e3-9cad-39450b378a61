const catchAsync = require('../../utils/catchAsync');
const { chatService } = require('../../services');
const httpStatus = require('http-status');

/**
 * Send a message as an admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const sendAdminMessage = catchAsync(async (req, res) => {
  const { chatId, message } = req.body;
  const adminId = req.admin.id; // Using admin.id from adminAuth middleware

  const chatMessage = await chatService.sendMessage(chatId, adminId, message, true);
  res.status(httpStatus.CREATED).json({
    status: true,
    data: chatMessage,
    message: 'Admin message sent successfully',
  });
});

/**
 * Get chat messages as an admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAdminMessages = catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const { page = 1, limit = 20 } = req.query;
  const adminId = req.admin.id; // Using admin.id from adminAuth middleware
  
  const messages = await chatService.getChatMessages(chatId, adminId, page, limit,true);
  res.json({
    status: true,
    data: messages,
    message: 'Messages retrieved successfully',
  });
});

/**
 * Close a chat as an admin
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const closeChat = catchAsync(async (req, res) => {
  const { chatId } = req.params;
  const adminId = req.admin.id; // Using admin.id from adminAuth middleware

  const chat = await chatService.closeChat(chatId, adminId);
  res.json({
    status: true,
    data: chat,
    message: 'Chat closed successfully',
  });
});





module.exports = {
  sendAdminMessage,
  getAdminMessages,
  closeChat,
}; 