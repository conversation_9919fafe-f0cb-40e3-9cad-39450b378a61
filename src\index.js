const app = require('./app');
const config = require('./config/config');
const logger = require('./config/logger');
const { sequelize } = require('./config/database');
const {ProductType} = require('./models');
require('./listeners/booking.listener');
let server;

sequelize
  .sync() // Sync Sequelize models with the database
  .then(() => {
    console.log('Connected to MySQL database');
    server = app.listen(config.port, () => {
      console.log(`Futr app listening on port ${config.port}!`);
      // addDummyProductTypes()
    });
  })
  .catch(err => {
    console.error('Unable to connect to the database:', err);
  });

// ------------- Don't Modify  -------------
const exitHandler = () => {
  if (server) {
    server.close(() => {
      logger.info('Server closed');
      process.exit(1);
    });
  } else {
    process.exit(1);
  }
};

const unexpectedErrorHandler = error => {
  logger.error(error);
  exitHandler();
};

process.on('uncaughtException', unexpectedErrorHandler);
process.on('unhandledRejection', unexpectedErrorHandler);

process.on('SIGTERM', () => {
  logger.info('SIGTERM received');
  if (server) {
    server.close();
  }
});
// ------------- Don't Modify  -------------

async function addDummyProductTypes() {
  try {
    // Dummy data for product types
    const dummyProductTypes = [
      { name: 'Type 1', icon: 'icon1.png' },
      { name: 'Type 2', icon: 'icon2.png' },
      { name: 'Type 3', icon: 'icon3.png' }
    ];

    // Bulk insert the dummy product types
    await ProductType.bulkCreate(dummyProductTypes);
    console.log('Dummy product types added successfully.');
  } catch (error) {
    console.error('Error adding dummy product types:', error);
  }
}