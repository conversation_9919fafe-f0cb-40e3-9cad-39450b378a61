const express = require('express');
const router = express.Router();
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const {fileUploadService} = require('../../microservices');
const {stationController, reviewController} = require('../../controllers');
const stationValidation = require('../../validations/station.validation');

router.get('/amenities', stationController.getAmenities);
router.get('/connectors', stationController.getConnectors);
router.get('/powers', stationController.getPowers);

router.get('/', validate(stationValidation.getStations), stationController.getStations);
router
  .route('/chargers/:chargerId(\\d+)')
  .get(firebaseAuth, validate(stationValidation.getCharger), stationController.getCharger);

router.get(
  '/:stationId/chargers',
  firebaseAuth,
  validate(stationValidation.getStationChargers),
  stationController.getStationChargers
);

router.patch(
  '/:stationId/enable-disable',
  firebaseAuth,
  validate(stationValidation.toggleStationStatus),
  stationController.toggleStationStatus
);

router.post(
  '/',
  firebaseAuth,
  fileUploadService.handleMulterErrors('images', 10, false),
  validate(stationValidation.addStation),
  stationController.addStation
);
router.delete(
  '/chargers/:chargerId',
  firebaseAuth,
  validate(stationValidation.deleteCharger),
  stationController.deleteCharger
);

router
  .route('/:stationId(\\d+)')
  .get(firebaseAuth, validate(stationValidation.getStationById), stationController.getStationById)
  .put(
    firebaseAuth,
    fileUploadService.handleMulterErrors('images', 10, false),
    validate(stationValidation.updateStation),
    stationController.updateStation
  );

router.post('/chargers', firebaseAuth, validate(stationValidation.addChargers), stationController.addChargerToStation);
router.patch(
  '/chargers/:chargerId/enable-disable',
  firebaseAuth,
  validate(stationValidation.toggleChargerStatus),
  stationController.toggleChargerStatus
);

// Routes for station blocking
router.get('/:stationId/blocks', validate(stationValidation.getStationBlocks), stationController.getStationBlocks);
router.post(
  '/:stationId/block',
  firebaseAuth,
  validate(stationValidation.blockStation),
  stationController.blockStation
);
router.delete(
  '/blocks/:blockId',
  firebaseAuth,
  validate(stationValidation.removeStationBlock),
  stationController.removeStationBlock
);

// Review Routes for stations
router
  .route('/:stationId/reviews')
  .post(firebaseAuth, validate(stationValidation.addReview), reviewController.addReview);

router
  .route('/:stationId/reviews')
  .get(firebaseAuth, validate(stationValidation.getReviews), reviewController.getReviews);
// router.put('/reviews/:reviewId', reviewController.updateReview);
router.delete(
  '/reviews/:reviewId',
  firebaseAuth,
  validate(stationValidation.getReviews),
  reviewController.deleteReview
);

// Get station visitors
router
  .route('/:stationId/visitors')
  .get(firebaseAuth, validate(stationValidation.getStationVisitors), stationController.getStationVisitors);

//check charger compatiibility with respect to vehicle
router.get(
  '/check-compatibility/:vehicleId/:stationId',
  validate(stationValidation.checkStationCompatibility),
  stationController.checkStationCompatibility
);
module.exports = router;
