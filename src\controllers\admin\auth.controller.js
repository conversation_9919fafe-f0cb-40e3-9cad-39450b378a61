const catchAsync = require('../../utils/catchAsync');
const httpStatus = require('http-status');
const authService = require('../../services/admin/auth.service');


const login = catchAsync(async (req, res) => {
  const { email, password } = req.body;
  const { admin, token } = await authService.login(email, password);

  return res.status(httpStatus.OK).json({ 
    status: true, 
    data: { admin, token },
    message: "Admin logged in successfully." 
  });
});

const createAdmin = catchAsync(async (req, res) => {
  const newAdmin = await authService.createAdmin(req.body, req.admin.role);

  res.status(httpStatus.CREATED).json({
    status: true,
    data: newAdmin,
    message: 'Admin created successfully'
  });
});

module.exports = {
  login,
  createAdmin
}; 
