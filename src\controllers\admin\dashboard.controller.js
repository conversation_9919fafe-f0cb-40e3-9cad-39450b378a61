const dashboardService = require('../../services/admin/dashboard.service');
const catchAsync = require('../../utils/catchAsync');
const httpStatus = require('http-status');
const logger = require('../../config/logger');

/**
 * Get dashboard KPIs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Object} Dashboard KPIs
 * @throws {Error} If something goes wrong while fetching dashboard KPIs
 * @description Get dashboard KPIs
 */
const getDashboardKPIs = catchAsync(async (req, res) => {
  const data = await dashboardService.getDashboardKPIs();
    res.status(httpStatus.OK).json({
    status: true,
    data,
    message: 'Dashboard KPIs fetched successfully'
  });
});

/**
 * Get revenue chart data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Revenue chart data
 * @throws {Error} If something goes wrong while fetching revenue chart data
 * @description Get monthly revenue data for the current year
 */
const getRevenueChartData = catchAsync(async (req, res) => {
  const filterType = req.query.filterType;
  const data = await dashboardService.getRevenueChartData(filterType);
    res.status(httpStatus.OK).json({
    status: true,
    data,
    message: 'Revenue chart data fetched successfully'
  });

});

/**
 * Get overview chart data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Overview chart data
 * @throws {Error} If something goes wrong while fetching overview chart data
 * @description Get daily data for stations, vendors, and users for the current month
 */
const getOverviewChartData = catchAsync(async (req, res) => {
  const filterType = req.query.filterType;
  const data = await dashboardService.getOverviewChartData(filterType);
    res.status(httpStatus.OK).json({
    status: true,
    data,
    message: 'Overview chart data fetched successfully'
  });
});

/**
 * Get users chart data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Users chart data
 * @throws {Error} If something goes wrong while fetching users chart data
 * @description Get user growth data with monthly/daily breakdown
 */
const getUsersChartData = catchAsync(async (req, res) => {
  const filterType = req.query.filterType
  const data = await dashboardService.getUsersChartData(filterType);
    res.status(httpStatus.OK).json({
    status: true,
    data,
    message: 'Users chart data fetched successfully'
  });
});

/**
 * Get list of stations pending approval
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} List of pending stations
 * @throws {Error} If something goes wrong while fetching pending stations
 * @description Get list of stations pending approval with name and address details
 */
const getPendingStations = catchAsync(async (req, res) => {
  const pendingStations = await dashboardService.getPendingStations();
  res.status(httpStatus.OK).json({
    status: true,
    data: pendingStations,
    message: 'Pending approval stations fetched successfully'
  });
});

/**
 * Get booking statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Booking statistics
 */
const getBookingStats = catchAsync(async (req, res) => {
  const { timeFilter } = req.query;
  
  logger.info('Fetching booking statistics', { timeFilter });
  
  const stats = await dashboardService.getBookingStats(timeFilter);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: stats,
    message: 'Booking statistics fetched successfully'
  });
});

module.exports = { 
  getDashboardKPIs,
  getRevenueChartData,
  getOverviewChartData,
  getUsersChartData,
  getPendingStations,
  getBookingStats
}
