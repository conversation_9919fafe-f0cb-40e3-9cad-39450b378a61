const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const SessionEvent = sequelize.define(
    'session_events',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      sessionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'charging_sessions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      eventType: {
        type: DataTypes.ENUM(
          'SessionStarted',
          'SessionStopped',
          'TransactionStarted',
          'TransactionStopped',
          'StatusChanged',
          'MeterValueReceived',
          'ErrorOccurred',
          'RemoteCommand',
          'UserAction'
        ),
        allowNull: false,
      },
      eventData: {
        type: DataTypes.JSONB,
        allowNull: true,
        comment: 'Additional event data'
      },
      timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      source: {
        type: DataTypes.ENUM('OCPP', 'System', 'User', 'External'),
        allowNull: false,
        defaultValue: 'OCPP',
      }
    },
    {
      tableName: 'session_events',
      timestamps: true,
      indexes: [
        { fields: ['sessionId'] },
        { fields: ['eventType'] },
        { fields: ['timestamp'] },
        { fields: ['source'] }
      ]
    }
  );

  return SessionEvent;
};
