const {DataTypes, Sequelize} = require('sequelize');
const {paginate} = require('./plugins/paginate'); // Custom pagination plugin
const {sequelize} = require('../config/database'); // Import sequelize instance

const Vendor = sequelize.define(
  'Vendor',
  {
    businessName: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    stationName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    stationAddress: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    country: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false, // Set to true if email is optional
      validate: {
        isEmail: true, // Validate email format
      },
    },
    phone: {
      type: DataTypes.STRING,
      allowNull: false, // Set to true if phone is optional
    },
    isKycApproved: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    profileSetupCompleted: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    userId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'users', // Foreign key references the `User` table
        key: 'id',
      },
      allowNull: false,
    },
    deletedAt: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    tableName: 'vendors',
    timestamps: true,
    paranoid: true,
  }
);

module.exports = Vendor;
