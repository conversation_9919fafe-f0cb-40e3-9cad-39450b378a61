const express = require('express');
const router = express.Router();
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');

const { vendorController } = require('../../controllers');
const vendorValidation = require('../../validations/vendor.validation');

router.post('/',firebaseAuth, validate(vendorValidation.addVendor), vendorController.addVendor);
router.post('/profile-setup',firebaseAuth, validate(vendorValidation.setupVendorProfile), vendorController.setupVendorProfile);
router.get('/',firebaseAuth,vendorController.getVendorProfile);

router.get('/stations',firebaseAuth,validate(vendorValidation.getVendorStations),vendorController.getVendorStations);

router
    .route('/bussiness/update')
    .put(
        firebaseAuth,
        validate(vendorValidation.updateVendor),
        vendorController.updateVendor
    );

    
module.exports = router;
