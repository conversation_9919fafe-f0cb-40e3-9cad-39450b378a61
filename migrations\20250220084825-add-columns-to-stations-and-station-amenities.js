'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('vendors', 'stationName', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('vendors', 'stationAddress', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('vendors', 'country', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('vendors', 'stationName');
    await queryInterface.removeColumn('vendors', 'stationAddress');
    await queryInterface.removeColumn('vendors', 'country');

  }
};
