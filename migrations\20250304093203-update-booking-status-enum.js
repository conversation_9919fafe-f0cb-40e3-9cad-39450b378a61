'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    const tableDesc = await queryInterface.describeTable('bookings');
    if (tableDesc.status) {
      await queryInterface.removeColumn('bookings', 'status');
    }

    // Add the new 'status' column with updated ENUM values
    await queryInterface.addColumn('bookings', 'status', {
      type: Sequelize.ENUM('pending','upcoming', 'completed', 'cancelled'),
      defaultValue: 'pending',
      allowNull: false,
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
      // Remove the updated 'status' column
      await queryInterface.removeColumn('bookings', 'status');
      // Re-add the original 'status' column with old ENUM values
    await queryInterface.addColumn('bookings', 'status', {
      type: Sequelize.ENUM('pending', 'confirmed', 'canceled', 'completed'),
      defaultValue: 'pending',
      allowNull: false,
    });
  }
};
