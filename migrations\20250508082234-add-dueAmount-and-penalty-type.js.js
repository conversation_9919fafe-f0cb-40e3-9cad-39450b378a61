'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    // 1. Add 'dueAmount' column to 'wallets' table
    await queryInterface.addColumn('wallets', 'dueAmount', {
      type: Sequelize.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.00,
      comment: 'Pending penalties or charges',
    });

    // 2. Add 'penalty' to ENUM in 'wallet_transactions.type'
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_wallet_transactions_type" 
      ADD VALUE IF NOT EXISTS 'penalty'
      AFTER 'last_valid_value'; /* Specify position if order matters */
    `);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
     // 1. Remove 'dueAmount' column from 'wallets' table
     await queryInterface.removeColumn('wallets', 'dueAmount');
  }
};
