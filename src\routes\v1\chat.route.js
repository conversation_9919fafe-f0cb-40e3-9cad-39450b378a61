const express = require('express');
const validate = require('../../middlewares/validate');
const chatValidation = require('../../validations/chat.validation');
const chatController = require('../../controllers/chat.controller');
const {firebaseAuth} = require('../../middlewares/firebaseAuth');

const router = express.Router();

router
  .route('/')
  .post(firebaseAuth, validate(chatValidation.initializeChat), chatController.initializeChat);

router
  .route('/message')
  .post(firebaseAuth, validate(chatValidation.sendMessage), chatController.sendMessage);

router
  .route('/:chatId/messages')
  .get(firebaseAuth, validate(chatValidation.getMessages), chatController.getMessages);

router
  .route('/:chatId/close')
  .post(firebaseAuth, validate(chatValidation.closeChat), chatController.closeChat);

module.exports = router;