'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('chargers', 'reason', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Reason for charger status change or maintenance notes'
    });

    await queryInterface.addColumn('stations', 'reason', {
      type: Sequelize.TEXT,
      allowNull: true,
      comment: 'Reason for station status change or maintenance notes'
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('chargers', 'reason');
    await queryInterface.removeColumn('stations', 'reason');
  }
};
