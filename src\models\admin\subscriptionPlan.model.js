const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');
const { duration } = require('moment');

const SubscriptionPlan = sequelize.define('SubscriptionPlan', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: 'Name of the subscription plan (e.g., "Basic", "Premium", "Pro")',
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Detailed description of the subscription plan benefits',
    },
    durationValue: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Length of the subscription period (e.g., 1 for monthly, 3 for quarterly)',
    },
    durationType: {
        type: DataTypes.ENUM('month', 'year'),
        allowNull: false,
        comment: 'Unit of duration (e.g., "month", "year")',
    },
    price: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0,
        },
        comment: 'Price of the subscription plan',
    },
    currency: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'USD',
        comment: 'Currency of the price (e.g., USD, EUR)',
    },
    benefits: {
        type: DataTypes.JSON,
        allowNull: false,
        comment: 'Array of benefits included in the plan',
        defaultValue: [],
    },
    isActive: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether the plan is currently active and available for purchase',
    },
    displayOrder: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Order in which plans should be displayed (lower number = higher priority)',
    }
}, {
    tableName: 'subscription_plans',
    timestamps: true,
    indexes: [
        {
            fields: ['isActive'],
        },
        {
            fields: ['displayOrder'],
        }
    ]
});

module.exports = SubscriptionPlan;