const { DataTypes, Sequelize } = require('sequelize');
// const { paginate } = require('./plugins/paginate'); // Custom pagination plugin
const { sequelize } = require('../config/database'); // Import sequelize instance


  const Review = sequelize.define('Review', {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    stationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
    },
    rating: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 5,
      },
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: true,
    }
  }, {
    tableName: 'reviews',
    timestamps: true,
  });

//   Review.associate = (models) => {
//     Review.belongsTo(models.User, { foreignKey: 'userId' });
//     Review.belongsTo(models.Station, { foreignKey: 'stationId' });
//   };

//   return Review;

module.exports = Review; 