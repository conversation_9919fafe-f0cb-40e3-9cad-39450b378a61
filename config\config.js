const path = require('path');
const dotnev = require('dotenv');

dotnev.config({path: path.join(__dirname, '../.env')});
const envVars = process.env;

module.exports = {
  development: {
    username: envVars.DB_USERNAME,
    password: envVars.DB_PASSWORD,
    database: envVars.DB_DATABASE,
    host: envVars.DB_HOST,
    dialect: 'postgres', // Fixed dialect name
    port: envVars.DB_PORT || 5432
  },
  test: {
    username: envVars.DB_USERNAME,
    password: envVars.DB_PASSWORD,
    database: envVars.DB_DATABASE,
    host: envVars.DB_HOST,
    dialect: 'postgres', // Fixed dialect name
    port: envVars.DB_PORT || 5432
  },
  production: {
    username: envVars.DB_USERNAME,
    password: envVars.DB_PASSWORD,
    database: envVars.DB_DATABASE,
    host: envVars.DB_HOST,
    dialect: 'postgres', // Fixed dialect name
    port: envVars.DB_PORT || 5432
  },
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  mysql: {
    host: envVars.DB_HOST,
    port: envVars.DB_PORT || 5432,
    database: envVars.DB_DATABASE,
    username: envVars.DB_USERNAME,
    password: envVars.DB_PASSWORD,
    dialect: 'postgres', // Fixed dialect name
  },
};
