const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

const FAQ = sequelize.define(
  'FAQ',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    question: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        notEmpty: true
      },
      comment: 'The question text'
    },
    answer: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        notEmpty: true
      },
      comment: 'The answer text'
    },
    type: {
      type: DataTypes.ENUM('Account', 'Service', 'Booking', 'Payment', 'Technical', 'Other'),
      allowNull: false,
      comment: 'Category of the FAQ'
    },
    status: {
      type: DataTypes.ENUM('live', 'draft', 'paused'),
      allowNull: false,
      defaultValue: 'draft',
      comment: 'Publication status of the FAQ'
    },
    displayOrder: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      comment: 'Order in which FAQs are displayed (lower numbers first)'
    }
  },
  {
    tableName: 'faqs',
    timestamps: true,
    comment: 'Frequently Asked Questions'
  }
);

module.exports = FAQ;