const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');
const zaincashService = require('./zaincash.service');
const fibService = require('./fib.service');
const {PAYMENTS_METHODS} = require('../constants/payment')
/**
 * Initialize a payment for wallet top-up using the specified payment provider
 * @param {Object} paymentData - Payment data
 * @param {string} paymentData.provider - Payment provider (e.g., 'zaincash', 'stripe', etc.)
 * @param {number} paymentData.amount - Amount to pay
 * @param {number} paymentData.walletId - Wallet ID to credit
 * @param {string} paymentData.userId - User ID making the payment
 * @returns {Promise<Object>} - Payment initialization result with provider-specific details
 */
const initializePayment = async (paymentData) => {
  const { provider, amount, walletId, userId } = paymentData;

  if (!provider) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment provider is required');
  }

  if (!amount || amount <= 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be greater than 0');
  }

  if (!walletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Wallet ID is required');
  }

  logger.info(`Initializing payment with provider: ${provider}`, { userId, walletId, amount });

  // Route to the appropriate payment service based on provider
  switch (provider.toLowerCase()) {
    case PAYMENTS_METHODS.ZAINCASH:
      return zaincashService.initializePayment({ userId, walletId, amount });

    case PAYMENTS_METHODS.FIB:
      return fibService.initializePayment({ userId, walletId, amount });

    // Add more payment providers as needed
    // case 'stripe':
    //   return stripeService.initializePayment({ userId, walletId, amount });

    // case 'paypal':
    //   return paypalService.initializePayment({ userId, walletId, amount });

    default:
      throw new ApiError(httpStatus.BAD_REQUEST, `Unsupported payment provider: ${provider}`);
  }
};

/**
 * Handle wallet top-up payment callback/webhook from payment providers
 * @param {string} provider - Payment provider name (e.g., 'zaincash', 'stripe')
 * @param {Object} callbackData - Provider-specific callback data
 * @returns {Promise<Object>} - Payment verification result with wallet update details
 */
const handlePaymentCallback = async (provider, callbackData) => {
  if (!provider) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment provider is required');
  }

  logger.info(`Handling payment callback from provider: ${provider}`);

  // Route to the appropriate payment service based on provider
  switch (provider.toLowerCase()) {
    case PAYMENTS_METHODS.ZAINCASH:
      return zaincashService.verifyPayment(callbackData.token);

    case PAYMENTS_METHODS.FIB:
      return fibService.verifyPayment(callbackData);

    // Add more payment providers as needed
    // case 'stripe':
    //   return stripeService.verifyPayment(callbackData);

    // case 'paypal':
    //   return paypalService.verifyPayment(callbackData);

    default:
      throw new ApiError(httpStatus.BAD_REQUEST, `Unsupported payment provider: ${provider}`);
  }
};

module.exports = {
  initializePayment,
  handlePaymentCallback
};
