const Joi = require('joi');

const addStation = {
    body: Joi.object().keys({
    stationName: Joi.string().max(255).required().messages({
      'string.max': 'Station name must not exceed 255 characters',
      'any.required': 'Station name is required',
    }),
    city: Joi.string().max(255).required().messages({
      'string.max': 'City must not exceed 255 characters',
      'any.required': 'City is required',
    }),
    pinCode: Joi.string().max(255).required().messages({
      'string.max': '<PERSON>nco<PERSON> must not exceed 255 characters',
      'any.required': '<PERSON>ncode is required',
    }),
    state: Joi.string().max(255).required().messages({
      'string.max': 'State must not exceed 255 characters',
      'any.required': 'State is required',
    }),
    address: Joi.string().max(255).required().messages({
      'string.max': 'Address must not exceed 255 characters',
      'any.required': 'Address is required',
    }),
    latitude: Joi.number().required().min(-90).max(90).messages({
      'number.base': 'Latitude must be a valid number.',
      'any.required': 'Latitude is required.',
      'number.min': 'Latitude must be between -90 and 90.',
      'number.max': 'Latitude must be between -90 and 90.',
    }),
    longitude: Joi.number().required().min(-180).max(180).messages({
      'number.base': 'Longitude must be a valid number.',
      'any.required': 'Longitude is required.',
      'number.min': 'Longitude must be between -180 and 180.',
      'number.max': 'Longitude must be between -180 and 180.',
    }),
    amenities: Joi.array().items(Joi.number().integer()).required().messages({
      'any.required': 'At least one amenity is required',
      'array.base': 'Amenities must be an array of numbers',
      'number.base': 'Each amenity ID must be a number',
    }),
    phoneNumber: Joi.string().max(255).messages({
      'string.max': 'Phone number must not exceed 255 characters',
    }),
    operationalSchedule: Joi.array()
      .items(
        Joi.object({
          day: Joi.string()
            .valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
            .required()
            .messages({
              'any.only': 'Day must be a valid weekday name',
              'any.required': 'Day is required',
            }),
          startTime: Joi.string()
            .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
            .required()
            .messages({
              'string.pattern.base': 'Start time must be in HH:mm:ss format (e.g., 09:00:00)',
              'any.required': 'Start time is required',
            }),
          endTime: Joi.string()
            .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
            .required()
            .messages({
              'string.pattern.base': 'End time must be in HH:mm:ss format (e.g., 18:00:00)',
              'any.required': 'End time is required',
            }),
        })
      )
      .required()
      .messages({
        'array.base': 'Operational schedule must be an array of objects',
        'any.required': 'Operational schedule is required',
      }),
  }),
};

const getStations = {
  query: Joi.object().keys({
    stationName: Joi.string().trim(),
    latitude: Joi.number().min(-90).max(90),
    longitude: Joi.number().min(-180).max(180),
    useCurrentLocation: Joi.boolean().default(false),
    chargingTypes: Joi.string().custom((value, helpers) => {
      return value.split(',').map(type => type.trim());
    }).default(''),
    // connectorTypes: Joi.string().custom((value, helpers) => {
    //   return value.split(',').map(type => type.trim());
    // }).default(''),
    vehicleId: Joi.number().integer().min(1).required(),
    stationAmenities: Joi.array()
      .items(Joi.number().integer().min(1))
      .single()
      .default([]),
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(10)
      .optional()
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100',
      }),
    // sortBy: Joi.string()
    //   .valid('createdAt', 'stationName', 'totalReviews')
    //   .default('createdAt')
    //   .optional()
    //   .messages({
    //     'any.only': 'Sort by must be one of: createdAt, stationName, totalReviews',
    //   }),
    // sortOrder: Joi.string()
    //   .valid('asc', 'desc')
    //   .default('desc')
    //   .optional()
    //   .messages({
    //     'any.only': 'Sort order must be either asc or desc',
    //   })
  })
}
const addChargers = {
  body: Joi.object().keys({
    stationId: Joi.number().required(),
    connectorId: Joi.number().required(),
    powerId: Joi.number().required(),
    pricePerHour: Joi.number().required(),
  })
};

const toggleStationStatus = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    isEnabled: Joi.boolean().required(),
    reason: Joi.when('isEnabled', {
      is: false, // Only apply these rules when isEnabled is false
      then: Joi.string().min(5).required().messages({
        'string.empty': 'Reason is required when disabling a station',
        'string.min': 'Reason must be at least 5 characters long',
        'any.required': 'Reason is required when disabling a station'
      }),
      otherwise: Joi.string().allow(null, '').optional() // Optional when enabling
    })
  })
};

const getStationChargers = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required()
  }),
};

const deleteCharger = {
  params: Joi.object().keys({
    chargerId: Joi.number().integer().required()
  }),
}

const blockStation = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required()
  }),
  body: Joi.object().keys({
    startDate: Joi.date()
      .required()
      .min('now')
      .messages({
        'date.min': 'Start date cannot be in the past',
      }),
    endDate: Joi.date()
      .min(Joi.ref('startDate'))
      .required()
      .messages({
        'date.min': 'End date must be equal to or after start date',
      }),
    reason: Joi.string().required().min(5).messages({
      'string.min': 'Reason must be at least 5 characters long',
    }),
  }),
};

const getStationBlocks = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
};

const removeStationBlock = {
  params: Joi.object().keys({
    blockId:Joi.number().integer().required(),
  }),
};

const getStationById = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
};

const getCharger = {
  params: Joi.object().keys({
      chargerId: Joi.number().integer().required()
  })
};

const updateStation = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    stationName: Joi.string().max(255).messages({
      'string.max': 'Station name must not exceed 255 characters',
    }),
    city: Joi.string().max(255).messages({
      'string.max': 'City must not exceed 255 characters',
    }),
    pinCode: Joi.string().max(255).messages({
      'string.max': 'Pincode must not exceed 255 characters',
    }),
    state: Joi.string().max(255).messages({
      'string.max': 'State must not exceed 255 characters',
    }),
    address: Joi.string().max(255).messages({
      'string.max': 'Address must not exceed 255 characters',
    }),
    latitude: Joi.number().min(-90).max(90).messages({
      'number.base': 'Latitude must be a valid number.',
      'number.min': 'Latitude must be between -90 and 90.',
      'number.max': 'Latitude must be between -90 and 90.',
    }),
    longitude: Joi.number().min(-180).max(180).messages({
      'number.base': 'Longitude must be a valid number.',
      'number.min': 'Longitude must be between -180 and 180.',
      'number.max': 'Longitude must be between -180 and 180.',
    }),
    amenities: Joi.array().items(Joi.number().integer()).messages({
      'array.base': 'Amenities must be an array of numbers',
      'number.base': 'Each amenity ID must be a number',
    }),
    phoneNumber: Joi.string().max(255).messages({
      'string.max': 'Phone number must not exceed 255 characters',
    }),
    operationalSchedule: Joi.array()
      .items(
        Joi.object({
          day: Joi.string()
            .valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday')
            .required()
            .messages({
              'any.only': 'Day must be a valid weekday name',
              'any.required': 'Day is required',
            }),
          startTime: Joi.string()
            .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
            .required()
            .messages({
              'string.pattern.base': 'Start time must be in HH:mm:ss format (e.g., 09:00:00)',
              'any.required': 'Start time is required',
            }),
          endTime: Joi.string()
            .pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
            .required()
            .messages({
              'string.pattern.base': 'End time must be in HH:mm:ss format (e.g., 18:00:00)',
              'any.required': 'End time is required',
            }),
        })
      )
      .messages({
        'array.base': 'Operational schedule must be an array of objects',
      }),
  }).min(1).messages({
    'object.min': 'At least one field is required for update'
  }),
};

const addReview = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    rating: Joi.number().min(1).max(5).required(),
    comment: Joi.string().max(255).allow(''),
  }),
};
const getReviews = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required(),
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().default(1).min(1),
    limit: Joi.number().integer().default(10).min(1).max(100),
    sortBy:Joi.string().pattern(
      /^(createdAt|rating):(?:asc|desc)(?:,(createdAt|rating):(?:asc|desc))?$/,
      'valid sortBy format'
    ).default('createdAt:desc'), // Default to sorting by createdAt in descending order
  }),
}

const deleteReview = {
  params: Joi.object().keys({
    reviewId: Joi.number().integer().required(),
  }),
}
const toggleChargerStatus = {
  params: Joi.object().keys({
    chargerId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    isEnabled: Joi.boolean().required(),
    reason: Joi.when('isEnabled', {
      is: false, // Only apply these rules when isEnabled is false
      then: Joi.string().min(5).required().messages({
        'string.empty': 'Reason is required when disabling a charger',
        'string.min': 'Reason must be at least 5 characters long',
        'any.required': 'Reason is required when disabling a charger'
      }),
      otherwise: Joi.string().allow(null, '').optional() // Optional when enabling
    })
  })
};

const getStationVisitors = {
  params: Joi.object().keys({
    stationId: Joi.number().integer().required().messages({
      'number.base': 'Station ID must be a number',
      'number.integer': 'Station ID must be an integer',
      'any.required': 'Station ID is required',
    }),
  }),
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .default(10)
      .optional()
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100',
      }),
    timeFilter: Joi.string()
      .valid('all', '3months', '6months')
      .default('all')
      .optional()
      .messages({
        'any.only': 'Time filter must be one of: all, 3months, 6months',
      }),
  }),
};

const checkStationCompatibility = {
  params: Joi.object().keys({
    vehicleId: Joi.number().integer().required(),
    stationId: Joi.number().integer().required(),
  }),
};
module.exports = {
  addStation,
  getStations,
  addChargers,
  getStationChargers,
  deleteCharger,
  blockStation,
  getStationBlocks,
  removeStationBlock,
  toggleStationStatus,
  getStationById,
  getCharger,
  updateStation,
  addReview,
  getReviews,
  deleteReview,
  toggleChargerStatus,
  getStationVisitors,
  checkStationCompatibility
};
