const catchAsync = require('../utils/catchAsync');
const { reviewService } = require('../services');
const httpStatus = require('http-status');

const addReview = catchAsync(async (req, res) => {
    const { rating, comment } = req.body;
    const { stationId } = req.params;
    const { user } = req;

    const review = await reviewService.addReview(
        stationId,
        user.id,
        rating,
        comment
    );

    res.status(httpStatus.CREATED).json({
        success: true,
        data: review,
        message: 'Review created successfully'
    });
});

const getReviews = catchAsync(async (req, res) => {
    const { stationId } = req.params;
 
    let { sortBy = 'createdAt:desc' } = req.query; // 'newest' or 'oldest'

    if (!stationId) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Station ID is required');
    }

  const sortingFields = sortBy.split(',').map(sort => {
    const [field, direction] = sort.split(':');
    return [field, direction.toUpperCase()]; // Ensure direction is uppercase
  });

    const reviews = await reviewService.getReviews(stationId, req.query, sortingFields);

    res.status(httpStatus.OK).json({
        success: true,
        data: reviews,
        message: 'Reviews fetched successfully',
    });
});

const deleteReview = catchAsync(async (req, res) => {
    const { reviewId } = req.params;
    const { user } = req;

    await reviewService.deleteReview(reviewId, user.id);

    res.status(httpStatus.OK).json({
        success: true,
        message: 'Review deleted successfully',
    });
});
module.exports = { addReview, getReviews,deleteReview };