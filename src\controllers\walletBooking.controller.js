const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const { walletBookingService } = require('../services');

/**
 * Process booking payment using wallet
 */
const processBookingPayment = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { bookingId } = req.params;
  
  const result = await walletBookingService.processBookingPayment(userId, bookingId);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Booking payment processed successfully',
  });
});

/**
 * Process booking refund to wallet
 */
const processBookingRefund = catchAsync(async (req, res) => {
  const { bookingId } = req.params;
  
  const result = await walletBookingService.processBookingRefund(bookingId);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Booking refund processed successfully',
  });
});

module.exports = {
  processBookingPayment,
  processBookingRefund,
};
