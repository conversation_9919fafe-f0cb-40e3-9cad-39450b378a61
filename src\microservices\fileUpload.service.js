const { v4: uuidv4 } = require('uuid');
const multer = require('multer');
const { getSignedUrl } = require('@aws-sdk/s3-request-presigner');
const { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const config = require('../config/config');
const { fileTypes } = require('../constants');

const { endpoint, accessKeyId, region, secretAccessKey, name: bucketName } = config.do;

// Initialize S3 Client
const s3client = new S3Client({
  region, // DigitalOcean Spaces uses "us-east-1" as a default region
  endpoint:`https://${endpoint}`, // Custom endpoint for DigitalOcean Spaces
  credentials: { accessKeyId, secretAccessKey },
});

// Multer Storage Configuration
const storage = multer.memoryStorage();

const fileFilter = (req, file, cb) => {
  if (fileTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only JPEG, PNG, and PDF are allowed!'), false);
  }
};

// const multerUpload = multer({
//   storage,
//   fileFilter,
//   limits: { fileSize: 10 * 1024 * 1024, files: 10 }, // Limit file size to 10MB
// });

const configureMulter = ({ maxFileSizeMB = 10, maxFiles = 10 } = {}) => {
  return multer({
    storage,
    fileFilter,
    limits: { 
      fileSize: maxFileSizeMB * 1024 * 1024, // Convert MB to Bytes
      files: maxFiles,
      fieldSize: maxFileSizeMB * 1024 * 1024,
    },
  });
};

/**
* Middleware to handle multer file uploads dynamically
* @param {string} fieldName - Name of the field (e.g., 'image' or 'images')
* @param {number} maxFiles - Maximum number of files
* @param {boolean} isSingle - Whether to accept a single file or multiple
*/
const handleMulterErrors = (fieldName, maxFiles = 1, isSingle = true) => {
 const upload = isSingle
   ? configureMulter({ maxFileSizeMB: 5, maxFiles: 1 }).single(fieldName) // Single file upload
   : configureMulter({ maxFileSizeMB: 10, maxFiles }).array(fieldName, maxFiles); // Multiple file upload

 return (req, res, next) => {
   upload(req, res, (err) => {
    if (err) {
      if (err.code === 'LIMIT_FILE_SIZE') {
        return res.status(400).json({ status: false, message: 'File size should not exceed 10MB.' });
      }
      if (err.code === 'LIMIT_FILE_COUNT') {
        return res.status(400).json({ status: false, message: `You can upload a maximum of ${maxFiles} files.` });
      }
      return res.status(400).json({ status: false, message: err.message });
    }
    next();
  });
 };
};


// Function to Get Object URL (Public or Signed URL)
async function getObjectURL(Key, signedUrl = false) {
  try {
    if (signedUrl) {
      const command = new GetObjectCommand({ Bucket: bucketName, Key });
      const url = await getSignedUrl(s3client, command, { expiresIn: 3600 }); // URL expires in 1 hour
      return { key: Key, url };
    }
    return { key: Key, url: `https://${bucketName}.${endpoint}/${Key}` };
  } catch (error) {
    console.error('Error generating object URL:', error);
    return null;
  }
}

// Function to Delete an Object
async function s3Delete(Key) {
  try {
    const command = new DeleteObjectCommand({ Bucket: bucketName, Key });
    await s3client.send(command);
    return { success: true, message: 'File deleted successfully' };
  } catch (error) {
    console.error('Error deleting object:', error);
    return { success: false, message: 'Error deleting file' };
  }
}

// Function to Upload Files
async function s3Upload(files, folder = 'uploads') {
  try {
    const uploadPromises = files.map(async (file) => {
      const Key = `${folder}/${uuidv4()}-${file.originalname}`;
      const command = new PutObjectCommand({
        Bucket: bucketName,
        Key,
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read'
      });
      console.log(command);
      await s3client.send(command);

      return await getObjectURL(Key);
    });
    return await Promise.all(uploadPromises);
  } catch (error) {
    console.error('Error uploading files:', error);
    return null;
  }
}

module.exports = {
  s3Upload,
  s3Delete,
  getObjectURL,
  // multerUpload,
  handleMulterErrors
};
