const httpStatus = require('http-status');
const { User, Vendor, VendorSubscription, Station,Charger, Connector, Power,Amenity,StationBlock,SubscriptionPlan } = require('../../models');
const ApiError = require('../../utils/ApiError');
const { Op } = require('sequelize');
const logger = require('../../config/logger');
const { getYearStartAndEnd, getGrowthPercentage, countRecordsByStatus } = require('../../utils/analytics.utils');
const { sequelize } = require('../../config/database');
const admin = require('firebase-admin');




/**
 * Get all vendors with pagination and filtering
 * @param {Object} filters - Filter and pagination options
 * @returns {Promise<Object>} - Paginated list of vendors
 */
const getAllVendors = async (filters = {}) => {
  const {
    name,
    email,
    phone,
    isActive,
    fromDate,
    toDate,
    businessName,
    page,
    limit,
    sortBy
  } = filters;

  // Parse sortBy to get field and direction
  const [sortField, sortDirection] = sortBy.split(':');
  const direction = sortDirection || 'desc';

  // Build the where clause for User model
  const userWhere = { isVendor: true };
  
  if (name) {
    userWhere.name = { [Op.iLike]: `%${name}%` };
  }
  
  if (email) {
    userWhere.email = { [Op.iLike]: `%${email}%` };
  }
  
  if (phone) {
    userWhere.phone = { [Op.iLike]: `%${phone}%` };
  }
  
  if (isActive !== undefined) {
    userWhere.isActive = isActive;
  }
  
  if (fromDate && toDate) {
    userWhere.createdAt = {
      [Op.between]: [new Date(fromDate), new Date(toDate)]
    };
  } else if (fromDate) {
    userWhere.createdAt = { [Op.gte]: new Date(fromDate) };
  } else if (toDate) {
    userWhere.createdAt = { [Op.lte]: new Date(toDate) };
  }

  // Build the where clause for Vendor model
  const vendorWhere = {};
  
  if (businessName) {
    vendorWhere.businessName = { [Op.iLike]: `%${businessName}%` };
  }
  

  // Build the include array
  const include = [
    {
      model: Vendor,
      as: 'vendor',
      required: true,
      ...(Object.keys(vendorWhere).length > 0 && { where: vendorWhere }),
      include: []
    }
  ];

  // Add subscription status filter if provided
  // if (subscriptionStatus) {
  //   const subscriptionWhere = {};
    
  //   if (subscriptionStatus === 'active') {
  //     subscriptionWhere.expiryDate = { [Op.gt]: new Date() };
  //     subscriptionWhere.status = 'active';
  //   } else if (subscriptionStatus === 'expired') {
  //     subscriptionWhere.expiryDate = { [Op.lt]: new Date() };
  //     subscriptionWhere.status = 'active';
  //   } else if (subscriptionStatus === 'none') {
  //     // Will use a left join and check for null
  //   }
    
  //   include[0].include.push({
  //     model: VendorSubscription,
  //     as: 'activeSubscription',
  //     where: subscriptionStatus !== 'none' ? subscriptionWhere : undefined,
  //     required: subscriptionStatus !== 'none'
  //   });
  // }

  // Set up pagination
  const offset = (page - 1) * limit;
  
  // Set up sorting
  let order = [[sortField, direction]];
  
  const { count, rows } = await User.findAndCountAll({
    where: userWhere,
    include,
    distinct: true,
    offset,
    limit,
    order
  });

  // Transform the data for the response
  const vendors = rows.map(user => {
    const vendor = user.vendor || {};
    
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      isActive: user.isActive,
      profilePicUrl: user.profilePicUrl,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      vendor: {
        id: vendor.id,
        businessName: vendor.businessName,
        address: vendor.address,
        city: vendor.city,
        state: vendor.state,
        country: vendor.country,
        zipCode: vendor.zipCode,
        // subscription: vendor.activeSubscription ? {
        //   id: vendor.activeSubscription.id,
        //   planName: vendor.activeSubscription.planName,
        //   status: vendor.activeSubscription.status,
        //   startDate: vendor.activeSubscription.startDate,
        //   expiryDate: vendor.activeSubscription.expiryDate
        // } : null
      }
    };
  });

  return {
    vendors,
    pagination: {
      page,
      limit,
      totalItems: count,
      totalPages: Math.ceil(count / limit)
    }
  };
};




/**
 * Create a vendor with user account
 * @param {string} email - User email
 * @param {string} password - User password (optional if user exists)
 * @param {Object} additionalData - Vendor data including businessName, phone, etc.
 * @returns {Promise<Object>} - Created vendor with user data
 */
const createVendorWithUser = async (email, password, additionalDaa) => {
  const {name, gender, country, dob, businessName, phone} = additionalDaa;
  let firebaseUser;
  let existingUser;

  try {
    // Check if user already exists in Firebase by email
    try {
      existingUser = await admin.auth().getUserByEmail(email);
      logger.info(`User already exists in Firebase with email: ${email}`);
      firebaseUser = existingUser;
    } catch (error) {
      // User doesn't exist, create new Firebase user
      if (error.code === 'auth/user-not-found') {
        if (!password) {
          throw new ApiError(httpStatus.BAD_REQUEST, 'Password is required to create a new user account');
        }

        logger.info(`Creating new Firebase user with email: ${email}`);
        firebaseUser = await admin.auth().createUser({
          email,
          password,
          emailVerified: true,
        });
      } else {
        throw error;
      }
    }

    // Check if user exists in our database
    let user = await User.findOne({where: {firebaseUid: firebaseUser.uid}});

    // If user doesn't exist in our database, create it
    if (!user) {
      logger.info(`Creating new user in database with email: ${email}`);
      user = await User.create({
        email: firebaseUser.email,
        firebaseUid: firebaseUser.uid,
        profilePicUrl: firebaseUser.photoURL || null,
        phone: phone || firebaseUser.phoneNumber || null,
        isEmailVerified: firebaseUser.emailVerified,
        firebaseSignInProvider: firebaseUser.providerData?.[0]?.providerId || 'password',
        isVendor: true,
        name,
        gender,
        country,
        dob,
      });
    } 

    const existingVendor = await Vendor.findOne({ where: { userId: user.id } });
    if (existingVendor) {
      logger.info(`Vendor already exists for userId: ${user.id}`);
      throw new ApiError(httpStatus.BAD_REQUEST, 'Vendor already exists for this user');
    }

    const vendor = await Vendor.create({
      businessName,
      email,
      phone: phone || user.phone,
      userId: user.id,
    });

    user.isVendor = true;
    await user.save();
    logger.info(`Vendor created with userId: ${user.id}`);
    return {
      user,
      vendor,
    };
  } catch (error) {
    logger.error('Error creating vendor with user:', error);

    // If we created a Firebase user but failed later, clean up
    if (firebaseUser && !existingUser) {
      try {
        await admin.auth().deleteUser(firebaseUser.uid);
      } catch (deleteError) {
        logger.error('Error deleting Firebase user after failure:', deleteError);
      }
    }

    throw new ApiError(error.statusCode || httpStatus.BAD_REQUEST, error.message || 'Failed to create vendor account');
  }
};

/**
 * @param {number} userId - User ID
 * @param {number} page - Page number
 * @param {number} limit - Number of items per page
 * @returns {Promise<Object>} - Station details
 */

const getVendorStations = async (userId, page, limit) => {
    // Find the vendor
    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

   // Get total station count separately
   const { rows: stations, count: totalStations } = await Station.findAndCountAll({
    where: { vendorId: vendor.id },
    limit,
    offset,
    attributes: { exclude: ["createdAt", "updatedAt"] },
    order: [["id", "DESC"]],
    distinct: true, 
    // include: [
    //     {
    //         model: Charger,
    //         attributes: ["id", "connectorId", "powerId", "pricePerHour"],
    //         include: [{ model: Connector, attributes: ["id", "name", "type"] }]
    //     },
    //     {
    //         model: Amenity,
    //         as: "amenities",
    //         attributes: ["id", "name"],
    //         through: { attributes: [] }
    //     }
    // ]
});

    return {
        stations,
        pagination: {
            page,
            limit,
            totalStations,
            totalPages: Math.ceil(totalStations / limit),
        },
    };
};


/**
 * @param {number} stationId - Station ID
 * @returns {Promise<Object>} - Station details
 */
const getStationDetails = async (stationId) => {
    const [station, blocks] = await Promise.all([
      fetchStationWithDetails(stationId),
      fetchActiveBlocksForStation(stationId),
    ]);
  
    if (!station) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    }
  
    station.setDataValue('blocks', blocks);
    return station;
  };
  
  // Fetch station with associated chargers, connectors, power, and amenities
  const fetchStationWithDetails = (stationId) => {
    return Station.findByPk(stationId, {
      attributes: { exclude: ['createdAt', 'updatedAt'] },
      include: [
        {
          model: Charger,
          attributes: ['id', 'connectorId', 'powerId', 'pricePerHour'],
          include: [
            {
              model: Connector,
              attributes: ['id', 'name', 'type'],
            },
            {
              model: Power,
              attributes: ['id', 'value', 'unit'],
            },
          ],
        },
        {
          model: Amenity,
          as: 'amenities',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
      ],
    });
  };
  
  // Fetch active future blocks for the station
  const fetchActiveBlocksForStation = (stationId) => {
    return StationBlock.findAll({
      where: {
        stationId,
        isActive: true,
        endDate: { [Op.gte]: new Date() },
      },
      attributes: ['id', 'reason', 'startDate', 'endDate'],
    });
  };
  

  const addChargerToStation = async (chargerData, stationId) => {
    const station = await Station.findByPk(stationId);
    if (!station) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    }
  
    const createdCharger = await Charger.create({
      stationId: station.id,
      connectorId: chargerData.connectorId,
      powerId: chargerData.powerId,
      pricePerHour: chargerData.pricePerHour,
    });
  
    return createdCharger;
  };


/**
  
 * Get vendor statistics with year-over-year comparison
 * @returns {Promise<Object>} Vendor statistics
 */
const getVendorStats = async () => {
  const currentYear = moment().year();
  const previousYear = currentYear - 1;
  
  // Get year date ranges
  const currentYearRange = getYearStartAndEnd(currentYear);
  const previousYearRange = getYearStartAndEnd(previousYear);
  
  // For all-time queries, use a very old start date and current date as end date
  const allTimeStartDate = moment('2000-01-01').startOf('day').toDate();
  const currentDate = moment().endOf('day').toDate();
  
  // Run all queries in parallel for better performance
  const [
    totalVendors,
    totalVendorsPreviousYear,
    newVendors,
    newVendorsPreviousYear,
    paidVendorsResult,
    paidVendorsPreviousYearResult,
    freeVendorsResult,
    freeVendorsPreviousYearResult
  ] = await Promise.all([
    // Total vendors (current)
    countRecordsByStatus(Vendor, {
      startDate: allTimeStartDate,
      endDate: currentDate
    }),
    
    // Total vendors (previous year)
    countRecordsByStatus(Vendor, {
      startDate: allTimeStartDate,
      endDate: previousYearRange.endDate
    }),
    
    // New vendors this year
    countRecordsByStatus(Vendor, {
      startDate: currentYearRange.startDate,
      endDate: currentYearRange.endDate
    }),
    
    // New vendors previous year
    countRecordsByStatus(Vendor, {
      startDate: previousYearRange.startDate,
      endDate: previousYearRange.endDate
    }),
    
    // Paid vendors (current)
sequelize.query(`
  SELECT COUNT(DISTINCT v.id) as count
  FROM vendors v
  JOIN vendor_subscriptions vs ON v.id = vs."vendorId"
  WHERE vs.status = 'active'
`, { type: sequelize.QueryTypes.SELECT }).then(result => result[0].count),

// Paid vendors (previous year)
sequelize.query(`
  SELECT COUNT(DISTINCT v.id) as count
  FROM vendors v
  JOIN vendor_subscriptions vs ON v.id = vs."vendorId"
  WHERE vs.status = 'active'
  AND v."createdAt" <= :endDate
`, {
  replacements: { endDate: previousYearRange.endDate },
  type: sequelize.QueryTypes.SELECT
}).then(result => result[0].count),

// Free vendors (without active subscription)
sequelize.query(`
  SELECT COUNT(DISTINCT v.id) as count
  FROM vendors v
  LEFT JOIN (
    SELECT "vendorId" FROM vendor_subscriptions 
    WHERE status = 'active' 
  ) vs ON v.id = vs."vendorId"
  WHERE vs."vendorId" IS NULL
`, { type: sequelize.QueryTypes.SELECT }).then(result => result[0].count),

// Free vendors (previous year)
sequelize.query(`
  SELECT COUNT(DISTINCT v.id) as count
  FROM vendors v
  LEFT JOIN (
    SELECT "vendorId" FROM vendor_subscriptions 
    WHERE status = 'active'
    AND "createdAt" <= :endDate
  ) vs ON v.id = vs."vendorId"
  WHERE vs."vendorId" IS NULL
  AND v."createdAt" <= :endDate
`, {
  replacements: { endDate: previousYearRange.endDate },
  type: sequelize.QueryTypes.SELECT
}).then(result => result[0].count)

  ]);
  const paidVendorsCount = Number(paidVendorsResult);
const paidVendorsPreviousYear = Number(paidVendorsPreviousYearResult);
const freeVendorsCount = Number(freeVendorsResult);
const freeVendorsPreviousYear = Number(freeVendorsPreviousYearResult);


  return {
    totalVendors: {
      count: parseInt(totalVendors),
      growth: getGrowthPercentage(totalVendors, totalVendorsPreviousYear)
    },
    newVendors: {
      count: parseInt(newVendors),
      growth: getGrowthPercentage(newVendors, newVendorsPreviousYear)
    },
    paidVendors: {
      count: parseInt(paidVendorsCount),
      growth: getGrowthPercentage(paidVendorsCount, paidVendorsPreviousYear)
    },
    freeVendors: {
      count: parseInt(freeVendorsCount),
      growth: getGrowthPercentage(freeVendorsCount, freeVendorsPreviousYear)
    }
  };
};

/**
 * Get vendor details by userId
 * @param {number} userId - User ID
 * @returns {Promise<Object>} - Vendor details with user information
 */
const getVendorById = async (userId) => {
  const user = await User.findOne({
    where: { id: userId, isVendor: true },
    attributes: ['id', 'name', 'email', 'phone', 'isActive', 'profilePicUrl', 'createdAt', 'gender', 'country'],
    include: [
      {
        model: Vendor,
        as: 'vendor',
        required: true,
      }
    ]
  });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
  }

  // Transform the data for the response
  const vendor = user.vendor || {};
  
  return {
    id: user.id,
    name: user.name,
    email: user.email,
    phone: user.phone,
    isActive: user.isActive,
    profilePicUrl: user.profilePicUrl,
    createdAt: user.createdAt,
    gender: user.gender,
    country: user.country,
    vendor: {
      id: vendor.id,
      businessName: vendor.businessName,
      address: vendor.address,
      isKycApproved: vendor.isKycApproved,
      profileSetupCompleted: vendor.profileSetupCompleted,
      phone: vendor.phone,
      // subscription: vendor.activeSubscription ? {
      //   id: vendor.activeSubscription.id,
      //   planName: vendor.activeSubscription.planName,
      //   status: vendor.activeSubscription.status,
      //   startDate: vendor.activeSubscription.startDate,
      //   expiryDate: vendor.activeSubscription.expiryDate
      // } : null
    }
  };
};

/**
 * Get vendor business details by userId
 * @param {number} userId - User ID
 * @returns {Promise<Object>} - Vendor business details with active subscription
 */
const getVendorBusiness = async (userId) => {
  const vendor = await Vendor.findOne({
    where: { userId },
    attributes: ['id', 'businessName', 'country'],
    include: [
      {
        model: VendorSubscription,
        required: false,
        where: { isActive: true },
        include: [
          {
            model: SubscriptionPlan,
            attributes: ['id', 'name', 'description', 'durationValue', 'durationType', 'price', 'currency', 'benefits']
          }
        ],
        limit: 1,
        order: [['createdAt', 'DESC']]
      }
    ]
  });

  if (!vendor) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor business not found');
  }

  // Transform the data for the response
  return {
    id: vendor.id,
    businessName: vendor.businessName,
    country: vendor.country,
    subscription: vendor.VendorSubscriptions && vendor.VendorSubscriptions.length > 0 ? {
      id: vendor.VendorSubscriptions[0].id,
      startDate: vendor.VendorSubscriptions[0].startDate,
      endDate: vendor.VendorSubscriptions[0].endDate,
      autoRenew: vendor.VendorSubscriptions[0].autoRenew,
      plan: vendor.VendorSubscriptions[0].SubscriptionPlan ? {
        id: vendor.VendorSubscriptions[0].SubscriptionPlan.id,
        name: vendor.VendorSubscriptions[0].SubscriptionPlan.name,
        description: vendor.VendorSubscriptions[0].SubscriptionPlan.description,
        price: vendor.VendorSubscriptions[0].SubscriptionPlan.price,
        currency: vendor.VendorSubscriptions[0].SubscriptionPlan.currency,
        duration: `${vendor.VendorSubscriptions[0].SubscriptionPlan.durationValue} ${vendor.VendorSubscriptions[0].SubscriptionPlan.durationType}`,
        benefits: vendor.VendorSubscriptions[0].SubscriptionPlan.benefits
      } : null
    } : null
  };
};

module.exports = {
  createVendorWithUser,
  getAllVendors,
  getVendorStats,
  createVendorWithUser,
  getAllVendors,
  getVendorStations,
  getStationDetails,
  addChargerToStation,
  getVendorById,
  getVendorBusiness
  // other exports...
};
