const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');

const HelpCenter = sequelize.define(
  'HelpCenterTicket',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      comment: 'Reference to the user submitting the ticket'
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isEmail: true
      },
      comment: 'Email to which the ticket resolution will be sent'
    },
    ticketNo: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      comment: 'Unique ticket number like #123-4567'
    },
    query: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 1000] // Minimum 1 character, maximum 1000 characters
      },
      comment: 'User message or issue description'
    },
    status: {
      type: DataTypes.ENUM('resolved', 'pending', 'on-hold'),
      allowNull: false,
      defaultValue: 'pending',
      comment: 'Status of the ticket'
    },
  }, {
  timestamps: true, // createdAt and updatedAt
  tableName: 'help_center_tickets',
  comment: 'Table storing help center support tickets'
}
);

module.exports = HelpCenter;
