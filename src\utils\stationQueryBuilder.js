const { Op, Sequelize } = require('sequelize');
const { APPROVAL_STATUS } = require('../constants');
const Charger = require('../models/charger.model');
const Power = require('../models/power.model');
const Connector = require('../models/connector.model');
const StationAmenity = require('../models/stationAmenity.model');
const Amenity = require('../models/amenity.model');

const powerRanges = {
  tesla: { [Op.gte]: 250 },
  fast: { [Op.gte]: 50 },
  standard: { [Op.between]: [22, 50] },
  slow: { [Op.lt]: 22 }
};

const buildWhereClause = (filters) => {
  const whereClause = {};

  whereClause.approvalStatus = { [Op.eq]: APPROVAL_STATUS.APPROVED };

  if (filters.useCurrentLocation && filters.latitude && filters.longitude) {
    whereClause[Op.and] = [
      Sequelize.literal(`
        (
          6371000 * acos(
            cos(radians(${filters.latitude})) * 
            cos(radians(latitude)) * 
            cos(radians(longitude) - radians(${filters.longitude})) + 
            sin(radians(${filters.latitude})) * 
            sin(radians(latitude))
          )
        ) <= 5000
      `)
    ];
  }

  if (filters.stationName) {
    whereClause.stationName = { [Op.iLike]: `%${filters.stationName}%` };
  }

  return whereClause;
};

const buildIncludeClause = (filters) => {
  const includes = [];

  const chargerInclude = {
    model: Charger,
    required: true,
    include: []
  };

  if (Array.isArray(filters.chargingTypes) && filters.chargingTypes.length) {
    chargerInclude.include.push({
      model: Power,
      where: {
        value: { 
          [Op.or]: filters.chargingTypes
            .map(type => powerRanges[type])
            .filter(Boolean) 
        }
      }
    });
  } else {
    chargerInclude.include.push({ model: Power });
  }

  if (Array.isArray(filters.connectorTypes) && filters.connectorTypes.length) {
    chargerInclude.include.push({
      model: Connector,
      where: { type: { [Op.in]: filters.connectorTypes } }
    });
  } else {
    chargerInclude.include.push({ model: Connector });
  }

  includes.push(chargerInclude);

  if (Array.isArray(filters.stationAmenities) && filters.stationAmenities?.length) {
    includes.push({
      model: Amenity,
      as: 'amenities',
      required: true,
      through: {
        attributes: []
      },
      where: { 
        id: { [Op.in]: filters.stationAmenities } 
      },
      required: true
    });
  }


  return includes;
};

module.exports = {
  buildWhereClause,
  buildIncludeClause
}; 