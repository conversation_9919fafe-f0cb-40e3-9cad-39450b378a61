const { Charger, ChargerConnector, ChargingSession, Connector, Power } = require('../models');
const { Op } = require('sequelize');

class ChargerStateManager {
    constructor() {
        this.chargerStates = new Map(); // In-memory cache for charger states
        this.heartbeatTimeout = 5 * 60 * 1000; // 5 minutes
        // this.startHeartbeatMonitoring();
    }

    /**
     * Register or update a charger from BootNotification
     * @param {string} chargePointId - OCPP Charge Point ID
     * @param {Object} bootData - Boot notification data
     * @returns {Object} Charger registration result
     */
    async registerCharger(chargePointId, bootData) {
        try {
          const {
            chargePointModel,
            chargePointVendor,
            firmwareVersion,
            chargeBoxSerialNumber,
          } = bootData;
      
          // Find charger by chargePointId (which maps to charger.chargerId in DB)
          const charger = await Charger.findOne({
            where: { chargerId: chargePointId }
          });
      
          if (!charger) {
            console.error(`Charger ${chargePointId} not found in database. Please register the charger first.`);
            return {
              success: false,
              error: 'Charger not found. Please register the charger first.'
            };
          }
      
          // Update charger with BootNotification data and mark it available
          const chargerData = {
            chargePointModel,
            chargePointVendor,
            firmwareVersion,
            chargeBoxSerialNumber: chargeBoxSerialNumber || charger.chargeBoxSerialNumber,
            status: 'Available',
            lastHeartbeat: new Date(),
          };
      
          await charger.update(chargerData);
      
          console.log(`Updated charger ${chargePointId} with BootNotification data.`);
      
          return {
            success: true,
            charger,
            isNewCharger: false,
          };
      
        } catch (error) {
          console.error('Error registering charger:', error);
          throw error;
        }
      }
      

    /**
     * Update charger heartbeat
     * @param {string} chargePointId - OCPP Charge Point ID
     * @returns {Object} Heartbeat result
     */
    async updateHeartbeat(chargePointId) {
        try {
            const now = new Date();

            // Update database
            const [updatedRows] = await Charger.update(
                { 
                    lastHeartbeat: now,
                    status: 'Available'
                },
                { 
                    where: { chargerId: chargePointId }
                }
            );

            if (updatedRows === 0) {
                console.warn(`Charger ${chargePointId} not found for heartbeat update`);
                return { success: false, error: 'Charger not found' };
            }

            // Update cache
            // if (this.chargerStates.has(chargePointId)) {
            //     const state = this.chargerStates.get(chargePointId);
            //     state.lastHeartbeat = now;
            //     state.isOnline = true;
            //     this.chargerStates.set(chargePointId, state);
            // }

            return {
                success: true,
                currentTime: now.toISOString()
            };

        } catch (error) {
            console.error('Error updating heartbeat:', error);
            throw error;
        }
    }

    /**
     * Update connector status
     * @param {string} chargePointId - OCPP Charge Point ID
     * @param {number} connectorId - Connector ID
     * @param {string} status - New status
     * @param {string} errorCode - Optional error code
     * @param {string} info - Optional additional info
     * @returns {Object} Status update result
     */
    async updateConnectorStatus(chargePointId, connectorId, status, errorCode = null, info = null) {
        try {

            // Handle charger-level status (connectorId = 0)
        if (connectorId === 0) {
            const charger = await Charger.findOne({
                where: { chargerId: chargePointId },
                include: [
                    {
                        model: ChargerConnector,
                        as: 'connectors'
                    }
                ]
            });

            if (!charger) {
                throw new Error(`Charger ${chargePointId} not found`);
            }

            // Update all connectors of this charger to the same status
            if (charger.connectors && charger.connectors.length > 0) {
                await ChargerConnector.update(
                    {
                        status: status,
                        errorCode: errorCode,
                        info: info,
                        lastStatusUpdate: new Date()
                    },
                    {
                        where: { chargerId: charger.id }
                    }
                );
            }
              // Update charger status directly
              await charger.update({
                status: status,  // This will be 'Unavailable' in your case
                lastUpdated: new Date(),
                reason: info || ''
            });

            // Use updateChargerOverallStatus to update charger status
            // await this.updateChargerOverallStatus(charger.id);

            console.log(`Updated charger ${chargePointId} and all its connectors to status: ${status}`);

            return {
                success: true,
                chargePointId,
                status,
                isChargerLevel: true,
                connectorsUpdated: charger.connectors ? charger.connectors.length : 0
            };
        }

            // Find the charger with its connector
            const charger = await Charger.findOne({
                where: { chargerId: chargePointId },
                include: [
                    {
                        model: ChargerConnector,
                        as: 'connectors',
                        where: { connectorNumber: connectorId },
                        include: [
                            {
                                model: Connector,
                                as: 'connector'
                            }
                        ]
                    }
                ]
            });

            if (!charger) {
                throw new Error(`Charger ${chargePointId} not found`);
            }

            // Get the charger connector
            const chargerConnector = charger.connectors[0];
            if (!chargerConnector) {
                throw new Error(`Connector ${connectorId} not found for charger ${chargePointId}`);
            }

            // Update the status
            await chargerConnector.update({
                status,
                errorCode,
                info,
                lastStatusUpdate: new Date()
            });

            // Update charger overall status based on connector statuses
            await this.updateChargerOverallStatus(charger.id,info);

            console.log(`Updated connector ${connectorId} on charger ${chargePointId} to status: ${status}`);

            return {
                success: true,
                chargePointId,
                connectorId,
                status
            };

        } catch (error) {
            console.error('Error updating connector status:', error);
            throw error;
        }
    }

    /**
     * Get charger status
     * @param {string} chargePointId - OCPP Charge Point ID
     * @returns {Object} Charger status
     */
    async getChargerStatus(chargePointId) {
        try {
            const charger = await Charger.findOne({
                where: { chargerId: chargePointId },
                include: [{
                    model: ChargerConnector,
                    as: 'connectors'
                }]
            });

            if (!charger) {
                return { success: false, error: 'Charger not found' };
            }

            return {
                success: true,
                charger: {
                    id: charger.id,
                    chargerId: charger.chargerId,
                    status: charger.status,
                    // isOnline: charger.isOnline,
                    lastHeartbeat: charger.lastHeartbeat,
                    connectors: charger.connectors.map(c => ({
                        connectorId: c.connectorId,
                        status: c.status,
                        errorCode: c.errorCode,
                        info: c.info,
                        lastStatusUpdate: c.lastStatusUpdate
                    }))
                }
            };

        } catch (error) {
            console.error('Error getting charger status:', error);
            throw error;
        }
    }

    /**
     * Get all chargers with their status
     * @param {boolean} onlineOnly - Filter for online chargers only
     * @returns {Array} List of chargers
     */
    async getAllChargers(onlineOnly = false) {
        try {
            const whereClause = onlineOnly ? { isOnline: true } : {};

            const chargers = await Charger.findAll({
                where: whereClause,
                include: [{
                    model: ChargerConnector,
                    as: 'connectors'
                }],
                order: [['chargerId', 'ASC']]
            });

            return chargers.map(charger => ({
                id: charger.id,
                chargerId: charger.chargerId,
                chargePointModel: charger.chargePointModel,
                chargePointVendor: charger.chargePointVendor,
                status: charger.status,
                // isOnline: charger.isOnline,
                lastHeartbeat: charger.lastHeartbeat,
                connectors: charger.connectors.map(c => ({
                    connectorId: c.connectorId,
                    status: c.status,
                    connectorType: c.connectorType,
                    maxPower: c.maxPower,
                    errorCode: c.errorCode,
                    lastStatusUpdate: c.lastStatusUpdate
                }))
            }));

        } catch (error) {
            console.error('Error getting all chargers:', error);
            throw error;
        }
    }

    /**
     * Mark charger as offline
     * @param {string} chargePointId - OCPP Charge Point ID
     */
    async markChargerOffline(chargePointId) {
        try {
            await Charger.update(
                { 
                    // isOnline: false,
                    status: 'Offline'
                },
                { 
                    where: { chargerId: chargePointId }
                }
            );

            // Update cache
            // if (this.chargerStates.has(chargePointId)) {
            //     const state = this.chargerStates.get(chargePointId);
            //     state.isOnline = false;
            //     state.status = 'Offline';
            //     this.chargerStates.set(chargePointId, state);
            // }

            console.log(`Marked charger ${chargePointId} as offline`);

        } catch (error) {
            console.error('Error marking charger offline:', error);
        }
    }

    /**
     * Update charger overall status based on connector statuses
     * @param {number} chargerId - Charger database ID
     */
    async updateChargerOverallStatus(chargerId,info) {
        try {
            const connectors = await ChargerConnector.findAll({
                where: { chargerId }
            });

            if (connectors.length === 0) {
                return;
            }

            // Determine overall status based on connector statuses
            let overallStatus = 'Available';
            
            const statuses = connectors.map(c => c.status);
            
            if (statuses.includes('Faulted')) {
                overallStatus = 'Faulted';
            } else if (statuses.includes('Charging')) {
                overallStatus = 'Charging';
            } else if (statuses.includes('Preparing')) {
                overallStatus = 'Preparing';
            } else if (statuses.includes('Unavailable')) {
                overallStatus = 'Unavailable';
            } else if (statuses.every(s => s === 'Available')) {
                overallStatus = 'Available';
            }

            await Charger.update(
                { status: overallStatus, reason: info || '' },
                { where: { id: chargerId } }
            );

        } catch (error) {
            console.error('Error updating charger overall status:', error);
        }
    }

    /**
     * Create default connectors for a new charger
     * @param {number} chargerId - Charger database ID
     */
    async createDefaultConnectors(chargerId) {
        try {
            const defaultConnectors = [
                {
                    chargerId,
                    connectorId: 1,
                    connectorType: 'Type2',
                    maxPower: 22.0,
                    status: 'Available'
                },
                {
                    chargerId,
                    connectorId: 2,
                    connectorType: 'Type2',
                    maxPower: 22.0,
                    status: 'Available'
                }
            ];

            await ChargerConnector.bulkCreate(defaultConnectors);
            console.log(`Created default connectors for charger ${chargerId}`);

        } catch (error) {
            console.error('Error creating default connectors:', error);
        }
    }

    /**
     * Update connector cache
     * @param {string} chargePointId - OCPP Charge Point ID
     * @param {number} connectorId - Connector ID
     * @param {string} status - Status
     */
    updateConnectorCache(chargePointId, connectorId, status) {
        if (!this.chargerStates.has(chargePointId)) {
            this.chargerStates.set(chargePointId, {
                connectors: new Map()
            });
        }

        const chargerState = this.chargerStates.get(chargePointId);
        chargerState.connectors.set(connectorId, {
            status,
            lastUpdate: new Date()
        });
        this.chargerStates.set(chargePointId, chargerState);
    }

    /**
     * Start heartbeat monitoring
     */
    startHeartbeatMonitoring() {
        setInterval(async () => {
            try {
                const cutoffTime = new Date(Date.now() - this.heartbeatTimeout);
                
                // Find chargers that haven't sent heartbeat recently
                const staleChargers = await Charger.findAll({
                    where: {
                        // isOnline: true,
                        status: 'Available',
                        lastHeartbeat: {
                            [Op.lt]: cutoffTime
                        }
                    },
                    attributes: ['chargerId']
                });

                // Mark them as offline
                for (const charger of staleChargers) {
                    await this.markChargerOffline(charger.chargerId);
                }

                if (staleChargers.length > 0) {
                    console.log(`Marked ${staleChargers.length} chargers as offline due to missing heartbeat`);
                }

            } catch (error) {
                console.error('Error in heartbeat monitoring:', error);
            }
        }, 60000); // Check every minute
    }

    /**
     * Get connector availability
     * @param {string} chargePointId - OCPP Charge Point ID
     * @param {number} connectorId - Optional specific connector
     * @returns {Object} Availability status
     */
    async getConnectorAvailability(chargePointId, connectorId = null) {
        try {
            const charger = await Charger.findOne({
                where: { chargerId: chargePointId },
                include: [{
                    model: ChargerConnector,
                    as: 'connectors',
                    where: connectorId ? { connectorId } : {}
                }]
            });

            if (!charger) {
                return { success: false, error: 'Charger not found' };
            }

            const availability = charger.connectors.map(connector => ({
                connectorId: connector.connectorId,
                status: connector.status,
                isAvailable: connector.status === 'Available',
                connectorType: connector.connectorType,
                maxPower: connector.maxPower
            }));

            return {
                success: true,
                chargePointId,
                // isOnline: charger.isOnline,
                status:charger.status,
                connectors: availability
            };

        } catch (error) {
            console.error('Error getting connector availability:', error);
            throw error;
        }
    }

    /**
     * Get charger statistics
     * @param {string} chargePointId - Optional specific charger
     * @returns {Object} Statistics
     */
    async getChargerStatistics(chargePointId = null) {
        try {
            const whereClause = chargePointId ? { chargerId: chargePointId } : {};

            const stats = await Charger.findAll({
                where: whereClause,
                include: [
                    {
                        model: ChargerConnector,
                        as: 'connectors'
                    },
                    {
                        model: ChargingSession,
                        as: 'sessions',
                        where: {
                            status: {
                                [Op.in]: ['Starting', 'Active', 'Suspended']
                            }
                        },
                        required: false
                    }
                ]
            });

            const summary = {
                totalChargers: stats.length,
                // onlineChargers: stats.filter(c => c.isOnline).length,
                // offlineChargers: stats.filter(c => !c.isOnline).length,
                totalConnectors: stats.reduce((sum, c) => sum + c.connectors.length, 0),
                availableConnectors: 0,
                chargingConnectors: 0,
                faultedConnectors: 0,
                activeSessions: stats.reduce((sum, c) => sum + c.sessions.length, 0)
            };

            stats.forEach(charger => {
                charger.connectors.forEach(connector => {
                    switch (connector.status) {
                        case 'Available':
                            summary.availableConnectors++;
                            break;
                        case 'Charging':
                            summary.chargingConnectors++;
                            break;
                        case 'Faulted':
                            summary.faultedConnectors++;
                            break;
                    }
                });
            });

            return summary;

        } catch (error) {
            console.error('Error getting charger statistics:', error);
            throw error;
        }
    }
}

module.exports = ChargerStateManager;
