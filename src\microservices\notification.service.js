const admin = require('firebase-admin');
const { Notification, User } = require('../models');
const { Op } = require('sequelize');
const {RECIPIENT_TYPE} = require('../constants')
const ALLOWED_PUBLIC_TOPICS = new Set([
    'admin_notifications',
    'admin_support_channel',
    'system_announcements'
    // Add other allowed public topics here
]);
/**
 * Send notification to a specific topic
 * @param {String} topic - Firebase topic to send to
 * @param {Object} notification - Notification object with title and body
 * @param {Object} data - Additional data for the notification
 * @param {String} recipientType - Type of recipient (user, admin, or both)
 */
async function sendToTopic(topic, notification, data, recipientType = 'user') {
  const messaging = admin.messaging();
  
  // Convert all data values to strings
  const stringifiedData = {};
  for (const [key, value] of Object.entries(data)) {
    stringifiedData[key] = value === null ? '' : String(value);
  }

  const payload = {
    notification: {
      title: notification.title,
      body: notification.body,
    },
    data: stringifiedData, // Use the stringified data
    topic: topic,
    webpush: {
      headers: {
        Urgency: 'high', // High-priority delivery for web push notifications
      }
    },
    android: {
      priority: 'high', // High-priority delivery for Android
      notification: {
        channel_id: 'high_importance_channel', // Custom notification channel for Android
      },
    },
  };
  try {
    const response = await messaging.send(payload);
    
    // Store notification in database with original data (not stringified)
    await storeNotification({
      userId: data.userId || null,
      isAdminNotification: recipientType === RECIPIENT_TYPE.ADMIN || recipientType === RECIPIENT_TYPE.BOTH,
      title: notification.title,
      body: notification.body,
      type: data.type || 'system',
      referenceId: data.referenceId || null,
      referenceType: data.referenceType || null,
      data: data, // Store original data in database
      firebaseMessageId: response,
      topic: topic,
      recipientType: recipientType
    });
    
    return true;
  } catch (err) {
    console.error('Error sending notification:', err);
    return false;
  }
}

/**
 * Send notification to admin panel
 * @param {Object} notification - Notification object with title and body
 * @param {Object} data - Additional data for the notification
 */
async function sendAdminNotification(notification, data) {
  try {
    // Send to admin topic
    const response = await sendToTopic('admin_notifications', notification, data, 'admin');
    
    return response;
  } catch (error) {
    console.error('Error sending admin notification:', error);
    return false;
  }
}

/**
 * Send notification to both user and admin
 * @param {String} userTopic - User's Firebase topic
 * @param {Object} notification - Notification object with title and body
 * @param {Object} data - Additional data for the notification
 */
async function sendToUserAndAdmin(userTopic, notification, data) {
  try {
    // Send to user topic
    await sendToTopic(userTopic, notification, data, 'user');
    
    // Send to admin topic
    await sendToTopic('admin_notifications', notification, data, 'admin');
    
    // Store a combined notification
    await storeNotification({
      userId: data.userId || null,
      isAdminNotification: true,
      title: notification.title,
      body: notification.body,
      type: data.type || 'system',
      referenceId: data.referenceId || null,
      referenceType: data.referenceType || null,
      data: data,
      firebaseMessageId: `combined_${Date.now()}`,
      topic: 'both',
      recipientType: RECIPIENT_TYPE.BOTH
    });
    
    return true;
  } catch (error) {
    console.error('Error sending combined notification:', error);
    return false;
  }
}

async function storeNotification(notificationData) {
  try {
    const notification = await Notification.create(notificationData);
    return notification;
  } catch (error) {
    console.error('Error storing notification:', error);
    throw error;
  }
}

async function getUserNotifications(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Notification.findAndCountAll({
      where: { 
        [Op.and]: [
          // Only show notifications specifically for this user
          { userId },
          {
            // Exclude chat notifications from user notifications
            [Op.or]: [
              { referenceType: { [Op.ne]: 'chat' } },
              { referenceType: null }
            ]
          }
        ]
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
    
    return {
      notifications: rows,
      totalCount: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit)
    };
}

async function getAdminNotifications(page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    
    const { count, rows } = await Notification.findAndCountAll({
      where: { 
        [Op.and]: [
          {
            [Op.or]: [
              { isAdminNotification: true },
              { 
                recipientType: RECIPIENT_TYPE.BOTH 
              }
            ]
          },
          {
            // Exclude chat notifications from admin panel
            [Op.or]: [
              { referenceType: { [Op.ne]: 'chat' } },
              { referenceType: null }
            ]
          }
        ]
      },
      order: [['createdAt', 'DESC']],
      limit,
      offset
    });
    
    return {
      notifications: rows,
      totalCount: count,
      currentPage: page,
      totalPages: Math.ceil(count / limit)
    };
}

async function markNotificationAsRead(notificationId, userId = null, isAdmin = false) {
    const whereClause = { id: notificationId };
    
    if (isAdmin) {
      whereClause[Op.or] = [
        { isAdminNotification: true },
        { recipientType: RECIPIENT_TYPE.BOTH }
      ];
    } else if (userId) {
      whereClause[Op.or] = [
        { userId },
        { recipientType: RECIPIENT_TYPE.BOTH }
      ];
    }
    
    const notification = await Notification.findOne({ where: whereClause });
    
    if (!notification) {
      throw new Error('Notification not found or unauthorized');
    }
    
    await notification.update({ isRead: true });
    return notification;
}

async function markAllNotificationsAsRead(userId = null, isAdmin = false) {

    const whereClause = { isRead: false };
    
    if (isAdmin) {
      whereClause[Op.or] = [
        { isAdminNotification: true },       
        { recipientType: RECIPIENT_TYPE.BOTH },
        { recipientType: RECIPIENT_TYPE.ADMIN } 
      ];
    } else if (userId) {
      whereClause[Op.or] = [
        { userId },
        { recipientType: RECIPIENT_TYPE.BOTH },
        { recipientType: RECIPIENT_TYPE.USER } 
      ];
    }
    
    await Notification.update(
      { isRead: true },
      { where: whereClause }
    );
    return true;
}

async function getUnreadNotificationCount(userId = null, isAdmin = false) {

    let whereClause = { isRead: false };
    
    if (isAdmin) {
      whereClause[Op.and] = [
        {
          [Op.or]: [
            { isAdminNotification: true },
            { recipientType: RECIPIENT_TYPE.BOTH }
          ]
        },
        {
          // Exclude chat notifications from admin panel count
          [Op.or]: [
            { referenceType: { [Op.ne]: 'chat' } },
            { referenceType: null }
          ]
        }
      ];
    } else if (userId) {
      whereClause = {
        userId,
        recipientType:RECIPIENT_TYPE.USER,
        isAdminNotification: false,
        referenceType: { [Op.ne]: 'chat' },
        isRead:false
      };
    }
    
    const count = await Notification.count({
      where: whereClause
    });
    return count;
}

async function subscribeToTopic(req, res) {
    try {
        const { topic, token } = req.body;
        const userId = req.user.id;

        let finalTopic;

        if (topic === 'user') {
            // Secure private topic (based on logged-in user ID)
            finalTopic = `user_${userId}`;
            console.log(finalTopic)
        } else if (ALLOWED_PUBLIC_TOPICS.has(topic)) {
            // Valid public topic
            finalTopic = topic;
            console.log(finalTopic)
        } else {
            return res.status(403).json({ 
                status: false,
                message: 'Unauthorized or invalid topic.' 
            });
        }

        await admin.messaging().subscribeToTopic(token, finalTopic);
        
        return res.status(200).json({ 
            status: true,
            message: `Successfully subscribed to topic: ${finalTopic}`,
            data: {
                topic: finalTopic,
                token: token
            }
        });

    } catch (error) {
        console.error('Error subscribing to topic:', error);
        return res.status(500).json({ 
            status: false,
            message: 'Internal server error during topic subscription.',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}

async function unsubscribeFromTopic(req, res) {
    try {
        const { topic, token } = req.body;
        const userId = req.user.id;

   
        let finalTopic;

        if (topic === 'user') {
            // Secure private topic (based on logged-in user ID)
            finalTopic = `user_${userId}`;
        } else if (ALLOWED_PUBLIC_TOPICS.includes(topic)) {
            // Valid public topic
            finalTopic = topic;
        } else {
            return res.status(403).json({ 
                status: false,
                message: 'Unauthorized or invalid topic.' 
            });
        }

        await admin.messaging().unsubscribeFromTopic(token, finalTopic);
        
        return res.status(200).json({ 
            status: true,
            message: `Successfully unsubscribed from topic: ${finalTopic}`,
            data: {
                topic: finalTopic,
                token: token
            }
        });

    } catch (error) {
        console.error('Error unsubscribing from topic:', error);
        return res.status(500).json({ 
            status: false,
            message: 'Internal server error during topic unsubscription.',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}

module.exports = {
  sendToTopic,
  sendAdminNotification,
  sendToUserAndAdmin,
  storeNotification,
  getUserNotifications,
  getAdminNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount,
  subscribeToTopic,
  unsubscribeFromTopic
};
