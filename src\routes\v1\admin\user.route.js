const express = require('express');
const router = express.Router();
const {adminAuth, superAdminAuth} = require('../../../middlewares/adminAuth');
const userController = require('../../../controllers/admin/user.controller');
const validate = require('../../../middlewares/validate');
const userValidation = require('../../../validations/admin/user.validation');

router.route('/').get(adminAuth, validate(userValidation.getUsers), userController.getUsers);

/**
 * @api {get} v1/admin/users/stats Get user statistics
 * @apiDescription Get statistics about users including total count, new users, active/inactive users with YoY comparison
 * @apiName GetUserStats
 *
 * @apiSuccess {Object} data User statistics
 * @apiSuccess {Object} data.totalUsers Total users count and growth
 * @apiSuccess {Object} data.newUsers New users count and growth
 * @apiSuccess {Object} data.activeUsers Active users count and growth
 * @apiSuccess {Object} data.inactiveUsers Inactive users count and growth
 */
router.route('/stats').get(adminAuth, userController.getUserStats);

/**
 * @api {get} v1/admin/users/:userId Get user by ID
 * @apiDescription Get user details by user ID
 * @apiName GetUserById
 * @apiParam {String} userId User's unique ID
 * 
 * @apiSuccess {Object} user User object
 * @apiError (404) {Object} error User not found
 */
router.route('/:userId').get(adminAuth, validate(userValidation.getUserById), userController.getUserById);

// Add the new route for toggling user status
router
  .route('/:userId/toggle-status')
  .patch(adminAuth, validate(userValidation.toggleUserStatus), userController.toggleUserStatus);

// Delete user
router.route('/:userId').delete(adminAuth, validate(userValidation.deleteUser), userController.deleteUser);

// Create User
router.route('/').post(superAdminAuth, validate(userValidation.createUser), userController.createUser);

//reset-password

router
  .route('/reset-password')
  .patch(superAdminAuth, validate(userValidation.resetPassword), userController.resetPassword);

/**
 * @api {get} v1/admin/users/:userId/bookings Get user bookings
 * @apiDescription Get all bookings for a specific user with optional filters
 * @apiName GetUserBookings
 * @apiGroup Admin
 * 
 * @apiParam {Number} userId User's unique ID
 * @apiQuery {Number} [page=1] Page number
 * @apiQuery {Number} [limit=10] Number of items per page
 * @apiQuery {String} [status] Filter by booking status (completed/cancelled/pending)
 * @apiQuery {String} [fromDate] Filter by start date (YYYY-MM-DD)
 * @apiQuery {String} [toDate] Filter by end date (YYYY-MM-DD)
 * 
 * @apiSuccess {Object[]} bookings List of bookings
 * @apiSuccess {Number} total Total number of bookings
 * @apiSuccess {Number} page Current page
 * @apiSuccess {Number} limit Items per page
 */
router
  .route('/:userId/bookings')
  .get(adminAuth, validate(userValidation.getUserBookings), userController.getUserBookings);

/**
 * @api {get} v1/admin/users/bookings/:bookingId Get booking details
 * @apiDescription Get detailed booking information by booking ID
 * @apiName GetBookingDetails
 * @apiGroup Admin
 * 
 * @apiParam {Number} bookingId Booking's unique ID
 * 
 * @apiSuccess {Object} data Booking details including station, vehicle, and charger information
 */
router
  .route('/bookings/:bookingId')
  .get(adminAuth, validate(userValidation.getBookingDetails), userController.getBookingDetails);

module.exports = router;
