const { Op } = require('sequelize');
const { Booking,StationBlock } = require('../models');
const { BOOKING_STATUS } = require('../constants');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const moment = require('moment-timezone');
const logger = require('../config/logger');

/**
 * Checks if the requested time slot is available for booking.
 */
const isSlotAvailable = async (chargerId, startTime, endTime) => {
    const overlappingCount = await Booking.count({
        where: {
            chargerId,
            [Op.or]: [
                { startTime: { [Op.lt]: endTime }, endTime: { [Op.gt]: startTime } },
            ],
            status: { [Op.in]: [BOOKING_STATUS.PENDING, BOOKING_STATUS.UPCOMING] },
        },
    });

    return overlappingCount === 0; // Returns true if no overlapping booking exists
};


/**
 * Checks if the arrival time is within the station's operational hours.
 */
const isWithinOperationalHours = async (station, startTime, endTime) => {
    if (!station.operationalSchedule?.length) {
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Station operational schedule is not set');
    }

    const startTimeUTC = moment.utc(startTime);
    const endTimeUTC = moment.utc(endTime);
    const bookingDay = startTimeUTC.format('dddd'); // e.g., "Friday"
    const previousDay = moment.utc(startTimeUTC).subtract(1, 'day').format('dddd');

    // Check if the station is blocked
    const blockedPeriod = await StationBlock.findOne({
        where: {
            stationId: station.id,
            isActive: true,
            [Op.or]: [
                {
                    startDate: { [Op.lte]: startTimeUTC.toDate() },
                    endDate: { [Op.gte]: startTimeUTC.toDate() },
                },
                {
                    startDate: { [Op.lte]: endTimeUTC.toDate() },
                    endDate: { [Op.gte]: endTimeUTC.toDate() },
                }
            ],
        }
    });

    if (blockedPeriod) {
        logger.info(`Station ${station.id} is blocked from ${blockedPeriod.startDate} to ${blockedPeriod.endDate}`);
        throw new ApiError(httpStatus.BAD_REQUEST, `Station is closed from ${moment(blockedPeriod.startDate).format("YYYY-MM-DD")} to ${moment(blockedPeriod.endDate).subtract(1, 'second').utc().format("YYYY-MM-DD")}`);
    }

    const checkOperationalWindow = (daySchedule, referenceDate) => {
        if (!daySchedule) return false;

        const startUTC = moment.utc(referenceDate).set({
            hour: moment.utc(daySchedule.startTime, "HH:mm:ss").hour(),
            minute: moment.utc(daySchedule.startTime, "HH:mm:ss").minute(),
            second: moment.utc(daySchedule.startTime, "HH:mm:ss").second(),
        });

        let endUTC = moment.utc(referenceDate).set({
            hour: moment.utc(daySchedule.endTime, "HH:mm:ss").hour(),
            minute: moment.utc(daySchedule.endTime, "HH:mm:ss").minute(),
            second: moment.utc(daySchedule.endTime, "HH:mm:ss").second(),
        });

        if (endUTC.isBefore(startUTC)) {
            endUTC.add(1, 'day'); // handle overnight
        }

        logger.debug(`Checking window: ${startUTC.format()} - ${endUTC.format()}`);

        const isStartTimeValid = startTimeUTC.isBetween(startUTC, endUTC, null, '[]');
        const isEndTimeValid = endTimeUTC.isBetween(startUTC, endUTC, null, '[]');

        return isStartTimeValid && isEndTimeValid;
    };

    const currentDaySchedule = station.operationalSchedule.find(day => day.day === bookingDay);
    const previousDaySchedule = station.operationalSchedule.find(day => day.day === previousDay);

    const isInCurrentWindow = checkOperationalWindow(currentDaySchedule, startTimeUTC);
    const isInPreviousWindow = checkOperationalWindow(previousDaySchedule, moment.utc(startTimeUTC).subtract(1, 'day'));

    if (!isInCurrentWindow && !isInPreviousWindow) {
        throw new ApiError(httpStatus.BAD_REQUEST, `Selected time is outside station operational hours for ${bookingDay}`);
    }

    return true;
};




/**
 * Calculates the total cost of the booking.
 */
const calculateTotalCost = (pricePerHour, durationInMinutes) => {
    const durationInHours = durationInMinutes / 60;
    return pricePerHour * durationInHours;
};


module.exports = {
    isSlotAvailable,
    isWithinOperationalHours,
    calculateTotalCost
};
