const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const  policyService  = require('../../services/admin/policy.service');
const ApiError = require('../../utils/ApiError');

/**
 * Create a new policy
 */
const createPolicy = catchAsync(async (req, res) => {
  const policy = await policyService.createPolicy(req.body);
  
  res.status(httpStatus.CREATED).json({
    status: true,
    data: policy,
    message: 'Policy created successfully'
  });
});

/**
 * Get all policies with pagination and filtering
 */
const getPolicies = catchAsync(async (req, res) => {
  const { type, userType, page, limit, sortBy, sortOrder } = req.query;
  const filters = {};
  
  if (type) filters.type = type;
  if (userType) filters.userType = userType;
  
  const result = await policyService.getPolicies(filters, {
    page,
    limit,
    sortBy,
    sortOrder
  });
  
  res.status(httpStatus.OK).json({
    status: true,
    data: result.policies,
    pagination: result.pagination,
    message: 'Policies retrieved successfully'
  });
});

/**
 * Get policy by ID
 */
const getPolicy = catchAsync(async (req, res) => {
  const { policyId } = req.params;
  const policy = await policyService.getPolicyById(policyId);
  
  if (!policy) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Policy not found');
  }
  
  res.status(httpStatus.OK).json({
    status: true,
    data: policy,
    message: 'Policy retrieved successfully'
  });
});

/**
 * Update policy
 */
const updatePolicy = catchAsync(async (req, res) => {
  const { policyId } = req.params;
  const policy = await policyService.updatePolicy(policyId, req.body);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: policy,
    message: 'Policy updated successfully'
  });
});

/**
 * Toggle policy status (active/inactive)
 */
const togglePolicyStatus = catchAsync(async (req, res) => {
  const { policyId } = req.params;
  const policy = await policyService.togglePolicyStatus(policyId);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: policy,
    message: `Policy ${policy.isActive ? 'activated' : 'deactivated'} successfully`
  });
});

/**
 * Delete policy
 */
const deletePolicy = catchAsync(async (req, res) => {
  const { policyId } = req.params;
  await policyService.deletePolicy(policyId);
  
  res.status(httpStatus.OK).json({
    status: true,
    message: 'Policy deleted successfully'
  });
});

module.exports = {
  createPolicy,
  getPolicies,
  getPolicy,
  updatePolicy,
  togglePolicyStatus,
  deletePolicy
};