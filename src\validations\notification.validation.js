const Joi = require('joi');
const { ALLOWED_PUBLIC_TOPICS } = require('../constants/notification');

const getNotifications = {
    query: Joi.object().keys({
        page: Joi.number().integer().min(1).optional().default(1),
        limit: Joi.number().integer().min(1).max(100).optional().default(10),
    }),
};

const markAsRead = {
    params: Joi.object().keys({
        notificationId: Joi.string().required(),
    }),
};

const subscribeToTopic = {
    body: Joi.object().keys({
        topic: Joi.string()
            .required()
            .valid('user', ...ALLOWED_PUBLIC_TOPICS),
        token: Joi.string().required()
    })
};

const unsubscribeFromTopic = {
    body: Joi.object().keys({
        topic: Joi.string()
            .required()
            .valid('user', ...ALLOWED_PUBLIC_TOPICS),
        token: Joi.string().required()
    })
};

module.exports = {
    getNotifications,
    markAsRead,
    subscribeToTopic,
    unsubscribeFromTopic
};
