'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    // Add the isPrimary column to user_vehicles
     await queryInterface.addColumn('user_vehicles', 'isPrimary', {
      type: Sequelize.BOOLEAN,
      allowNull: true,  // Allow NULL initially
    });

    // Set the first vehicle as primary and others as false
    await queryInterface.sequelize.query(`
      WITH ranked_vehicles AS (
        SELECT id, "userId",
               ROW_NUMBER() OVER (PARTITION BY "userId" ORDER BY id ASC) AS row_num
        FROM user_vehicles
      )
      -- Update the first vehicle as primary and others as false
      UPDATE user_vehicles
      SET "isPrimary" = CASE 
        WHEN row_num = 1 THEN TRUE  -- First vehicle gets TRUE
        ELSE FALSE                 -- All others get FALSE
      END
      FROM ranked_vehicles
      WHERE user_vehicles.id = ranked_vehicles.id;
    `);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('user_vehicles', 'isPrimary');
  }
};
