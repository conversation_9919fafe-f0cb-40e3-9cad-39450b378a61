const Joi = require('joi');

const initializePayment = {
  body: Joi.object().keys({
    walletId: Joi.number().integer().required(),
    amount: Joi.number().min(250).required().description('Amount must be at least 250 IQD'),
  }),
};

const handlePaymentCallback = {
  query: Joi.object().keys({
    token: Joi.string().required(),
  }),
};

module.exports = {
  initializePayment,
  handlePaymentCallback,
};
