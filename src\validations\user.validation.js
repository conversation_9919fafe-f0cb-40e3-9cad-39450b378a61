const Joi = require('joi');

const addUserVehicle = {
  body: Joi.object().keys({
    // Brand validation
    brandId: Joi.number(),
    brandName: Joi.string().when('brandId', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }),
    // Model validation
    modelId: Joi.number(),
    modelName: Joi.string().when('modelId', {
      is: Joi.exist(),
      then: Joi.optional(),
      otherwise: Joi.required()
    }),
    // Trim validation
    // trimId: Joi.number(),
    // trimName: Joi.string().when('trimId', {
    //   is: Joi.exist(),
    //   then: Joi.optional(),
    //   otherwise: Joi.required()
    // }),
    // Required IDs
    manufacturingUnitId: Joi.number().required(),
    batteryCapacityId: Joi.number().required(),
    // Registration number
    // registrationNumber: Joi.string().required()
  })
};

const createUser = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
    password: Joi.string().required().min(6),
    name: Joi.string().required(),
    phone: Joi.string().optional(),
    // Additional optional fields
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    country: Joi.string().optional(),
  }),
};

module.exports = {
  addUserVehicle,
  createUser,
}; 
