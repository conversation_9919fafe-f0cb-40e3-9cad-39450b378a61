const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const revenueService = require('../../services/admin/revenue.service');
const logger = require('../../config/logger');

/**
 * Get revenue KPIs
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Revenue KPIs
 */
const getRevenueKPIs = catchAsync(async (req, res) => {
  logger.info('Admin requesting revenue KPIs');
  
  const revenueData = await revenueService.getRevenueKPIs();
  
  res.status(httpStatus.OK).json({
    status: true,
    data: revenueData,
    message: 'Revenue KPIs fetched successfully'
  });
});

module.exports = {
  getRevenueKPIs
};