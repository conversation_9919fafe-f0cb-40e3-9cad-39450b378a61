const { authService, } = require('../services');
const catchAsync = require('../utils/catchAsync');
const { User } = require('../models');
const httpStatus = require('http-status');
const createNewUserObject = newUser => ({
  email: newUser.email,
  firebaseUid: newUser.uid,
  profilePicUrl: newUser.picture,
  phone: newUser.phoneNumber,
  isEmailVerified: newUser.isEmailVerified,
  firebaseSignInProvider: newUser.firebase.sign_in_provider,
});
const login = catchAsync(async (req, res) => {
  const user = req.user;

  return res.status(httpStatus.OK).json({ status: true, data: user, message: "User logged in successfully." });

});
const register = catchAsync(async (req, res, next) => {

  let user;

  if (req.user) {
    res.status(httpStatus.CONFLICT).send({ message: 'User already exist' });
    // } else if (!req.newUser.email_verified) {
    //   res.status(401).send({ message: "Email not verified" });
  } else {
    const userObj = {
      ...createNewUserObject(req.newUser),
      ...req.body
    }

    user = await authService.create(userObj);

  }

  return res.status(httpStatus.CREATED).send({ status: true, data: user });

});

const changePassword = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const email = req.user.email;
  const { oldPassword, newPassword } = req.body;

  const result = await authService.changePassword(userId, email, oldPassword, newPassword);
  return res.status(httpStatus.OK).json({ status: true, data: result, message: "Password changed successfully." });
});

module.exports = {
  register,
  login,
  changePassword
};