const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const helpCenterService = require('../services/helpCenter.service');

/**
 * Create a new help center ticket
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Created ticket
 */
const createTicket = catchAsync(async (req, res) => {
  const userId = req.user.id;
  const { email, query } = req.body;
  
  const ticket = await helpCenterService.createTicket(userId, email, query);
  
  res.status(httpStatus.CREATED).json({
    status: true,
    data: ticket,
    message: 'Support ticket created successfully'
  });
});

/**
 * Get all live FAQs for mobile users with optional filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} List of filtered live FAQs
 */
const getLiveFAQs = catchAsync(async (req, res) => {
  const { type, search,page, limit } = req.query;
  
  const faqs = await helpCenterService.getLiveFAQs({
    type,
    search,
    page,
    limit
  });
  
  res.status(httpStatus.OK).json({
    status: true,
    data: faqs,
    message: 'FAQs fetched successfully'
  });
});

module.exports = {
  createTicket,
  getLiveFAQs
};
