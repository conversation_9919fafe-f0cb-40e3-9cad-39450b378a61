const express = require('express');
const router = express.Router();
const { superAdminAuth } = require('../../../middlewares/adminAuth');
const authController = require('../../../controllers/admin/auth.controller');
const validate = require('../../../middlewares/validate');
const adminAuthValidation = require('../../../validations/admin/auth.validation');

router
  .route('/login')
  .post(validate(adminAuthValidation.login), authController.login);

router
  .route('/create')
  .post(
    superAdminAuth,
    validate(adminAuthValidation.createAdmin),
    authController.createAdmin
  );

module.exports = router; 
