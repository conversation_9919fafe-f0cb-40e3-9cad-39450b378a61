const User = require('./user.model');
const VehicleModel = require('./vehicleModel.model');
const VehicleBrand = require('./vehicleBrand.model');
const VehicleTrim = require('./vehicleTrim.model');
const UserVehicle = require('./userVehicle.model');
const ManufacturingUnit = require('./vehicleManufacturing.model');
const BatteryCapacity = require('./vehicleBattery.model');
const Charger = require('./charger.model');
const Connector = require('./connector.model');
const Station = require('./station.model');
const Power = require('./power.model');
const Booking = require('./booking.model');
const Vendor = require('./vendor.model');
const Amenity = require('./amenity.model');
const StationBlock = require('./stationBlock.model');
const Bookmark = require('./bookmark.model');
const Chat = require('./chat.model');
const ChatMessage = require('./chatMessage.model');
const Reviews = require('./review.model');
const Wallet = require('./wallet.model');
const WalletTransaction = require('./walletTransaction.model');
const Admin = require('../models/admin/admin.model');
const Notification = require('./notification.model');
const SubscriptionPlan = require('./admin/subscriptionPlan.model');
const VendorSubscription = require('./vendorSubscription.model');
const PaymentTransaction = require('./paymentTransaction.model');
const Policy = require('./admin/policy.model');
const HelpCenterTicket = require('./admin/helpCenterTicket.model');
const FAQ = require('./admin/faq.model');

// Define relationships
VehicleBrand.hasMany(VehicleModel, { foreignKey: 'brandId' });
VehicleModel.belongsTo(VehicleBrand, { foreignKey: 'brandId' });



UserVehicle.belongsTo(VehicleBrand, { foreignKey: 'brandId' });
UserVehicle.belongsTo(VehicleModel, { foreignKey: 'modelId' });
UserVehicle.belongsTo(ManufacturingUnit, { foreignKey: 'manufacturingUnitId' });
UserVehicle.belongsTo(BatteryCapacity, { foreignKey: 'batteryCapacityId' });

Station.belongsToMany(Amenity, { through: 'station_amenities', foreignKey: 'stationId', as: 'amenities' });
Amenity.belongsToMany(Station, { through: 'station_amenities', foreignKey: 'amenityId', as: 'stations' });
Station.belongsTo(Vendor, { foreignKey: 'vendorId' });
Vendor.hasMany(Station, { foreignKey: 'vendorId' });

User.hasOne(Vendor, { foreignKey: 'userId', as: 'vendor' }); // A User can be a Vendor
Vendor.belongsTo(User, { foreignKey: 'userId' }); // Vendor belongs to that User



StationBlock.belongsTo(Station, { foreignKey: 'stationId' });
Station.hasMany(StationBlock, { foreignKey: 'stationId' });

// Charger relationships
Station.hasMany(Charger, { foreignKey: 'stationId' });
Charger.belongsTo(Station, { foreignKey: 'stationId' });
Charger.belongsTo(Connector, { foreignKey: 'connectorId' });
Connector.hasMany(Charger, { foreignKey: 'connectorId' });
Charger.belongsTo(Power, { foreignKey: 'powerId' });
Power.hasMany(Charger, { foreignKey: 'powerId' });

//Booking relationships
Booking.belongsTo(User, { foreignKey: 'userId' });
User.hasMany(Booking, { foreignKey: 'userId', as: 'bookings' });
Booking.belongsTo(Station, { foreignKey: 'stationId' });
Booking.belongsTo(Charger, { foreignKey: 'chargerId' });
Booking.belongsTo(UserVehicle, { foreignKey: 'vehicleId' });
Booking.hasMany(WalletTransaction, {
  foreignKey: 'bookingId', // The foreign key in WalletTransaction
  as: 'walletTransactions', // Alias for the relationship
});
// Define Station and StationBlock association
Station.hasMany(StationBlock, {
  foreignKey: 'stationId',
});

StationBlock.belongsTo(Station, {
  foreignKey: 'stationId',
});

// User can have many bookmarks
User.hasMany(Bookmark, { foreignKey: 'userId' });
Bookmark.belongsTo(User, { foreignKey: 'userId' });

// Station can have many bookmarks
Station.hasMany(Bookmark, { foreignKey: 'stationId' });
Bookmark.belongsTo(Station, { foreignKey: 'stationId' });


// For user messages
ChatMessage.belongsTo(User, {
  foreignKey: 'senderId',
  as: 'sender',
  constraints: false
});

// For admin messages
ChatMessage.belongsTo(Admin, {
  foreignKey: 'senderId',
  as: 'adminSender',
  constraints: false
});

// reviews relationships
User.hasMany(Reviews, { foreignKey: 'userId' });
Reviews.belongsTo(User, { foreignKey: 'userId', as: 'reviewer'});

// Wallet relationships
User.hasOne(Wallet, {
  foreignKey: 'ownerId',
  constraints: false,
  scope: {
    ownerType: 'user'
  },
  as: 'wallet'
});

Wallet.belongsTo(User, {
  foreignKey: 'ownerId',
  constraints: false,
  as: 'userOwner'
});

Vendor.hasOne(Wallet, {
  foreignKey: 'ownerId',
  constraints: false,
  scope: {
    ownerType: 'vendor'
  },
  as: 'wallet'
});

Wallet.belongsTo(Vendor, {
  foreignKey: 'ownerId',
  constraints: false,
  as: 'vendorOwner'
});

Wallet.hasMany(WalletTransaction, { foreignKey: 'walletId' });
WalletTransaction.belongsTo(Wallet, { foreignKey: 'walletId' });

WalletTransaction.hasOne(WalletTransaction, { foreignKey: 'relatedTransactionId', as: 'relatedTransaction' });
WalletTransaction.belongsTo(WalletTransaction, { foreignKey: 'relatedTransactionId', as: 'parentTransaction' });

WalletTransaction.belongsTo(Booking, {
  foreignKey: 'bookingId', // The foreign key in WalletTransaction
  as: 'booking', // Alias for the relationship
});

// Notification relationships
User.hasMany(Notification, { foreignKey: 'userId' });
Notification.belongsTo(User, { foreignKey: 'userId' });

// Vendor Subscription relationships
Vendor.hasMany(VendorSubscription, { foreignKey: 'vendorId' });
VendorSubscription.belongsTo(Vendor, { foreignKey: 'vendorId' });

SubscriptionPlan.hasMany(VendorSubscription, { foreignKey: 'subscriptionPlanId' });
VendorSubscription.belongsTo(SubscriptionPlan, { foreignKey: 'subscriptionPlanId' });

// Self-reference for renewal tracking
VendorSubscription.belongsTo(VendorSubscription, {
  foreignKey: 'renewedFromId',
  as: 'previousSubscription'
});
VendorSubscription.hasOne(VendorSubscription, {
  foreignKey: 'renewedFromId',
  as: 'renewedSubscription'
});

// Payment Transaction relationships
User.hasMany(PaymentTransaction, { foreignKey: 'userId' });
PaymentTransaction.belongsTo(User, { foreignKey: 'userId' });

module.exports = {
  User,
  VehicleBrand,
  VehicleModel,
  VehicleTrim,
  ManufacturingUnit,
  BatteryCapacity,
  UserVehicle,
  Charger,
  Connector,
  Power,
  Booking,
  Station,
  Vendor,
  StationBlock,
  Bookmark,
  Amenity,
  Chat,
  ChatMessage,
  Reviews,
  Wallet,
  WalletTransaction,
  Admin,
  WalletTransaction,
  Notification,
  SubscriptionPlan,
  VendorSubscription,
  PaymentTransaction,
  Policy,
  HelpCenterTicket,
  FAQ
};
