const Joi = require('joi');
const moment = require('moment');

const getSubscriptionPlans = {
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be greater than or equal to 1'
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be greater than or equal to 1',
        'number.max': 'Limit cannot exceed 100'
      }),
    sortBy: Joi.string()
      .optional()
      .default('displayOrder:asc,createdAt:desc')
      .custom((value, helpers) => {
        // Validate sort format: field:direction,field2:direction2
        const sortPattern = /^[\w]+(:(asc|desc))?(,[\w]+(:(asc|desc))?)*$/i;
        if (!sortPattern.test(value)) {
          return helpers.error('string.sortFormat');
        }

        // Validate allowed fields
        const allowedFields = [
          'displayOrder',
          'name',
          'price',
          'createdAt',
          'updatedAt',
          'isActive',
          'durationValue',
          'durationType'
        ];

        const sortParts = value.split(',');
        for (const part of sortParts) {
          const [field] = part.split(':');
          if (!allowedFields.includes(field)) {
            return helpers.error('string.sortField', { field });
          }
        }

        return value;
      })
      .messages({
        'string.sortFormat': 'Invalid sort format. Example: field:asc,field2:desc',
        'string.sortField': '{{#field}} is not a valid sort field'
      }),
    name: Joi.string()
      .trim()
      .optional()
      .max(50)
      .messages({
        'string.max': 'Name filter cannot exceed 50 characters'
      }),
    isActive: Joi.boolean()
      .optional()
      .messages({
        'boolean.base': 'isActive must be a boolean'
      }),
    priceMin: Joi.number()
      .optional()
      .min(0)
      .messages({
        'number.base': 'Minimum price must be a number',
        'number.min': 'Minimum price cannot be negative'
      }),
    priceMax: Joi.number()
      .optional()
      .min(Joi.ref('priceMin'))
      .messages({
        'number.base': 'Maximum price must be a number',
        'number.min': 'Maximum price must be greater than minimum price'
      }),
    fromDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'From date must be in YYYY-MM-DD format'
      }),
    toDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        // Check if toDate is after fromDate
        const fromDate = helpers.state.ancestors[0].fromDate;
        if (fromDate && moment(value).isBefore(moment(fromDate))) {
          return helpers.error('string.dateRange');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'To date must be in YYYY-MM-DD format',
        'string.dateRange': 'To date must be after from date'
      }),
    durationType: Joi.string()
      .optional()
      .valid('month', 'year')
      .messages({
        'any.only': 'Duration type must be either month or year'
      })
  }),
};

const addSubscriptionPlan = {
  body: Joi.object().keys({
    name: Joi.string()
      .required()
      .min(2)
      .max(50)
      .trim()
      .messages({
        'string.empty': 'Name is required',
        'string.min': 'Name must be at least 2 characters long',
        'string.max': 'Name cannot exceed 50 characters',
        'any.required': 'Name is required'
      }),
    description: Joi.string()
      .optional()
      .allow('')
      .max(500)
      .trim()
      .messages({
        'string.max': 'Description cannot exceed 500 characters'
      }),
    durationValue: Joi.number()
      .integer()
      .required()
      .min(1)
      .messages({
        'number.base': 'Duration value must be a number',
        'number.integer': 'Duration value must be an integer',
        'number.min': 'Duration value must be at least 1',
        'any.required': 'Duration value is required'
      }),
    durationType: Joi.string()
      .required()
      .valid('month', 'year')
      .messages({
        'string.empty': 'Duration type is required',
        'any.only': 'Duration type must be either month or year',
        'any.required': 'Duration type is required'
      }),
    price: Joi.number()
      .required()
      .min(0)
      .precision(2)
      .messages({
        'number.base': 'Price must be a number',
        'number.min': 'Price cannot be negative',
        'number.precision': 'Price cannot have more than 2 decimal places',
        'any.required': 'Price is required'
      }),
    currency: Joi.string()
      .optional()
      .default('USD')
      .length(3)
      .uppercase()
      .messages({
        'string.length': 'Currency must be a 3-letter code',
        'string.uppercase': 'Currency must be uppercase'
      }),
    benefits: Joi.array()
      .items(
        Joi.object({
          name: Joi.string()
            .required()
            .trim()
            .max(100)
            .messages({
              'string.empty': 'Benefit name is required',
              'string.max': 'Benefit name cannot exceed 100 characters',
              'any.required': 'Benefit name is required'
            }),
          isIncluded: Joi.boolean()
            .required()
            .messages({
              'boolean.base': 'isIncluded must be a boolean',
              'any.required': 'isIncluded is required'
            })
        })
      )
      .default([])
      .messages({
        'array.base': 'Benefits must be an array'
      }),
    isActive: Joi.boolean()
      .optional()
      .default(true)
      .messages({
        'boolean.base': 'isActive must be a boolean'
      }),
    displayOrder: Joi.number()
      .integer()
      .min(1)
      .optional()
      .messages({
        'number.base': 'Display order must be a number',
        'number.integer': 'Display order must be an integer',
        'number.min': 'Display order must be at least 1'
      })
  }),
};

const updateSubscriptionPlan = {
  params: Joi.object().keys({
    planId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Plan ID must be a number',
        'number.integer': 'Plan ID must be an integer',
        'any.required': 'Plan ID is required'
      })
  }),
  body: Joi.object().keys({
    name: Joi.string()
      .min(2)
      .max(50)
      .trim(),
    description: Joi.string()
      .allow('')
      .max(500)
      .trim(),
    durationValue: Joi.number()
      .integer()
      .min(1),
    durationType: Joi.string()
      .valid('month', 'year'),
    price: Joi.number()
      .min(0)
      .precision(2),
    currency: Joi.string()
      .length(3)
      .uppercase(),
    benefits: Joi.array()
      .items(
        Joi.object({
          name: Joi.string()
            .required()
            .trim()
            .max(100),
          isIncluded: Joi.boolean()
            .required()
        })
      ),
    isActive: Joi.boolean(),
    displayOrder: Joi.number()
      .integer()
      .min(1)
  })
    .min(1)
    .messages({
      'object.min': 'At least one field must be provided for update'
    }),
};

const deleteSubscriptionPlan = {
  params: Joi.object().keys({
    planId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Plan ID must be a number',
        'number.integer': 'Plan ID must be an integer',
        'any.required': 'Plan ID is required'
      })
  }),
};

const togglePlanStatus = {
  params: Joi.object().keys({
    planId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Plan ID must be a number',
        'number.integer': 'Plan ID must be an integer',
        'any.required': 'Plan ID is required'
      })
  }),
};

const getVendorsByPlanId = {
  params: Joi.object().keys({
    planId: Joi.number().integer().required()
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10)
  })
};

module.exports = {
  getSubscriptionPlans,
  addSubscriptionPlan,
  updateSubscriptionPlan,
  deleteSubscriptionPlan,
  togglePlanStatus,
  getVendorsByPlanId
};
