const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const { zaincashService } = require('../services');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Initialize a ZainCash payment
 */
const initializePayment = catchAsync(async (req, res) => {
  // const userId = req.user.id;
  const userId = 24;

  const { walletId, amount } = req.body;

  logger.info(`Initializing ZainCash payment - userId: ${userId}, walletId: ${walletId}, amount: ${amount}`);

  const paymentData = {
    userId,
    walletId,
    amount
  };

  const result = await zaincashService.initializePayment(paymentData);

  logger.info(`ZainCash payment initialized - orderId: ${result.orderId}, transactionId: ${result.transactionId}`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Payment initialized successfully',
  });
});

/**
 * Handle ZainCash payment callback
 */
const handlePaymentCallback = catchAsync(async (req, res) => {
  const { token } = req.query;

  if (!token) {
    logger.error('ZainCash callback received without token');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token is required');
  }

  logger.info(`ZainCash payment callback received with token`);

  try {
    const result = await zaincashService.verifyPayment(token);

    logger.info(`ZainCash payment verified and processed - walletId: ${result.walletId}, amount: ${result.amount}`);

    // Redirect to the frontend with success status
    //use deeplink here
    res.redirect(`${config.clientUrl}wallet/payment/success?amount=${result.amount}&walletId=${result.walletId}`);
  } catch (error) {
    logger.error(`ZainCash payment verification failed: ${error.message}`);

    // Redirect to the frontend with error status
    res.redirect(`${config.clientUrl}wallet/payment/error?message=${encodeURIComponent(error.message)}`);
  }
});

module.exports = {
  initializePayment,
  handlePaymentCallback,
};
