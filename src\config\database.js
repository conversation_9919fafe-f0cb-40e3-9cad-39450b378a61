// const {Sequelize} = require('sequelize');
// const config = require('./config');

// //  if (config.env === 'development') {
//   const sequelize = new Sequelize({
//     host: config.mysql.host,
//     username: config.mysql.username,
//     password: config.mysql.password,
//     database: config.mysql.database,
//     dialect: 'postgres',
//     port: '5432',
//   });
// // } else if (config.env === 'staging') {
// //   sequelize = new Sequelize(
// //     config.mysql.staging.database,
// //     config.mysql.staging.username,
// //     config.mysql.staging.password,
// //     {
// //       host: config.mysql.staging.host,
// //       dialect: 'mysql',
// //       logging: true, // Set to true if you want to log SQL queries
// //     }
// //   );
//   sequelize
//   .authenticate()
//   .then(() => {
//     console.log('Database connection has been established successfully.');
//   })
//   .catch(err => {
//     console.error('Unable to connect to the database:', err);
//   });
// // } else {
// //   throw new Error('Invalid value for DB_ENV');
// // }

// module.exports = {sequelize};

const { Sequelize } = require('sequelize');
const config = require('./config');

const sequelize = new Sequelize({
  host: config.mysql.host,
  username: config.mysql.username,
  password: config.mysql.password,
  database: config.mysql.database,
  dialect: 'postgres',
  port: 5432, // ✅ Ensure port is a number, not a string
  logging: false, // Set to true to log queries (useful for debugging)
  pool: { // ✅ Recommended for production
    max: 10, // Maximum number of connections
    min: 1,
    acquire: 30000, // Wait 30 seconds before throwing an error
    idle: 10000, // Close idle connections after 10 seconds
  },
});

sequelize
  .authenticate()
  .then(() => {
    console.log('Database connection has been established successfully.');
  })
  .catch(err => {
    console.error('Unable to connect to the database:', err);
  });

module.exports = { sequelize };
