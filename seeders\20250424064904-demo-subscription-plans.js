'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */

    // Inserting sample subscription plans into the SubscriptionPlan table
    await queryInterface.bulkInsert(
      'subscription_plans',
      [
        {
          name: 'Basic',
          description: 'Basic subscription plan with limited features.',
          durationValue: 1, // 1 month
          durationType: 'month',
          price: 10.00,
          currency: 'USD',
          benefits: JSON.stringify([
            { name: 'Enhanced Visibility', isIncluded: false },
            { name: 'Advanced Analytics', isIncluded: false },
            { name: 'Priority Support', isIncluded: false },
          ]),
          isActive: true,
          displayOrder: 1,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Pro',
          description: 'Pro plan with enhanced features and priority support.',
          durationValue: 3, // 3 months (Quarterly)
          durationType: 'month',
          price: 25.00,
          currency: 'USD',
          benefits: JSON.stringify([
            { name: 'Enhanced Visibility', isIncluded: true },
            { name: 'Advanced Analytics', isIncluded: true },
            { name: 'Priority Support', isIncluded: false },
          ]),
          isActive: true,
          displayOrder: 2,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Premium',
          description: 'Premium plan with all available features.',
          durationValue: 1, // 1 year
          durationType: 'year',
          price: 90.00,
          currency: 'USD',
          benefits: JSON.stringify([
            { name: 'Enhanced Visibility', isIncluded: true },
            { name: 'Advanced Analytics', isIncluded: true },
            { name: 'Priority Support', isIncluded: true },
          ]),
          isActive: true,
          displayOrder: 3,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ],
      {}
    );
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
    await queryInterface.bulkDelete('subscription_plans', null, {});
  },
};