const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const vendorSubscriptionValidation = require('../../validations/vendorSubscription.validation');
const vendorSubscriptionController = require('../../controllers/vendorSubscription.controller');

// Initialize subscription payment
router.post(
  '/payment',
  firebaseAuth,
  validate(vendorSubscriptionValidation.initializeSubscriptionPayment),
  vendorSubscriptionController.initializeSubscriptionPayment
);

// Get subscription by ID
router.get(
  '/:subscriptionId',
  firebaseAuth,
  validate(vendorSubscriptionValidation.getSubscriptionById),
  vendorSubscriptionController.getSubscriptionById
);

// Get active subscription for a vendor
router.get(
  '/vendor/:vendorId/active',
  firebaseAuth,
  validate(vendorSubscriptionValidation.getActiveSubscription),
  vendorSubscriptionController.getActiveSubscription
);

// // Cancel a subscription
// router.post(
//   '/:subscriptionId/cancel',
//   firebaseAuth,
//   validate(vendorSubscriptionValidation.cancelSubscription),
//   vendorSubscriptionController.cancelSubscription
// );

// Provider-specific callback routes (no auth required as these are called by payment providers)
router.get(
  '/zaincash/callback',
  validate(vendorSubscriptionValidation.handleZaincashCallback),
  vendorSubscriptionController.handleZaincashCallback
);

// FIB callback route (POST request from FIB server)
router.post(
  '/fib/callback',
  validate(vendorSubscriptionValidation.handleFibCallback),
  vendorSubscriptionController.handleFibCallback
);

// FIB redirect route (GET request for user redirection)
router.get(
  '/fib/redirect',
  validate(vendorSubscriptionValidation.handleFibRedirect),
  vendorSubscriptionController.handleFibRedirect
);

module.exports = router;
