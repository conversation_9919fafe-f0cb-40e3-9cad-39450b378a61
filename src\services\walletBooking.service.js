const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Booking, Wallet, WalletTransaction, Station } = require('../models');
const { sequelize } = require('../config/database');
const { WALLET_TRANSACTION_TYPE, WALLET_TRANSACTION_STATUS, PAYMENT_STATUS, BOOKING_STATUS } = require('../constants');

/**
 * Process booking payment using wallet
 * @param {number} userId - User ID
 * @param {number} bookingId - Booking ID
 * @returns {Promise<Object>}
 */
const processBookingPayment = async (userId, bookingId) => {
  const transaction = await sequelize.transaction();

  try {
    // Get booking with lock
    const booking = await Booking.findByPk(bookingId, {
      transaction,
      lock: true,
      include: [
        {
          model: Station,
          include: [{ model: Vendor }],
        },
      ],
    });

    if (!booking) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');
    }

    // Verify booking belongs to user
    if (booking.userId !== userId) {
      await transaction.rollback();
      throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to pay for this booking');
    }

    // Check if booking is already paid
    if (booking.paymentStatus === PAYMENT_STATUS.PAID) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Booking is already paid');
    }

    // Get user wallet
    const userWallet = await Wallet.findOne({
      where: {
        ownerId: userId,
        ownerType: 'user'
      },
      transaction,
      lock: true,
    });

    if (!userWallet) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'User wallet not found. Please create a wallet first.');
    }

    // Check if wallet has sufficient balance
    if (userWallet.balance < booking.totalCost) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Insufficient wallet balance. Please top up your wallet.');
    }

    // Get vendor wallet
    const vendorUserId = booking.Station.Vendor.userId; // Get the vendor's user ID
    let vendorWallet = await Wallet.findOne({
      where: {
        ownerId: vendorUserId,
        ownerType: 'vendor'
      },
      transaction,
      lock: true,
    });

    // // Create vendor wallet if it doesn't exist
    // if (!vendorWallet) {
    //   vendorWallet = await Wallet.create(
    //     {
    //       ownerId: vendorUserId,
    //       ownerType: 'vendor',
    //       balance: 0,
    //       currency: userWallet.currency,
    //       isActive: true,
    //     },
    //     { transaction }
    //   );
    // }

    // Debit from user wallet
    await userWallet.updateBalanceWithLock(-booking.totalCost, transaction);

    // Create user transaction record
    const userTransaction = await WalletTransaction.create(
      {
        walletId: userWallet.id,
        amount: -booking.totalCost,
        balanceAfter: userWallet.balance,
        type: WALLET_TRANSACTION_TYPE.BOOKING_PAYMENT,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description: `Payment for booking #${booking.id}`,
        metadata: {
          bookingId: booking.id,
          stationId: booking.stationId,
          chargerId: booking.chargerId,
        },
        processedAt: new Date(),
      },
      { transaction }
    );

    // Credit to vendor wallet
    await vendorWallet.updateBalanceWithLock(booking.totalCost, transaction);

    // Create vendor transaction record
    const vendorTransaction = await WalletTransaction.create(
      {
        walletId: vendorWallet.id,
        amount: booking.totalCost,
        balanceAfter: vendorWallet.balance,
        type: WALLET_TRANSACTION_TYPE.BOOKING_PAYMENT,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description: `Payment received for booking #${booking.id}`,
        metadata: {
          bookingId: booking.id,
          stationId: booking.stationId,
          chargerId: booking.chargerId,
          userId: booking.userId,
        },
        relatedTransactionId: userTransaction.id,
        processedAt: new Date(),
      },
      { transaction }
    );

    // Update user transaction with related transaction ID
    await userTransaction.update(
      { relatedTransactionId: vendorTransaction.id },
      { transaction }
    );

    // Update booking status
    await booking.update(
      {
        paymentStatus: PAYMENT_STATUS.PAID,
        status: BOOKING_STATUS.CONFIRMED,
      },
      { transaction }
    );

    await transaction.commit();

    return {
      booking,
      userTransaction,
      vendorTransaction,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Process booking refund to wallet
 * @param {number} bookingId - Booking ID
 * @returns {Promise<Object>}
 */
const processBookingRefund = async (bookingId) => {
  const transaction = await sequelize.transaction();

  try {
    // Get booking with lock
    const booking = await Booking.findByPk(bookingId, {
      transaction,
      lock: true,
      include: [
        {
          model: Station,
          include: [{ model: Vendor }],
        },
      ],
    });

    if (!booking) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');
    }

    // Check if booking is paid and can be refunded
    if (booking.paymentStatus !== PAYMENT_STATUS.PAID) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Booking is not paid and cannot be refunded');
    }

    if (booking.paymentStatus === PAYMENT_STATUS.REFUNDED) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Booking is already refunded');
    }

    // Get user wallet
    const userWallet = await Wallet.findOne({
      where: {
        ownerId: booking.userId,
        ownerType: 'user'
      },
      transaction,
      lock: true,
    });

    if (!userWallet) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'User wallet not found');
    }

    // Get vendor wallet
    const vendorUserId = booking.Station.Vendor.userId;
    const vendorWallet = await Wallet.findOne({
      where: {
        ownerId: vendorUserId,
        ownerType: 'vendor'
      },
      transaction,
      lock: true,
    });

    if (!vendorWallet) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'Vendor wallet not found');
    }

    // Check if vendor has sufficient balance
    if (vendorWallet.balance < booking.totalCost) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Insufficient vendor wallet balance for refund');
    }

    // Debit from vendor wallet
    await vendorWallet.updateBalanceWithLock(-booking.totalCost, transaction);

    // Create vendor transaction record
    const vendorTransaction = await WalletTransaction.create(
      {
        walletId: vendorWallet.id,
        amount: -booking.totalCost,
        balanceAfter: vendorWallet.balance,
        type: WALLET_TRANSACTION_TYPE.BOOKING_REFUND,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description: `Refund issued for booking #${booking.id}`,
        metadata: {
          bookingId: booking.id,
          stationId: booking.stationId,
          chargerId: booking.chargerId,
          userId: booking.userId,
        },
        processedAt: new Date(),
      },
      { transaction }
    );

    // Credit to user wallet
    await userWallet.updateBalanceWithLock(booking.totalCost, transaction);

    // Create user transaction record
    const userTransaction = await WalletTransaction.create(
      {
        walletId: userWallet.id,
        amount: booking.totalCost,
        balanceAfter: userWallet.balance,
        type: WALLET_TRANSACTION_TYPE.BOOKING_REFUND,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description: `Refund received for booking #${booking.id}`,
        metadata: {
          bookingId: booking.id,
          stationId: booking.stationId,
          chargerId: booking.chargerId,
        },
        relatedTransactionId: vendorTransaction.id,
        processedAt: new Date(),
      },
      { transaction }
    );

    // Update vendor transaction with related transaction ID
    await vendorTransaction.update(
      { relatedTransactionId: userTransaction.id },
      { transaction }
    );

    // Update booking status
    await booking.update(
      {
        paymentStatus: PAYMENT_STATUS.REFUNDED,
        refundAmount: booking.totalCost,
      },
      { transaction }
    );

    await transaction.commit();

    return {
      booking,
      userTransaction,
      vendorTransaction,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

module.exports = {
  processBookingPayment,
  processBookingRefund,
};
