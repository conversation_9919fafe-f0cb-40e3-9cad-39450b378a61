 New connection established.
 ChargePoint ID: tesidaBFB97932
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 32 38 22 2c 22 42 6f 6f 74 4e 6f 74 69 66 69 63 61 74 69 6f 6e 22 2c 7b 22 63 68 61 72 67 65 50 6f 69 6e 74 4d 6f 64 65 ... 124 more bytes>
 Parsed message: [
   2,
   '00201228',
   'BootNotification',
   {
     chargePointModel: 'tsdogjlztw_l',
     chargePointVendor: 'tesida',
     firmwareVersion: 'V4.5',
     chargeBoxSerialNumber: 'tesidaBFB97932',
     imsi: ''
   }
 ]
 meterValues: undefined
 Handling action: BootNotification
 tesidaBFB97932
 Updated Charger tesidaBFB97932 status to Booted
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 32 39 22 2c 22 48 65 61 72 74 62 65 61 74 22 2c 7b 7d 5d>
 Parsed message: [ 2, '00201229', 'Heartbeat', {} ]
 meterValues: undefined
 Handling action: Heartbeat
 Received Heartbeat
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 30 22 2c 22 4d 65 74 65 72 56 61 6c 75 65 73 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a 31 2c 22 6d 65 74 65 ... 824 more bytes>
 Parsed message: [
   2,
   '00201230',
   'MeterValues',
   { connectorId: 1, meterValue: [ [Object] ] }
 ]
 meterValues: [
   {
     timestamp: '2025-05-08T07:21:32Z',
     sampledValue: [
       [Object], [Object],
       [Object], [Object],
       [Object], [Object],
       [Object], [Object]
     ]
   }
 ]
 sampleValue: [
   {
     value: '0',
     location: 'Cable',
     unit: 'Wh',
     measurand: 'Energy.Active.Import.Register',
     context: 'Other'
   },
   {
     value: '25',
     location: 'Body',
     unit: 'Celsius',
     measurand: 'Temperature',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L1',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L2',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L3',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L1-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L2-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L3-N',
     context: 'Other'
   }
 ]
 Handling action: MeterValues
 Meter Reading [2025-05-08T07:21:32Z]: Energy.Active.Import.Register = 0 Wh
 Meter Reading [2025-05-08T07:21:32Z]: Temperature = 25 Celsius
 Meter Reading [2025-05-08T07:21:32Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:32Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:32Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:32Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:21:32Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:21:32Z]: Voltage = 226 V
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 31 22 2c 22 4d 65 74 65 72 56 61 6c 75 65 73 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a 32 2c 22 6d 65 74 65 ... 824 more bytes>
 Parsed message: [
   2,
   '00201231',
   'MeterValues',
   { connectorId: 2, meterValue: [ [Object] ] }
 ]
 meterValues: [
   {
     timestamp: '2025-05-08T07:21:34Z',
     sampledValue: [
       [Object], [Object],
       [Object], [Object],
       [Object], [Object],
       [Object], [Object]
     ]
   }
 ]
 sampleValue: [
   {
     value: '0',
     location: 'Cable',
     unit: 'Wh',
     measurand: 'Energy.Active.Import.Register',
     context: 'Other'
   },
   {
     value: '25',
     location: 'Body',
     unit: 'Celsius',
     measurand: 'Temperature',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L1',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L2',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L3',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L1-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L2-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L3-N',
     context: 'Other'
   }
 ]
 Handling action: MeterValues
 Meter Reading [2025-05-08T07:21:34Z]: Energy.Active.Import.Register = 0 Wh
 Meter Reading [2025-05-08T07:21:34Z]: Temperature = 25 Celsius
 Meter Reading [2025-05-08T07:21:34Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:34Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:34Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:21:34Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:21:34Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:21:34Z]: Voltage = 226 V
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 32 22 2c 22 53 74 61 74 75 73 4e 6f 74 69 66 69 63 61 74 69 6f 6e 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a ... 81 more bytes>
 Parsed message: [
   2,
   '00201232',
   'StatusNotification',
   {
     connectorId: 2,
     errorCode: 'NoError',
     status: 'Available',
     timestamp: '2025-05-08T07:21:36Z'
   }
 ]
 meterValues: undefined
 Handling action: StatusNotification
 Updated Charger undefined status to Available




New connection established.
 ChargePoint ID: tesidaBFB97932
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 33 22 2c 22 42 6f 6f 74 4e 6f 74 69 66 69 63 61 74 69 6f 6e 22 2c 7b 22 63 68 61 72 67 65 50 6f 69 6e 74 4d 6f 64 65 ... 124 more bytes>
 Parsed message: [
   2,
   '00201233',
   'BootNotification',
   {
     chargePointModel: 'tsdogjlztw_l',
     chargePointVendor: 'tesida',
     firmwareVersion: 'V4.5',
     chargeBoxSerialNumber: 'tesidaBFB97932',
     imsi: ''
   }
 ]
 meterValues: undefined
 Handling action: BootNotification
 tesidaBFB97932
 Updated Charger tesidaBFB97932 status to Booted
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 34 22 2c 22 48 65 61 72 74 62 65 61 74 22 2c 7b 7d 5d>
 Parsed message: [ 2, '00201234', 'Heartbeat', {} ]
 meterValues: undefined
 Handling action: Heartbeat
 Received Heartbeat
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 35 22 2c 22 4d 65 74 65 72 56 61 6c 75 65 73 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a 31 2c 22 6d 65 74 65 ... 824 more bytes>
 Parsed message: [
   2,
   '00201235',
   'MeterValues',
   { connectorId: 1, meterValue: [ [Object] ] }
 ]
 meterValues: [
   {
     timestamp: '2025-05-08T07:23:03Z',
     sampledValue: [
       [Object], [Object],
       [Object], [Object],
       [Object], [Object],
       [Object], [Object]
     ]
   }
 ]
 sampleValue: [
   {
     value: '0',
     location: 'Cable',
     unit: 'Wh',
     measurand: 'Energy.Active.Import.Register',
     context: 'Other'
   },
   {
     value: '25',
     location: 'Body',
     unit: 'Celsius',
     measurand: 'Temperature',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L1',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L2',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L3',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L1-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L2-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L3-N',
     context: 'Other'
   }
 ]
 Handling action: MeterValues
 Meter Reading [2025-05-08T07:23:03Z]: Energy.Active.Import.Register = 0 Wh
 Meter Reading [2025-05-08T07:23:03Z]: Temperature = 25 Celsius
 Meter Reading [2025-05-08T07:23:03Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:03Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:03Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:03Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:23:03Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:23:03Z]: Voltage = 226 V
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 36 22 2c 22 4d 65 74 65 72 56 61 6c 75 65 73 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a 32 2c 22 6d 65 74 65 ... 824 more bytes>
 Parsed message: [
   2,
   '00201236',
   'MeterValues',
   { connectorId: 2, meterValue: [ [Object] ] }
 ]
 meterValues: [
   {
     timestamp: '2025-05-08T07:23:05Z',
     sampledValue: [
       [Object], [Object],
       [Object], [Object],
       [Object], [Object],
       [Object], [Object]
     ]
   }
 ]
 sampleValue: [
   {
     value: '0',
     location: 'Cable',
     unit: 'Wh',
     measurand: 'Energy.Active.Import.Register',
     context: 'Other'
   },
   {
     value: '25',
     location: 'Body',
     unit: 'Celsius',
     measurand: 'Temperature',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L1',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L2',
     context: 'Other'
   },
   {
     value: '0',
     location: 'Cable',
     unit: 'A',
     measurand: 'Current.Import',
     phase: 'L3',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L1-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L2-N',
     context: 'Other'
   },
   {
     value: '226',
     unit: 'V',
     measurand: 'Voltage',
     phase: 'L3-N',
     context: 'Other'
   }
 ]
 Handling action: MeterValues
 Meter Reading [2025-05-08T07:23:05Z]: Energy.Active.Import.Register = 0 Wh
 Meter Reading [2025-05-08T07:23:05Z]: Temperature = 25 Celsius
 Meter Reading [2025-05-08T07:23:05Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:05Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:05Z]: Current.Import = 0 A
 Meter Reading [2025-05-08T07:23:05Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:23:05Z]: Voltage = 226 V
 Meter Reading [2025-05-08T07:23:05Z]: Voltage = 226 V
 Received: <Buffer 5b 32 2c 22 30 30 32 30 31 32 33 37 22 2c 22 53 74 61 74 75 73 4e 6f 74 69 66 69 63 61 74 69 6f 6e 22 2c 7b 22 63 6f 6e 6e 65 63 74 6f 72 49 64 22 3a ... 81 more bytes>
 Parsed message: [
   2,
   '00201237',
   'StatusNotification',
   {
     connectorId: 2,
     errorCode: 'NoError',
     status: 'Available',
     timestamp: '2025-05-08T07:23:08Z'
   }
 ]
 meterValues: undefined
 Handling action: StatusNotification
