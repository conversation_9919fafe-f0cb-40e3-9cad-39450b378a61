const getPaginateConfig = (queryParams) => {
  const sortOrder = queryParams.order || -1;
  const sort = queryParams.sortBy || "createdAt";
  const sortBy = buildSort(sort, sortOrder);
  const page = parseInt(queryParams.page, 10) || 1;
  const limit = parseInt(queryParams.limit, 10) || 5;

  return {
    sort: sortBy,
    lean: true,
    page,
    limit,
  };
};

const buildSort = (sort, order) => ({ [sort]: order });

// const getItems(req, model, query) { method
//   const options = await listInitOptions(req);
//   return new Promise((resolve, reject) => {
//     model.paginate(query, options, (err, items) => {
//       if (err) {
//         reject(buildErrObject(422, err.message));
//       }
//       resolve(cleanPaginationID(items));
//     });
//   });
// }

const cleanPaginationID = (result) => {
  result.docs.map((element) => delete element.id);
  return result;
};

module.exports = {
  getPaginateConfig,
  cleanPaginationID
};
