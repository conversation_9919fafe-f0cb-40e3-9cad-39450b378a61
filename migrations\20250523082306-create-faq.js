'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('faqs', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      question: {
        type: Sequelize.STRING(255),
        allowNull: false,
        comment: 'The question text',
      },
      answer: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'The answer text',
      },
      type: {
        type: Sequelize.ENUM('Account', 'Service', 'Booking', 'Payment', 'Technical', 'Other'),
        allowNull: false,
        comment: 'Category of the FAQ',
      },
      status: {
        type: Sequelize.ENUM('live', 'draft', 'paused'),
        allowNull: false,
        defaultValue: 'draft',
        comment: 'Publication status of the FAQ',
      },
      displayOrder: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Order in which FAQs are displayed (lower numbers first)',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('faqs');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_faqs_type";');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_faqs_status";');
  }
};
