const express = require('express');
const router = express.Router();
const { firebaseAuth } = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const { paymentController } = require('../../controllers');
const paymentValidation = require('../../validations/payment.validation');

// Initialize wallet top-up payment (generic endpoint for all payment providers)
router.post(
  '/wallet/topup',
  firebaseAuth,
  validate(paymentValidation.initializePayment),
  paymentController.initializePayment
);

// Provider-specific callback routes (no auth required as these are called by payment providers)
router.get(
  '/wallet/zaincash/callback',
  validate(paymentValidation.handleZaincashCallback),
  paymentController.handleZaincashCallback
);

// FIB callback route (POST request from FIB server)
router.post(
  '/wallet/fib/callback',
  validate(paymentValidation.handleFibCallback),
  paymentController.handleFibCallback
);

// FIB redirect route (GET request when user is redirected from FIB app)
// router.get(
//   '/wallet/fib/redirect',
//   validate(paymentValidation.handleFibRedirect),
//   paymentController.handleFibRedirect
// );

// Add more provider-specific callback routes for wallet top-ups as needed
// router.post('/wallet/stripe/webhook', validate(paymentValidation.handleStripeWebhook), paymentController.handleStripeCallback);
// router.get('/wallet/paypal/callback', validate(paymentValidation.handlePaypalCallback), paymentController.handlePaypalCallback);

// In the future, you can add subscription payment routes here
// router.post('/subscription/create', firebaseAuth, validate(paymentValidation.createSubscription), paymentController.createSubscription);
// router.post('/subscription/cancel', firebaseAuth, validate(paymentValidation.cancelSubscription), paymentController.cancelSubscription);

module.exports = router;
