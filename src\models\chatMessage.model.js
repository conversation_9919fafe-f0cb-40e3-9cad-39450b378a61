const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const ChatMessage = sequelize.define(
  'ChatMessage',
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
    },
    chatId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'chats',
        key: 'id',
      },
    },
    senderId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      comment: 'ID of sender (user or admin based on isAdmin flag)',
    },
    message: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    isAdmin: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    isSystem: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    firebaseMessageId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  },
  {
    tableName: 'chat_messages',
    timestamps: true,
  }
);

module.exports = ChatMessage;