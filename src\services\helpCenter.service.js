const { HelpCenterTicket,FAQ } = require('../models');
const { TICKET_STATUS } = require('../constants');
const { generateTicketNumber } = require('../utils/ticketUtils');
const { Op } = require('sequelize');
const logger = require('../config/logger');

/**
 * Create a new help center ticket
 * @param {number} userId - User ID
 * @param {string} email - User email
 * @param {string} query - User query/issue description
 * @returns {Promise<Object>} Created ticket
 */
const createTicket = async (userId, email, query) => {
    // Generate a unique ticket number
    const ticketNo = await generateTicketNumber();
    
    // Create the ticket
    const ticket = await HelpCenterTicket.create({
      userId,
      email,
      query,
      ticketNo,
      status: TICKET_STATUS.PENDING
    });
    
    return ticket;
};

/**
 * Get all live FAQs for mobile users with optional filtering
 * @param {Object} options - Filter options
 * @param {string} [options.type] - Filter by FAQ type
 * @param {string} [options.search] - Search in question and answer
 * @returns {Promise<Array>} - List of filtered live FAQs
 */
const getLiveFAQs = async (options = {}) => {
  const { type, search, page, limit } = options;
  
  logger.info('Fetching live FAQs with filters', { type, search });
  
  // Build where clause for filtering
  const where = { 
    status: 'live' // Always filter by live status
  };
  
  // Add type filter if provided
  if (type) {
    where.type = type;
  }
  
  // Add search filter if provided
  if (search) {
    where[Op.or] = [
      { question: { [Op.iLike]: `%${search}%` } },
      { answer: { [Op.iLike]: `%${search}%` } }
    ];
  }
  
  // Find FAQs with the built where clause
  const faqs = await FAQ.findAll({
    where,
    limit,
    offset: (page - 1) * limit,
    order: [['displayOrder', 'ASC']],
    attributes: ['id', 'question', 'answer', 'type', 'displayOrder']
  });
  
  logger.info(`Found ${faqs.length} live FAQs matching filters`);
  return faqs;
};

module.exports = {
  createTicket,
  getLiveFAQs
};
