const ApiError = require('../utils/ApiError');
const eventEmitter = require('../utils/eventEmitter');
const { BOOKING_EVENTS } = require('../constants/bookingEvents');
const httpStatus = require('http-status');
const moment = require('moment-timezone');
const logger = require('../config/logger');
const { Op } = require('sequelize');
const { Booking, Station, Charger, Vendor, UserVehicle, Connector, Power, VehicleBrand, VehicleModel, BatteryCapacity,ManufacturingUnit } = require('../models');
const { sequelize } = require('../config/database');
const { BOOKING_STATUS, PAYMENT_STATUS, CANCELLATION_BY,RECIPIENT_TYPE,REFERENCE_TYPE } = require('../constants');
const { isSlotAvailable, isWithinOperationalHours, calculateTotalCost } = require('../utils/bookingUtils');
const { sendToTopic } = require('../microservices/notification.service');



/**
 * Books a charging slot if available and within operational hours.
 */
const bookChargingSlot = async (userId, stationId, chargerId, vehicleId, arrivalTime, duration) => {
    // Convert arrivalTime to UTC Date object (assuming frontend sends in UTC)
    const startTime = moment.utc(arrivalTime).toDate();
    const endTime = moment.utc(startTime).add(duration, 'minutes').toDate();

    // Fetch station and charger in a single query
    const station = await Station.findOne({
        where: { id: stationId },
        include: [{ model: Charger, where: { id: chargerId } }],
    });

    if (!station) throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    if (!station.chargers?.length) throw new ApiError(httpStatus.NOT_FOUND, 'Charger not found at this station');

    const charger = station.chargers[0];

    // Validate vehicle ownership in a single check
    const userVehicle = await UserVehicle.findOne({ where: { id: vehicleId, userId } });
    if (!userVehicle) throw new ApiError(httpStatus.FORBIDDEN, 'Vehicle not found or does not belong to the user');

    // Check slot availability and operational hours in parallel
    const [slotAvailable, withinOperationalHours] = await Promise.all([
        isSlotAvailable(chargerId, startTime, endTime),
        isWithinOperationalHours(station, startTime, endTime),
    ]);

    if (!slotAvailable) throw new ApiError(httpStatus.CONFLICT, 'Selected slot is already booked');
    if (!withinOperationalHours) throw new ApiError(httpStatus.BAD_REQUEST, 'Selected time is outside station operational hours');

    // Calculate total cost
    const totalCost = calculateTotalCost(charger.pricePerHour, duration);

    // Handle concurrency using transactions
    const transaction = await sequelize.transaction();
    try {
        const newBooking = await Booking.create(
            { userId, stationId, chargerId, vehicleId, startTime, endTime, totalCost, status: BOOKING_STATUS.PENDING },
            { transaction }
        );

        // Get station owner's userId
        const stationOwner = await Vendor.findOne({
            where: { id: station.vendorId },
            attributes: ['userId'],
            transaction
        });

        // Format the time for notifications
        const formattedStartTime = moment(startTime).format('MMMM Do YYYY, h:mm a');

        // Get vehicle details
        const vehicleDetails = await UserVehicle.findOne({
            where: { id: vehicleId },
            include: [
                { model: VehicleBrand, attributes: ['name'] },
                { model: VehicleModel, attributes: ['name'] }
            ],
            transaction
        });

        const vehicleInfo = `${vehicleDetails.VehicleBrand.name} ${vehicleDetails.VehicleModel.name}`;

        // Send notification to user
        await sendToTopic(
            `user_${userId}`,
            {
                title: 'Booking Confirmation',
                body: `Your booking at ${station.stationName} has been created for ${formattedStartTime}`
            },
            {
                type: 'booking',
                bookingId: newBooking.id,
                stationName: station.stationName,
                startTime: formattedStartTime,
                duration: duration,
                totalCost: totalCost,
                vehicleInfo: vehicleInfo,
                userId: userId,
                referenceId: newBooking.id,
                referenceType: REFERENCE_TYPE.BOOKING
            },
            RECIPIENT_TYPE.USER
        );

        // Send notification to station owner
        await sendToTopic(
            `user_${stationOwner.userId}`,
            {
                title: 'New Booking Request',
                body: `New booking request received for ${station.stationName}`
            },
            {
                type: 'booking',
                bookingId: newBooking.id,
                stationName: station.stationName,
                startTime: formattedStartTime,
                duration: duration,
                totalCost: totalCost,
                vehicleInfo: vehicleInfo,
                userId: stationOwner.userId,
                referenceId: newBooking.id,
                referenceType: REFERENCE_TYPE.BOOKING
            },
            RECIPIENT_TYPE.USER
        );

        await transaction.commit();
        return newBooking;
    } catch (error) {
        await transaction.rollback();
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error processing booking.');
    }
};


/**
 * Fetches booking details for a user or vendor with pagination.
 * @param {string} userId - User ID
 * @param {string} status - Booking status
 * @param {number} page - Page number (default: 1)
 * @param {number} limit - Number of items per page (default: 10)
 * @returns {Object} - Paginated booking details
 **/

const getBookingDetails = async function (userId, status, page, limit, isVendor = false) {

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    let vendorId;
    if (isVendor) {
        const vendor = await Vendor.findOne({ where: { userId } });
        if (!vendor) throw new Error('Vendor not found');
        vendorId = vendor.id;
    }

    const where = isVendor
        ? { status, '$Station.vendorId$': vendorId }
        : { userId, status };

    // Get total count for pagination
    const totalCount = await Booking.count({
        where,
        include: isVendor ? [{ model: Station }] : [] // Include Station model if we're filtering by vendor
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limit);

    // Get bookings with pagination
    const bookings = await Booking.findAll({
        where,
        include: [
            {
                model: Station,
                attributes: [
                    'stationName',
                    'address',
                    'latitude',
                    'longitude',
                    [sequelize.literal(`"Station"."images"->0->>'url'`), 'stationImage']
                ]
            },
            {
                model: Charger,
                include: [
                    { model: Connector, attributes: ['name'] },
                    { model: Power, attributes: ['value', 'unit'] }
                ]
            }
        ],
        attributes: {
            include: [
                [
                    sequelize.literal('ROUND(EXTRACT(EPOCH FROM ("endTime" - "startTime")) / 3600, 2)'),
                    'duration'
                ]
            ]
        },
        limit,
        offset,
        order: [['startTime', 'DESC']] // Order by start time, most recent first
    });

    const formattedBookings = bookings.map(booking => {
        const bookingData = booking.toJSON();
        return {
            ...bookingData,
            duration: Math.round(booking.getDataValue('duration')),
            Station: {
                ...bookingData.Station,
            }
        };
    });

    // Return paginated result
    return {
        bookings: formattedBookings,
        pagination: {
            totalItems: totalCount,
            totalPages,
            currentPage: page,
            itemsPerPage: limit,
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1
        }
    };
};


/**
 * Fetches booking details by ID for a user or vendor.
**/

const getBookingDetailsById = async function (userId, bookingId) {

    // Check if the user is a vendor
    const vendor = await Vendor.findOne({ where: { userId } });
    const isVendor = !!vendor;

    // Construct the where clause based on user type
    const where = isVendor
        ? { id: bookingId, '$Station.vendorId$': vendor.id } // Vendor can only see bookings for their stations
        : { id: bookingId, userId }; // Regular user can only see their own bookings

    return await Booking.findOne({
        where,
        include: [
            {
                model: Station,
                attributes: ['stationName', 'address', 'latitude', 'longitude'],
            },
            {
                model: Charger,
                include: [
                    { model: Connector, attributes: ['name'] },
                    { model: Power, attributes: ['value', 'unit'] },
                ],
            },
            {
                model: UserVehicle,
                attributes: ['brandName','modelName'],
                include: [
                    { model: VehicleBrand, attributes: ['name'] },
                    { model: VehicleModel, attributes: ['name'] },
                    { model: BatteryCapacity, attributes: ['capacity', 'unit'] },
                    { model: ManufacturingUnit, attributes: ['name', 'location'] },
                ],
            },
        ],
    });
}

/**
 * Cancels a booking and processes a refund if applicable.
**/

const cancelBooking = async function (userId, bookingId, cancellationReason) {

    // Find the booking along with its station and vendor details
    const booking = await Booking.findByPk(bookingId, {
        include: {
            model: Station,
            attributes: ['vendorId'],
            include: {
                model: Vendor,
                attributes: ['userId'], // Get the userId of the vendor
            },
        },
    });

    if (!booking) throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');

    const isBookingOwner = booking.userId === userId;
    const isStationOwner = booking.Station?.Vendor?.userId === userId;

    // Ensure that either the user or the station owner can cancel the booking
    if (!isBookingOwner && !isStationOwner) {
        throw new ApiError(httpStatus.UNAUTHORIZED, 'Only the booking owner or station owner can cancel the booking');
    }

    // Check if the booking is cancellable
    if (![BOOKING_STATUS.PENDING, BOOKING_STATUS.UPCOMING].includes(booking.status)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Booking is not cancellable');
    }
    // Update the booking status and cancellation details
    await booking.update({
        status: BOOKING_STATUS.CANCELED,
        cancellationReason,
        cancelledBy: isBookingOwner ? CANCELLATION_BY.USER : CANCELLATION_BY.VENDOR,
        cancellationTime: new Date(),
    });

    // Handle refund if payment was made
    // if (booking.paymentStatus === PAYMENT_STATUS.PAID) {
    //     await booking.update({
    //         paymentStatus: PAYMENT_STATUS.REFUNDED,
    //         refundAmount: booking.totalCost,
    //     });
    // }

    // Notify the user (e.g., via email or SMS)
}

const changeTimeSlot = async function (userId, bookingId, arrivalTime, duration, timeSlotChangeReason) {


    const startTime = moment.utc(arrivalTime).toDate();
    const endTime = moment.utc(startTime).add(duration, 'minutes').toDate();

    // Find the vendor associated with the user
    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');



    const booking = await Booking.findByPk(bookingId, {
        include: [
            {
                model: Station,
                attributes: ['stationName'],
            },
        ],
    });

    if (!booking) throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');

    // Check if the user making the change is the station owner
    if (vendor.userId !== userId) {
        throw new ApiError(httpStatus.UNAUTHORIZED, 'Only the station owner can change the booking time');
    }

    // Validate booking status
    if (![BOOKING_STATUS.PENDING, BOOKING_STATUS.UPCOMING].includes(booking.status)) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Only pending or upcoming bookings can be rescheduled');
    }
    // Check slot availability
    const slotAvailable = await isSlotAvailable(booking.chargerId, startTime, endTime, bookingId);
    if (!slotAvailable) {
        throw new ApiError(httpStatus.CONFLICT, 'Selected slot is already booked');
    }


    const transaction = await sequelize.transaction();
    try {
        await booking.update(
            { startTime, endTime, timeSlotChangeReason,status:BOOKING_STATUS.UPCOMING },
            { transaction }
        );

        await transaction.commit();
        

        // Emit booking rescheduled event
        eventEmitter.emit(BOOKING_EVENTS.RESCHEDULED, {
            bookingId: booking.id,
            userId: booking.userId,
            stationName: booking.Station.stationName,
            startTime,
            endTime,
            reason: timeSlotChangeReason,
            performedBy: userId
        });

        logger.info(`Booking time changed successfully - bookingId: ${booking.id}`);
        return booking;
    } catch (error) {
        await transaction.rollback();
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error processing booking.');
    }
};


/**
 * Get available time slots for a station or specific charger on a specific date
 * @param {number} stationId - Station ID
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {number} chargerId - Optional charger ID to filter slots
 * @returns {Promise<Array>} - Array of available time slots
 */

const getAvailableTimeSlots = async (stationId, date, chargerId = null) => {
    logger.info(`Starting getAvailableTimeSlots - stationId: ${stationId}, date: ${date}, chargerId: ${chargerId}`);

    // Parse date in UTC to ensure consistent timezone handling
    const selectedDate = moment.utc(date, 'YYYY-MM-DD');
    if (!selectedDate.isValid()) {
        logger.error(`Invalid date format received: ${date}`);
        throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid date format. Use YYYY-MM-DD');
    }

    const dayOfWeek = selectedDate.format('dddd');

    logger.info(`Fetching station data for stationId: ${stationId}`);

    const station = await Station.findByPk(stationId);
    if (!station) {
        logger.error(`Station not found for stationId: ${stationId}`);
        throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    }

    if (!station.isEnabled) {
        logger.warn(`Station is disabled - stationId: ${stationId}`);
        throw new ApiError(httpStatus.BAD_REQUEST, 'Station is currently disabled');
    }

    logger.info(`Fetching charger data - stationId: ${stationId}, chargerId: ${chargerId}`);

    const chargerQuery = {
        stationId,
        isEnabled: true,
        ...(chargerId && { id: chargerId })
    };

    const chargers = await Charger.findAll({
        where: chargerQuery
    });

    logger.info(`Chargers fetched: ${chargers.length}`);

    if (chargers.length === 0) {
        logger.warn(`No active chargers found for stationId: ${stationId}`);
        return {
            stationId,
            stationName: station.stationName,
            date: selectedDate.format('YYYY-MM-DD'),
            dayOfWeek,
            message: chargerId ? 'Charger not found or disabled' : 'No active chargers found for this station',
            availableSlots: []
        };
    }

    const operationalSchedule = station.operationalSchedule || [];
    const operationalDay = operationalSchedule.find(day => day.day === dayOfWeek);

    if (!operationalDay) {
        logger.info(`Station is closed on ${dayOfWeek} - stationId: ${stationId}`);
        return {
            stationId,
            stationName: station.stationName,
            date: selectedDate.format('YYYY-MM-DD'),
            dayOfWeek,
            message: `Station is closed on ${dayOfWeek}`,
            availableSlots: []
        };
    }

    logger.info(`Generating time slots - Start: ${operationalDay.startTime}, End: ${operationalDay.endTime}`);

    const slots = [];
    // Create moment objects for start and end times in UTC
    const startTime = moment.utc(selectedDate.format('YYYY-MM-DD') + ' ' + operationalDay.startTime);
    const endTime = moment.utc(selectedDate.format('YYYY-MM-DD') + ' ' + operationalDay.endTime);
    
    // Handle cases where end time is on the next day
    if (endTime.isBefore(startTime)) {
        endTime.add(1, 'day');
    }

    const slotDurationMinutes = 60;
    const totalMinutes = endTime.diff(startTime, 'minutes');
    const numberOfSlots = Math.floor(totalMinutes / slotDurationMinutes);

    // Generate all possible hourly slots
    for (let i = 0; i < numberOfSlots; i++) {
        const slotStart = startTime.clone().add(i * slotDurationMinutes, 'minutes');
        const slotEnd = slotStart.clone().add(slotDurationMinutes, 'minutes');

        slots.push({
            startTime: slotStart.format('HH:mm:ss'),
            endTime: slotEnd.format('HH:mm:ss')
        });
    }

    logger.info(`Total slots generated: ${slots.length}`);

    // Use UTC dates for database queries
    const startOfDay = selectedDate.clone().startOf('day').toDate();
    const endOfDay = selectedDate.clone().endOf('day').toDate();

    const chargerIds = chargers.map(charger => charger.id);

    const bookings = await Booking.findAll({
        where: {
            chargerId: { [Op.in]: chargerIds },
            startTime: { [Op.between]: [startOfDay, endOfDay] },
            status: { [Op.in]: [BOOKING_STATUS.PENDING, BOOKING_STATUS.UPCOMING] }
        },
        attributes: ['chargerId', 'startTime', 'endTime', 'status'],
        order: [['startTime', 'ASC']]
    });

    logger.info(`Total bookings found: ${bookings.length}`);

    // Filter slots to only include available ones or ones with pending bookings
    const availableSlots = slots.filter(slot => {
        const slotStartTime = selectedDate.clone().set({
            hour: parseInt(slot.startTime.split(':')[0]),
            minute: parseInt(slot.startTime.split(':')[1]),
            second: 0
        });

        const slotEndTime = selectedDate.clone().set({
            hour: parseInt(slot.endTime.split(':')[0]),
            minute: parseInt(slot.endTime.split(':')[1]),
            second: 0
        });

        // Handle slots that cross midnight
        if (slot.endTime < slot.startTime) {
            slotEndTime.add(1, 'day');
        }

        // A slot is available if for ANY charger:
        // 1. There are no bookings for this time slot, OR
        // 2. All bookings for this time slot are in PENDING status
            return chargers.some(charger => {
                // Find any bookings that overlap with this slot for this charger
                const conflictingBookings = bookings.filter(booking => {
                    const bookingStart = moment.utc(booking.startTime);
                    const bookingEnd = moment.utc(booking.endTime);
                    
                    // Check if booking overlaps with slot
                    const isOverlapping = bookingStart.isBefore(slotEndTime) && 
                                        bookingEnd.isAfter(slotStartTime);
                    
                    return booking.chargerId === charger.id && isOverlapping;
                });

                // Slot is available if there are no bookings or all bookings are pending
                return conflictingBookings.length === 0 || 
                    conflictingBookings.every(booking => booking.status === BOOKING_STATUS.PENDING);
            });
    });

    // Sort slots by start time
    availableSlots.sort((a, b) => {
        return moment(a.startTime, 'HH:mm:ss').diff(moment(b.startTime, 'HH:mm:ss'));
    });

    logger.info(`Available slots calculated: ${availableSlots.length} out of ${slots.length}`);

    const result = {
        stationId,
        stationName: station.stationName,
        date: selectedDate.format('YYYY-MM-DD'),
        dayOfWeek,
        operationalHours: {
            open: operationalDay.startTime,
            close: operationalDay.endTime
        },
        availableSlots
    };

    return result;
};

/**
 * Confirms a pending booking by a vendor
 * @param {string} vendorId - Vendor ID
 * @param {string} bookingId - Booking ID to confirm
 * @returns {Promise<Booking>} - Updated booking
 */
const confirmBooking = async (userId, bookingId) => {
    // Get booking details with station information
    const booking = await Booking.findOne({
        where: { id: bookingId },
        include: [{
            model: Station,
            attributes: ['vendorId',"stationName"],
            include:{
                model:Vendor,
                attributes:['userId']
            }
        }]
    });

    if (!booking) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');
    }

    // Check if booking is in pending state
    if (booking.status !== BOOKING_STATUS.PENDING) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Only pending bookings can be confirmed');
    }

    // Verify vendor owns the station
    if (booking.Station.Vendor.userId !== userId) {
        throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to confirm bookings for this station');
    }

    // Update booking status to confirmed
    await booking.update({
        status: BOOKING_STATUS.UPCOMING
    });

    // Emit booking confirmed event
    eventEmitter.emit(BOOKING_EVENTS.CONFIRMED, {
        bookingId: booking.id,
        userId: booking.userId,
        stationName: booking.Station.stationName,
        startTime: booking.startTime,
        endTime: booking.endTime
    });

    return booking;
};

/**
 * Get booking history for a specific station with sorting and pagination
 * @param {number} stationId - Station ID
 * @param {number} userId - User ID (to verify vendor ownership)
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {string} options.sortBy - Field to sort by
 * @param {string} options.sortOrder - Sort order (asc/desc)
 * @returns {Promise<Object>} - Paginated booking history
 */
const getStationBookingHistory = async (stationId, userId, { page, limit, sortBy='startTime', sortOrder='DESC',timeFilter }) => {
    // Get vendor details from user ID

    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Only vendors can view station booking history');
    }

    // Verify if the vendor owns the station
    const station = await Station.findOne({
        where: {
            id: stationId,
            vendorId: vendor.id
        }
    });

    if (!station) {
        throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view booking history for this station');
    }

    const offset = (page - 1) * limit;

    // Get total count
    const totalCount = await Booking.count({
        where: { stationId, status: BOOKING_STATUS.COMPLETED }
    });

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / limit);

     // Calculate the date threshold based on the time filter
  let dateThreshold = null;
  if (timeFilter !== 'all') {
    const months = timeFilter === '3months' ? 3 : 6;
    dateThreshold = moment().subtract(months, 'months').toDate();
  }

  // Base booking where clause
  const bookingWhere = { stationId,status: BOOKING_STATUS.COMPLETED };
  if (dateThreshold) {
    bookingWhere.startTime = {
      [Op.gte]: dateThreshold
    };
  }
    // Get bookings with sorting and pagination
    const bookings = await Booking.findAll({
        where: bookingWhere,
        order: [[sortBy, sortOrder.toUpperCase()]], // Sorting based on user input
        limit,
        offset,
        attributes: [
            'id',
            'startTime',
            'status',
            'totalCost',
            'createdAt',
        ],
        include: [
            {
                model: Station, // Including Station details
                attributes: [
                    'stationName',  // Fields you want from the Station
                ]
            }
        ]
    });

    return {
        bookings,
        pagination: {
            totalItems: totalCount,
            totalPages,
            currentPage: page,
            itemsPerPage: limit,
            hasNextPage: page < totalPages,
            hasPreviousPage: page > 1
        }
    };
};


module.exports = {
    bookChargingSlot,
    getBookingDetails,
    getBookingDetailsById,
    cancelBooking,
    changeTimeSlot,
    getAvailableTimeSlots,
    confirmBooking,
    getStationBookingHistory
}
