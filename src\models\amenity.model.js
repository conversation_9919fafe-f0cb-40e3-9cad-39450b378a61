const { DataTypes, Sequelize } = require('sequelize');
const { paginate } = require('./plugins/paginate'); // Custom pagination plugin
const { sequelize } = require('../config/database'); // Import sequelize instance


const Amenity = sequelize.define('Amenity', {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
    },
  }, {
    tableName: 'amenities',
    timestamps: true,
  });
  
  module.exports = Amenity;