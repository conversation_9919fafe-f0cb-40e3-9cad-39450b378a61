const httpStatus = require('http-status');
const { Admin } = require('../../models');
const { sequelize } = require('../../config/database');
const ApiError = require('../../utils/ApiError');
const jwt = require('jsonwebtoken');
const { ADMIN_ROLE } = require('../../constants');
const config = require('../../config/config');
const logger = require('../../config/logger');

/**
 * Login admin user
 * @param {string} email
 * @param {string} password
 * @returns {Promise<{admin: Admin, token: string}>}
 */
const login = async (email, password) => {
    const admin = await Admin.findOne({
        where: { email, isActive: true }
    });

    if (!admin || !(await admin.isPasswordMatch(password))) {
        throw new ApiError(httpStatus.UNAUTHORIZED, 'Invalid credentials');
    }

    await admin.update({ lastLoginAt: new Date() });

    const token = jwt.sign(
        { id: admin.id, email: admin.email, role: admin.role },
        config.jwt.secret,
        { expiresIn: config.jwt.accessExpirationMinutes + 'm' }
    );

    return { admin, token };
};

/**
 * Create a new admin
 * @param {Object} adminBody
 * @param {string} adminBody.name
 * @param {string} adminBody.email
 * @param {string} adminBody.password
 * @param {string} adminBody.role
 * @param {string} requestingAdminRole - Role of the admin making the request
 * @returns {Promise<Admin>}
 */
const createAdmin = async (adminBody, requestingAdminRole) => {
    if (requestingAdminRole !== ADMIN_ROLE.SUPER_ADMIN) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Only super admin can create new admins');
    }

    const transaction = await sequelize.transaction();
    try {
        const newAdmin = await Admin.create(adminBody, { transaction });
        await transaction.commit();
        return newAdmin;
    } catch (error) {
        await transaction.rollback();
        throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Admin creation failed');
    }
};

module.exports = {
    login,
    createAdmin
}; 
