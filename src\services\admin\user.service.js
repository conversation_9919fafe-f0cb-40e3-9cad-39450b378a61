const moment = require('moment');
const { Op } = require('sequelize');
const { User, Vendor, Booking, Station, UserVehicle,ManufacturingUnit,VehicleBrand,VehicleModel,Charger,Connector,Power } = require('../../models');
const httpStatus = require('http-status');
const ApiError = require('../../utils/ApiError');
const admin = require('firebase-admin');
const logger = require('../../config/logger');
const { sequelize } = require('../../config/database');
const { getYearStartAndEnd, getGrowthPercentage, countRecordsInYear, countRecordsByStatus } = require('../../utils/analytics.utils');

/**
 * Get all users with pagination and filtering
 * @param {Object} options - Pagination and filtering options
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Number of items per page
 * @param {string} [options.sortBy='createdAt:desc'] - Sort order
 * @param {string} [options.name] - Filter by name (partial match)
 * @param {string} [options.email] - Filter by email (partial match)
 * @param {string} [options.phone] - Filter by phone (partial match)
 * @param {boolean} [options.isActive] - Filter by active status
 * @param {string} [options.fromDate] - Filter by creation date (from) - YYYY-MM-DD
 * @param {string} [options.toDate] - Filter by creation date (to) - YYYY-MM-DD
 * @returns {Promise<Object>} - Paginated result with users and metadata
 */
const getAllUsers = async (options = {}) => {
  const {
    page,
    limit,
    sortBy,
    name,
    email,
    phone,
    isActive,
    fromDate,
    toDate
  } = options;

  logger.info('Getting users with filters', {
    page, limit, sortBy, name, email, phone, isActive, fromDate, toDate
  });

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build where clause for filtering
  const where = {};

  // Add name filter (case-insensitive partial match)
  if (name) {
    where.name = { [Op.iLike]: `%${name}%` };
  }

  // Add email filter (case-insensitive partial match)
  if (email) {
    where.email = { [Op.iLike]: `%${email}%` };
  }

  // Add phone filter (partial match)
  if (phone) {
    where.phone = { [Op.iLike]: `%${phone}%` };
  }

  // Add isActive filter
  if (isActive !== undefined) {
    where.isActive = isActive;
  }

  // Add date range filter
  if (fromDate || toDate) {
    where.createdAt = {};

    if (fromDate) {
      const fromDateObj = moment(fromDate, 'YYYY-MM-DD').startOf('day').toDate();
      where.createdAt[Op.gte] = fromDateObj;
    }

    if (toDate) {
      const toDateObj = moment(toDate, 'YYYY-MM-DD').endOf('day').toDate();
      where.createdAt[Op.lte] = toDateObj;
    }
  }

  // Parse sort options
  const order = [];
  const sortOptions = sortBy.split(',');

  for (const option of sortOptions) {
    const [field, direction = 'asc'] = option.split(':');
    order.push([field, direction.toUpperCase()]);
  }


  const { rows: users, count: totalItems } = await User.findAndCountAll({
    where,
    order,
    limit,
    offset,
    raw: true, // Improves performance by returning plain objects
    attributes: [
      'id',
      'name',
      'email',
      'phone',
      'gender',
      'dob',
      'isActive',
      'country',
      // 'isActive',
      'profilePicUrl',
      'createdAt',
      'updatedAt',
    ],
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / limit);

  // Return paginated result
  return {
    users,
    pagination: {
      totalItems,
      totalPages,
      currentPage: parseInt(page),
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  };
};

/**
 * Toggle user active status
 * @param {string} userId - User ID
 * @param {boolean} isActive - New active status
 * @param {string} [suspendedReason] - Reason for suspension
 * @param {string} [suspendedComment] - Optional comment
 * @returns {Promise<Object>} - Updated user
 */
const toggleUserStatus = async (userId, isActive, suspendedReason = null, suspendedComment = null) => {
  logger.info(`Toggling user status - userId: ${userId}, isActive: ${isActive}`);

  const user = await User.findByPk(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }

  if (!user.firebaseUid) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'User has no associated Firebase account');
  }

  // Update Firebase status
  await admin.auth().updateUser(user.firebaseUid, { disabled: !isActive });
  logger.info(`Firebase user ${user.firebaseUid} ${isActive ? 'enabled' : 'disabled'}`);

  // Use a transaction to ensure data consistency
  await sequelize.transaction(async (t) => {
    // Update user
    await user.update({
      isActive,
      suspendedReason: !isActive ? suspendedReason : null,
      suspendedComment: !isActive ? suspendedComment : null,
    }, { transaction: t });

    // Update vendor if exists
    const vendor = await Vendor.findOne({ where: { userId }, transaction: t });
    if (vendor) {
      await vendor.update({ isActive }, { transaction: t });
      logger.info(`Vendor ${vendor.id} ${isActive ? 'enabled' : 'disabled'}`);
    }
  });

  return {
    id: user.id,
    email: user.email,
    isActive: user.isActive,
    suspendedReason: user.suspendedReason,
    suspendedComment: user.suspendedComment,
  };
};

/**
 * Delete a user (soft delete in DB, hard delete in Firebase)
 * @param {string} userId - User ID
 * @returns {Promise<Object>} - Deleted user info
 */
const deleteUser = async (userId) => {
  try {
    // Find the user
    const user = await User.findByPk(userId);
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
    }

    await admin.auth().deleteUser(user.firebaseUid);
    logger.info(`Firebase user deleted - firebaseUid: ${user.firebaseUid}`);

    // Temporarily disable the foreign key constraint
    await sequelize.query('SET CONSTRAINTS ALL DEFERRED');

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      // Find the associated vendor
      const vendor = await Vendor.findOne({ where: { userId } });

      // Use direct SQL to soft delete vendor first
      if (vendor) {
        await sequelize.query(
          `UPDATE vendors SET "deletedAt" = NOW() WHERE "userId" = :userId AND "deletedAt" IS NULL`,
          {
            replacements: { userId },
            type: sequelize.QueryTypes.UPDATE,
            transaction
          }
        );
        logger.info(`Soft deleted vendor - vendorId: ${vendor.id}`);
      }

      // Use direct SQL to soft delete user
      await sequelize.query(
        `UPDATE users SET "deletedAt" = NOW() WHERE id = :userId AND "deletedAt" IS NULL`,
        {
          replacements: { userId },
          type: sequelize.QueryTypes.UPDATE,
          transaction
        }
      );
      logger.info(`Soft deleted user - userId: ${user.id}`);

      // Commit the transaction
      await transaction.commit();

      // Re-enable constraints
      await sequelize.query('SET CONSTRAINTS ALL IMMEDIATE');

      return {
        id: user.id,
        email: user.email,
        deletedAt: new Date(),
      };
    } catch (error) {
      await transaction.rollback();
      // Re-enable constraints
      await sequelize.query('SET CONSTRAINTS ALL IMMEDIATE');
      throw error;
    }
  } catch (error) {
    logger.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Create a user in Firebase
 * @param {string} email - User email
 * @param {string} password - User password
 * @returns {Promise<Object>} - Firebase user record
 */
const createFirebaseUser = async (email, password) => {
  try {
    // Create user in Firebase
    const userRecord = await admin.auth().createUser({
      email,
      password,
      emailVerified: true
    });

    return userRecord;
  } catch (error) {
    console.error('Error creating Firebase user:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message || 'Failed to create user in Firebase');
  }
};

/**
 * Create a user in the database
 * @param {Object} userData - User data
 * @returns {Promise<User>} - Created user
 */
const createUser = async (userData) => {
  try {
    const user = await User.create(userData);
    return user;
  } catch (error) {
    console.error('Error creating user in database:', error);

    // If there's an error creating the user in the database, delete the Firebase user
    if (userData.firebaseUid) {
      try {
        await admin.auth().deleteUser(userData.firebaseUid);
      } catch (deleteError) {
        console.error('Error deleting Firebase user after database error:', deleteError);
      }
    }

    throw new ApiError(httpStatus.BAD_REQUEST, error.message || 'Failed to create user in database');
  }
};

/**
 * Get user statistics with year-over-year comparison
 * @returns {Promise<Object>} User statistics
 */
const getUserStats = async () => {
  const currentYear = moment().year();
  const previousYear = currentYear - 1;

  // Get year date ranges
  const currentYearRange = getYearStartAndEnd(currentYear);
  const previousYearRange = getYearStartAndEnd(previousYear);

  // For all-time queries, use a very old start date and current date as end date
  const allTimeStartDate = moment('2000-01-01').startOf('day').toDate();
  const currentDate = moment().endOf('day').toDate();

  // Run all queries in parallel for better performance
  const [
    totalUsers,
    totalUsersPreviousYear,
    newUsers,
    newUsersPreviousYear,
    activeUsers,
    activeUsersPreviousYear,
    inactiveUsers,
    inactiveUsersPreviousYear
  ] = await Promise.all([
    // Total users (current)
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: currentDate
    }),

    // Total users (previous year)
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: previousYearRange.endDate
    }),

    // New users this year
    countRecordsByStatus(User, {
      startDate: currentYearRange.startDate,
      endDate: currentYearRange.endDate
    }),

    // New users previous year
    countRecordsByStatus(User, {
      startDate: previousYearRange.startDate,
      endDate: previousYearRange.endDate
    }),

    // Active users
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: currentDate,
      additionalWhere: { isActive: true }
    }),

    // Active users previous year
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: previousYearRange.endDate,
      additionalWhere: { isActive: true }
    }),

    // Inactive users
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: currentDate,
      additionalWhere: { isActive: false }
    }),

    // Inactive users previous year
    countRecordsByStatus(User, {
      startDate: allTimeStartDate,
      endDate: previousYearRange.endDate,
      additionalWhere: { isActive: false }
    })
  ]);

  return {
    totalUsers: {
      count: totalUsers,
      growth: getGrowthPercentage(totalUsers, totalUsersPreviousYear)
    },
    newUsers: {
      count: newUsers,
      growth: getGrowthPercentage(newUsers, newUsersPreviousYear)
    },
    activeUsers: {
      count: activeUsers,
      growth: getGrowthPercentage(activeUsers, activeUsersPreviousYear)
    },
    inactiveUsers: {
      count: inactiveUsers,
      growth: getGrowthPercentage(inactiveUsers, inactiveUsersPreviousYear)
    }
  };
};

/**
 * Get user by ID
 * @param {number} userId - User ID
 * @returns {Promise<Object>} User object
 * @throws {ApiError} If user is not found
 */
const getUserById = async (userId) => {
  logger.info(`Getting user by ID - userId: ${userId}`);

  const user = await User.findOne({
    where: {
      id: userId,
      deletedAt: null // Ensure we don't return soft-deleted users
    },
    attributes: [
      'id',
      'name',
      'email',
      'phone',
      'gender',
      'dob',
      'isActive',
      'country',
      'profilePicUrl',
      'suspendedReason',
      'suspendedComment',
      'createdAt',
      'updatedAt'
    ],
    raw: true // Returns plain object instead of Sequelize instance
  });

  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }

  return user;
};

/**
 * Get all bookings for a specific user with filters
 * @param {Object} options - Filtering and pagination options
 * @param {number} options.userId - User ID
 * @param {number} [options.page=1] - Page number
 * @param {number} [options.limit=10] - Number of items per page
 * @param {string} [options.status] - Filter by booking status (completed/cancelled/pending)
 * @param {string} [options.fromDate] - Filter by start date (YYYY-MM-DD)
 * @param {string} [options.toDate] - Filter by end date (YYYY-MM-DD)
 * @returns {Promise<Object>} - Paginated result with bookings and metadata
 */
const getUserBookings = async (options) => {
  const { 
    userId, 
    page = 1, 
    limit = 10, 
    status, 
    fromDate, 
    toDate, 
    region,
    minPrice,
    maxPrice 
  } = options;

  logger.info('Getting user bookings with filters', { 
    userId, page, limit, status, fromDate, toDate, region, minPrice, maxPrice 
  });

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build where clause for filtering
  const where = {
    userId,
  };


  // Add status filter
  if (status) {
    where.status = status;
  }


  // Add date range filter
  if (fromDate || toDate) {
    where.createdAt = {};
    
    if (fromDate) {
      const fromDateObj = moment(fromDate, 'YYYY-MM-DD').startOf('day').toDate();
      where.createdAt[Op.gte] = fromDateObj;
    }
    
    if (toDate) {
      const toDateObj = moment(toDate, 'YYYY-MM-DD').endOf('day').toDate();
      where.createdAt[Op.lte] = toDateObj;
    }
  }

  // Add price range filter
  if (minPrice !== undefined || maxPrice !== undefined) {
    where.totalCost = {};
    
    if (minPrice !== undefined) {
      where.totalCost[Op.gte] = parseFloat(minPrice);
    }
    
    if (maxPrice !== undefined) {
      where.totalCost[Op.lte] = parseFloat(maxPrice);
    }
  }

  // Get bookings with related data
  const { rows: bookings, count: totalItems } = await Booking.findAndCountAll({
    where,
    order: [['startTime', 'DESC']],
    limit,
    offset,
    attributes: ["id", "status", "totalCost", "startTime","createdAt"],
    include: [
      {
        model: Station,
        attributes: ['id', 'stationName', 'address',],
      },
      {
        model: UserVehicle,
        attributes: ['id'],
        include: [{
          model: ManufacturingUnit,
          attributes: ['name', 'location'],
          ...(region && { where: { location: region } })
        }],
        required: region ? true : false  // Make the join required only if region filter is applied
      }],
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalItems / limit);

  const transformedBookings = bookings.map(booking => ({
    id: booking.id,
    status: booking.status,
    totalCost: booking.totalCost,
    startTime: booking.startTime,
    createdAt: booking.createdAt,
    station: booking.Station ? {
      id: booking.Station.id,
      name: booking.Station.stationName,
      address: booking.Station.address
    } : null,
    region: booking?.userVehicle?.ManufacturingUnit?.location || null
  }));

  // Return paginated result
  return {
    bookings:transformedBookings,
    pagination: {
      totalItems,
      totalPages,
      currentPage: parseInt(page),
      itemsPerPage: parseInt(limit),
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  };
};

/**
 * Get detailed booking information by booking ID
 * @param {number} bookingId - Booking ID
 * @returns {Promise<Object>} - Detailed booking information
 * @throws {ApiError} If booking is not found
 */
const getBookingDetails = async (bookingId) => {
  logger.info(`Getting booking details - bookingId: ${bookingId}`);

  const booking = await Booking.findOne({
    where: { 
      id: bookingId,
      // deletedAt: null // Ensure we don't return soft-deleted bookings
    },
    attributes: [
      'id',
      'status',
      'totalCost',
      'startTime',
      'endTime',
      'createdAt',
      'updatedAt',
      'userId',
      'paymentStatus',
    ],
    include: [
      {
        model: Station,
        attributes: [
          'id', 
          'stationName', 
          'address'
        ],
        include: [
          {
            model: Charger,
            attributes: ["id", "connectorId", "powerId", "pricePerHour"],
            include: [
              { 
                model: Connector, 
                attributes: ["id", "name", "type"] 
              },
              { 
                model: Power, 
                attributes: ["id", "value", "unit"] 
              }
            ]
      },
      ]
      },
      {
        model: UserVehicle,
        attributes: ['id','brandName','modelName'],
        include: [
          {
            model: VehicleBrand,
            attributes: ['name'],
            as: 'VehicleBrand'
          },
          {
            model: VehicleModel,
            attributes: ['name'],
            as: 'VehicleModel'
          }
        ],
      }
    ]
  });

  if (!booking) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');
  }

  // Transform the data to a cleaner format
  // const transformedBooking = {
  //   id: booking.id,
  //   status: booking.status,
  //   totalCost: booking.totalCost,
  //   startTime: booking.startTime,
  //   endTime: booking.endTime,
  //   createdAt: booking.createdAt,
  //   updatedAt: booking.updatedAt,
  //   payment: {
  //     status: booking.paymentStatus,
  //     method: booking.paymentMethod,
  //     transactionId: booking.transactionId
  //   },
  //   user: booking.User ? {
  //     id: booking.User.id,
  //     name: booking.User.name,
  //     email: booking.User.email,
  //     phone: booking.User.phone,
  //     profilePic: booking.User.profilePicUrl
  //   } : null,
  //   station: booking.Station ? {
  //     id: booking.Station.id,
  //     name: booking.Station.stationName,
  //     address: booking.Station.address,
  //     location: {
  //       latitude: booking.Station.latitude,
  //       longitude: booking.Station.longitude
  //     },
  //     contact: booking.Station.contactNumber,
  //     operatingHours: booking.Station.operatingHours
  //   } : null,
  //   vehicle: booking.UserVehicle ? {
  //     id: booking.UserVehicle.id,
  //     vehicleNumber: booking.UserVehicle.vehicleNumber,
  //     model: booking.UserVehicle.model,
  //     batteryCapacity: booking.UserVehicle.batteryCapacity,
  //     chargingType: booking.UserVehicle.chargingType,
  //     manufacturingUnit: booking.UserVehicle.ManufacturingUnit ? {
  //       id: booking.UserVehicle.ManufacturingUnit.id,
  //       name: booking.UserVehicle.ManufacturingUnit.name,
  //       location: booking.UserVehicle.ManufacturingUnit.location,
  //       contact: booking.UserVehicle.ManufacturingUnit.contactNumber,
  //       email: booking.UserVehicle.ManufacturingUnit.email
  //     } : null
  //   } : null,
  //   charger: booking.Charger ? {
  //     id: booking.Charger.id,
  //     name: booking.Charger.name,
  //     type: booking.Charger.type,
  //     power: booking.Charger.power,
  //     connectorType: booking.Charger.connectorType,
  //     status: booking.Charger.status,
  //     lastMaintenanceDate: booking.Charger.lastMaintenanceDate
  //   } : null
  // };

  return booking;
};
module.exports = {
  getAllUsers,
  toggleUserStatus,
  deleteUser,
  createFirebaseUser,
  createUser,
  getUserStats,
  getUserById,
  getUserBookings,
  getBookingDetails
};
