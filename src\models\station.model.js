const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database'); // Import sequelize instance

const Station = sequelize.define('Station', {
    city: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    pincode: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    address: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    state: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    operationalSchedule: {
        type: DataTypes.JSONB, // Store operational hours for selected days
        allowNull: false,
        defaultValue: {}, // Default to an empty object
        validate: {
            isValidOperationalSchedule(value) {
                const days = Object.keys(value);
                for (const day of days) {
                    if (!value[day].startTime || !value[day].endTime) {
                        throw new Error(`Operational hours for ${day} must include startTime and endTime`);
                    }
                }
            }
        }
    },
    images: {
        type: DataTypes.JSONB, // Store an array of image URLs or file keys
        allowNull: true,
    },
    approvalStatus: {
        type: DataTypes.ENUM('pending', 'approved', 'rejected'),
        defaultValue: 'pending',
    },
    isEnabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
    },
    vendorId: {
        type: DataTypes.INTEGER,
        references: {
            model: 'vendors', // Foreign key references the `Vendor` table
            key: 'id',
        },
        allowNull: false,
    },
    phoneNumber:{
        type:DataTypes.STRING,
        allowNull: true,
    },
    stationName: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
            len: [1, 255], // Ensures the string is not too long
          },
    },
    latitude: {
        type: DataTypes.DOUBLE,
        allowNull: false,
    },
    longitude: {
        type: DataTypes.DOUBLE,
        allowNull: false,
    },
    totalReviews: {
        type: DataTypes.INTEGER,
        defaultValue: 0
    },
    averageRating: {
        type: DataTypes.FLOAT,
        defaultValue: 0
    },
    totalRatingSum: {
        type: DataTypes.INTEGER,
        defaultValue: 0
    },
    reason: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Reason for station status change or maintenance notes'
      }
}, {
    tableName: 'stations',
    timestamps: true,
});

module.exports = Station;
