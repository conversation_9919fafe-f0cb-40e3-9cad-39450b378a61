const Vendor = require('../models/vendor.model');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const Station = require('../models/station.model');
const Charger = require('../models/charger.model');
const Connector = require('../models/connector.model');
const Amenity = require('../models/amenity.model');

const addVendor = async (vendorData, userId) => {
    const { businessName, email, phone } = vendorData;

    const existingVendor = await Vendor.findOne({ where: { userId } });
    if (existingVendor) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Vendor already exists');
    }
    const vendor = await Vendor.create({ businessName, email, phone, userId });
    return vendor;

};

const setupVendorProfile = async (vendorData, userId) => {
    const { stationName, stationAddress, country } = vendorData;

    // Find vendor and check if exists
    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }

    // Check if vendor KYC is approved
    if (!vendor.isKycApproved) {
        throw new ApiError(httpStatus.BAD_REQUEST, 'Vendor KYC is not approved');
    }

    vendor.stationName = stationName;
    vendor.stationAddress = stationAddress;
    vendor.country = country;
    vendor.profileSetupCompleted = true;

    await vendor.save();

    return vendor;

}

const getVendorProfile = async (userId) => {
    // Find vendor and check if exists
    const vendor = await Vendor.findOne({ where: { userId }, attributes: { exclude: ['createdAt', 'updatedAt'] } });
    if (!vendor) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }
    return vendor;
}

const getVendorStations = async (userId, page = 1, limit = 10) => {
    // Find the vendor
    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

   // Get total station count separately
   const { rows: stations, count: totalStations } = await Station.findAndCountAll({
    where: { vendorId: vendor.id },
    limit,
    offset,
    attributes: { exclude: ["createdAt", "updatedAt"] },
    order: [["id", "DESC"]],
    distinct: true, 
    include: [
        {
            model: Charger,
            attributes: ["id", "connectorId", "powerId", "pricePerHour"],
            include: [{ model: Connector, attributes: ["id", "name", "type"] }]
        },
        {
            model: Amenity,
            as: "amenities",
            attributes: ["id", "name"],
            through: { attributes: [] }
        }
    ]
});

    return {
        stations,
        pagination: {
            page,
            limit,
            totalStations,
            totalPages: Math.ceil(totalStations / limit),
        },
    };
};

const updateVendor = async (vendorData, userId) => {
    const { businessName, stationName, stationAddress, country  } = vendorData;

    // Find vendor and check if exists
    const vendor = await Vendor.findOne({ where: { userId } });
    if (!vendor) {
        throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }

    // Check if vendor KYC was approved before allowing update
    if (!vendor.isKycApproved) {
        throw new ApiError(httpStatus.FORBIDDEN, 'Cannot update details. KYC is not approved');
    }

    // Update vendor details and set KYC status back to pending
    await vendor.update({
        businessName: businessName || vendor.businessName,
        stationName: stationName || vendor.stationName,
        stationAddress: stationAddress || vendor.stationAddress,
        country: country || vendor.country,
        isKycApproved: false, // Reset KYC status to pending after update
    });

    return vendor;
};

module.exports = {
    addVendor,
    setupVendorProfile,
    getVendorProfile,
    getVendorStations,
    updateVendor
};
