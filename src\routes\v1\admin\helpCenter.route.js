const express = require('express');
const validate = require('../../../middlewares/validate');
const helpCenterController = require('../../../controllers/admin/helpCenter.controller');
const { adminAuth } = require('../../../middlewares/adminAuth');
const helpCenterValidation = require('../../../validations/helpCenter.validation');
const router = express.Router();


/**
 * @api {get} v1/help-center/tickets Get all support tickets
 * @apiDescription Get all help center support tickets with pagination and filtering
 * @apiParam {Number} [page=1] Page number
 * @apiParam {Number} [limit=10] Number of items per page
 * @apiParam {String} [sortBy=createdAt:asc] Sort by field and direction
 * @apiParam {String} [ticketNo] Filter by ticket number
 * @apiParam {String} [fromDate] Filter by date range (start)
 * @apiParam {String} [toDate] Filter by date range (end)
 * @apiSuccess {Object[]} tickets List of tickets
 * @apiSuccess {Object} pagination Pagination info
 */
router.get('/tickets', adminAuth, validate(helpCenterValidation.getTickets), helpCenterController.getTickets);

/**
 * @api {delete} v1/admin/help-center/tickets/:ticketId Delete a support ticket
 * @apiDescription Delete a help center support ticket by ID
 *

 * @apiParam {Integer} ticketId Ticket ID
 *
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.delete(
  '/tickets/:ticketId', 
  adminAuth, 
  validate(helpCenterValidation.deleteTicket), 
  helpCenterController.deleteTicket
);

/**
 * @api {post} v1/admin/help-center/tickets/:ticketId/answer Answer a support ticket
 * @apiDescription Admin responds to a help center support ticket
 *
 * @apiParam {String} ticketId Ticket ID
 * @apiParam {String} response Admin's response to the ticket
 *
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {Object} data Updated ticket
 * @apiSuccess {String} message Success message
 */
router.post(
  '/tickets/:ticketId/answer', 
  adminAuth, 
  validate(helpCenterValidation.answerTicket), 
  helpCenterController.answerTicket
);

/**
 * @api {get} v1/admin/help-center/statistics Get help center statistics
 * @apiDescription Get statistics about help center tickets including counts by status and growth percentages
 *
 * @apiSuccess {Object} data Statistics data
 * @apiSuccess {Object} data.totalTickets Total tickets count and growth
 * @apiSuccess {Object} data.resolvedTickets Resolved tickets count and growth
 * @apiSuccess {Object} data.pendingTickets Pending tickets count and growth
 * @apiSuccess {Object} data.inProgressTickets In-progress tickets count and growth
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.get(
  '/statistics', 
  adminAuth, 
  helpCenterController.getStatistics
);

module.exports = router;
