const express = require('express');
const validate = require('../../../middlewares/validate');
const faqController = require('../../../controllers/admin/faq.controller');
const { adminAuth } = require('../../../middlewares/adminAuth');
const faqValidation = require('../../../validations/admin/faq.validation');
const router = express.Router();

/**
 * @api {post} v1/admin/faqs Create a new FAQ
 * @apiDescription Create a new frequently asked question
 * @apiName CreateFAQ
 * @apiGroup AdminFAQ
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {String} question The question text
 * @apiParam {String} answer The answer text
 * @apiParam {String} type Category of the FAQ (Account, Service, Booking, Payment, Technical, Other)
 * @apiParam {String} [status=draft] Publication status (live, draft, paused)
 * @apiParam {Number} [displayOrder] Order in which FAQs are displayed
 *
 * @apiSuccess {Object} faq Created FAQ
 */
router.post(
  '/',
  adminAuth,
  validate(faqValidation.createFAQ),
  faqController.createFAQ
);

/**
 * @api {delete} v1/admin/faqs/:faqId Delete a FAQ
 * @apiDescription Delete a frequently asked question by ID
 * @apiName DeleteFAQ
 * @apiGroup AdminFAQ
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {Number} faqId FAQ ID to delete
 *
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.delete(
  '/:faqId',
  adminAuth,
  validate(faqValidation.deleteFAQ),
  faqController.deleteFAQ
);

/**
 * @api {put} v1/admin/faqs/:faqId Update a FAQ
 * @apiDescription Update a frequently asked question by ID
 * @apiName UpdateFAQ
 * @apiGroup AdminFAQ
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {Number} faqId FAQ ID to update
 * @apiParam {String} [question] The updated question text
 * @apiParam {String} [answer] The updated answer text
 * @apiParam {String} [type] Updated category of the FAQ (Account, Service, Booking, Payment, Technical, Other)
 * @apiParam {String} [status] Updated publication status (live, draft, paused)
 *
 * @apiSuccess {Object} faq Updated FAQ
 */
router.put(
  '/:faqId',
  adminAuth,
  validate(faqValidation.updateFAQ),
  faqController.updateFAQ
);

/**
 * @api {get} v1/admin/faqs Get all FAQs
 * @apiDescription Get all FAQs with pagination and filtering
 * @apiName GetFAQs
 * @apiGroup AdminFAQ
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {Number} [page=1] Page number
 * @apiParam {Number} [limit=10] Number of items per page
 * @apiParam {String} [sortBy=displayOrder:asc,createdAt:desc] Sort by field and direction
 * @apiParam {String} [type] Filter by FAQ type (Account, Service, Booking, Payment, Technical, Other)
 * @apiParam {String} [status] Filter by FAQ status (live, draft, paused)
 * @apiParam {String} [search] Search in question and answer
 * @apiParam {String} [fromDate] Filter by date range (start) in YYYY-MM-DD format
 * @apiParam {String} [toDate] Filter by date range (end) in YYYY-MM-DD format
 *
 * @apiSuccess {Object[]} data List of FAQs
 * @apiSuccess {Object} pagination Pagination info
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.get(
  '/',
  adminAuth,
  validate(faqValidation.getFAQs),
  faqController.getFAQs
);

module.exports = router;


