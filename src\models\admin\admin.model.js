const { DataTypes } = require('sequelize');
const { sequelize } = require('../../config/database');
const bcrypt = require('bcrypt');

const Admin = sequelize.define(
  'Admin',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      allowNull: false
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.ENUM('super_admin', 'admin'),
      defaultValue: 'admin',
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
      allowNull: false
    },
    lastLoginAt: {
      type: DataTypes.DATE,
      allowNull: true
    }
  },
  {
    tableName: 'admins',
    timestamps: true,
    hooks: {
      beforeCreate: async (admin) => {
        if (admin.password) {
          console.log('Hashing password for new admin:', admin.email);
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(admin.password, salt);
          console.log('Generated hashed password:', hashedPassword);
          admin.password = hashedPassword;
        }
      },
      beforeUpdate: async (admin) => {
        if (admin.changed('password')) {
          console.log('Hashing updated password for admin:', admin.email);
          const salt = await bcrypt.genSalt(10);
          const hashedPassword = await bcrypt.hash(admin.password, salt);
          console.log('Generated hashed password:', hashedPassword);
          admin.password = hashedPassword;
        }
      }
    }
  }
);

// Instance method to check password
Admin.prototype.isPasswordMatch = async function (password) {
  console.log('Comparing passwords:');
  console.log('Input password:', password);
  console.log('Stored hash:', this.password);
  const result = await bcrypt.compare(password, this.password);
  console.log('Comparison result:', result);
  return result;
};

module.exports = Admin; 