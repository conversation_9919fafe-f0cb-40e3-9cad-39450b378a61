const httpStatus = require('http-status');
const { VendorSubscription, SubscriptionPlan, Vendor, PaymentTransaction } = require('../models');
const { Op } = require('sequelize');
const {PAYMENT_SOURCE,PAYMENT_STATUS,PAYMENT_PURPOSE,PAYMENTS_METHODS,TRANSACTION_TYPE} = require('../constants');
const moment = require('moment');
const ApiError = require('../utils/ApiError');
const logger = require('../config/logger');
const zaincashService = require('./zaincash.service');
const fibService = require('./fib.service');
const { sequelize } = require('../config/database');
const config = require('../config/config');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const { PaymentSDK } = require('@first-iraqi-bank/sdk/payment');

// FIB SDK client initialization
const clientId = process.env.FIB_CLIENT_ID || 'your_fib_client_id';
const clientSecret = process.env.FIB_CLIENT_SECRET || 'your_fib_client_secret';
const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';

// Initialize the FIB SDK client
const FIB = PaymentSDK.getClient(clientId, clientSecret, environment);
/**
 * Initialize a payment for vendor subscription purchase
 * @param {Object} paymentData - Payment data
 * @param {string} paymentData.provider - Payment provider (e.g., 'zaincash', 'fib')
 * @param {number} paymentData.vendorId - Vendor ID
 * @param {number} paymentData.subscriptionPlanId - Subscription plan ID
 * @param {number} paymentData.userId - User ID making the payment
 * @param {boolean} [paymentData.autoRenew=false] - Whether to auto-renew the subscription
 * @returns {Promise<Object>} - Payment initialization result with provider-specific details
 */
const initializeSubscriptionPayment = async (paymentData) => {
  const { provider, vendorId, subscriptionPlanId, userId, autoRenew = false } = paymentData;

  // Verify vendor exists and user is authorized
  const vendor = await Vendor.findOne({ where: { id: vendorId, userId } });
  if (!vendor) {
    throw new ApiError(
      httpStatus.FORBIDDEN,
      'You are not authorized to purchase a subscription for this vendor'
    );
  }

  // Check if vendor already has an active subscription
  const activeSubscription = await VendorSubscription.findOne({
    where: { vendorId, isActive: true }
  });

  if (activeSubscription) {
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Vendor already has an active subscription. Please cancel it before purchasing a new one.'
    );
  }

  // Get subscription plan details
  const subscriptionPlan = await SubscriptionPlan.findOne({
    where: { id: subscriptionPlanId, isActive: true }
  });

  if (!subscriptionPlan) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription plan not found or inactive');
  }

  // Generate a unique order ID
  const orderId = `vendor_sub_${vendorId}_${Date.now()}`;

  // Set the redirect URL based on the provider
  let redirectUrl;
  if (provider.toLowerCase() === PAYMENTS_METHODS.ZAINCASH) {
    redirectUrl = `${config.clientUrl}v1/vendor-subscriptions/zaincash/callback`;
  } else if (provider.toLowerCase() === PAYMENTS_METHODS.FIB) {
    redirectUrl = `${config.clientUrl}v1/vendor-subscriptions/fib/callback`;
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, `Unsupported payment provider: ${provider}`);
  }

  logger.info(`Initializing ${provider} payment for vendor subscription`, {
    vendorId,
    subscriptionPlanId,
    amount: subscriptionPlan.price
  });

  // Create a payment transaction record
  const paymentTransaction = await PaymentTransaction.create({
    userId,
    amount: subscriptionPlan.price,
    paymentMethod: provider.toLowerCase(),
    source: PAYMENT_SOURCE.EXTERNAL,
    purpose: PAYMENT_PURPOSE.SUBSCRIPTION,
    status: PAYMENT_STATUS.PENDING,
    referenceId: orderId,
    referenceType: 'vendor_subscription',
    remarks: `Subscription purchase for ${subscriptionPlan.name} plan`,
    transactionType: TRANSACTION_TYPE.CREDIT,
    metadata: {
      vendorId,
      subscriptionPlanId,
      planName: subscriptionPlan.name,
      planDuration: `${subscriptionPlan.durationValue} ${subscriptionPlan.durationType}(s)`,
      autoRenew
    }
  });

  // Create a pending subscription record
  const startDate = new Date();
  let endDate;

  if (subscriptionPlan.durationType === 'month') {
    endDate = moment(startDate).add(subscriptionPlan.durationValue, 'months').toDate();
  } else if (subscriptionPlan.durationType === 'year') {
    endDate = moment(startDate).add(subscriptionPlan.durationValue, 'years').toDate();
  } else {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid subscription duration type');
  }

  const subscription = await VendorSubscription.create({
    vendorId,
    subscriptionPlanId,
    startDate,
    endDate,
    isActive: false, // Will be set to true after payment is confirmed
    autoRenew,
    inGracePeriod: false,
    renewalAttempts: 0
  });

  // // Update the payment transaction with the subscription ID
  // await paymentTransaction.update({
  //   referenceId: subscription.id
  // });

  // Route to the appropriate payment service based on provider
  let paymentResult;

  try {
    if (provider.toLowerCase() === PAYMENTS_METHODS.ZAINCASH) {
      // Adapt the existing zaincash service for subscription payments
      const zaincashData = {
        amount: Number(subscriptionPlan.price),
        orderId,
        redirectUrl,
        metadata: {
          vendorId,
          subscriptionPlanId,
          subscriptionId: subscription.id,
          paymentTransactionId: paymentTransaction.id,
          purpose: PAYMENT_PURPOSE.SUBSCRIPTION
        }
      };

      paymentResult = await initializeZaincashSubscriptionPayment(zaincashData);
    } else if (provider.toLowerCase() === PAYMENTS_METHODS.FIB) {
      // Adapt the existing FIB service for subscription payments
      const fibData = {
        amount: Number(subscriptionPlan.price),
        orderId,
        redirectUrl,
        metadata: {
          vendorId,
          subscriptionPlanId,
          subscriptionId: subscription.id,
          paymentTransactionId: paymentTransaction.id,
          purpose: PAYMENT_PURPOSE.SUBSCRIPTION
        }
      };

      paymentResult = await initializeFibSubscriptionPayment(fibData);
    }

    return {
      ...paymentResult,
      vendorId,
      subscriptionPlanId,
      planName: subscriptionPlan.name,
      amount: subscriptionPlan.price,
      currency: subscriptionPlan.currency
    };
  } catch (error) {
    // If payment initialization fails, delete the subscription and payment transaction
    await subscription.destroy();
    await paymentTransaction.destroy();
    throw error;
  }
};

/**
 * Initialize a ZainCash payment for vendor subscription
 * @param {Object} paymentData - Payment data
 * @returns {Promise<Object>} - Payment URL and transaction ID
 */
const initializeZaincashSubscriptionPayment = async (paymentData) => {
  const { amount, orderId, redirectUrl, metadata } = paymentData;

  // Set the token expire time
  const time = Date.now();

  // Build the transaction data to be encoded in a JWT token
  const data = {
    amount: amount,
    serviceType: 'ev-iq-vendor-subscription',
    msisdn: config.zaincash.msisdn,
    orderId: orderId,
    redirectUrl: redirectUrl,
    iat: time,
    exp: time + 60 * 60 * 4, // Token expires in 4 hours
  };

  // Encode the data with JWT
  const token = jwt.sign(data, config.zaincash.secret);

  // Prepare the payment data to be sent to ZainCash API
  const postData = {
    token: token,
    merchantId: config.zaincash.merchantId,
    lang: config.zaincash.lang || 'en'
  };

  try {
    // Determine which URLs to use based on environment
    const initUrl = config.env === 'production' ? 'https://api.zaincash.iq/transaction/init' : 'https://test.zaincash.iq/transaction/init';
    const requestUrl = config.env === 'production' ? 'https://api.zaincash.iq/transaction/pay?id=' : 'https://test.zaincash.iq/transaction/pay?id=';

    // Initialize the ZainCash transaction
    const response = await axios.post(initUrl, postData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    // Get the operation ID from the response
    const operationId = response.data.id;

    if (!operationId) {
      throw new ApiError(httpStatus.BAD_GATEWAY, 'Failed to initialize ZainCash payment');
    }

    // Update the payment transaction with the ZainCash operation ID
    await PaymentTransaction.update(
      {
        metadata: {
          ...metadata,
          zaincashOperationId: operationId
        }
      },
      {
        where: { id: metadata.paymentTransactionId }
      }
    );

    // Return the payment URL and transaction details
    return {
      paymentUrl: requestUrl + operationId,
      transactionId: operationId,
      orderId: orderId
    };
  } catch (error) {
    if (error.response) {
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `ZainCash API error: ${error.response.data.message || 'Unknown error'}`
      );
    }
    throw error;
  }
};

/**
 * Initialize a FIB payment for vendor subscription
 * @param {Object} paymentData - Payment data
 * @returns {Promise<Object>} - Payment URL and transaction ID
 */
const initializeFibSubscriptionPayment = async (paymentData) => {
  const { amount, orderId, redirectUrl, metadata } = paymentData;

  try {
    logger.info('Initializing FIB payment', { amount, orderId, redirectUrl, metadata });

    // Authenticate with FIB
    const authRes = await FIB.authenticate();

    if (!authRes.ok) {
      const errorData = await authRes.json();
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `FIB authentication failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
      );
    }

    const authData = await authRes.json();
    const { access_token } = authData;

    const paymentInput = {
      monetaryValue: {
        amount: amount.toFixed(2), // Ensure 2 decimal places
        currency: "IQD"
      },
      statusCallbackUrl: new URL(redirectUrl).toString(),
      description: `Vendor subscription purchase`,
      expiresIn: "PT47H5M12.345S",
      category: "POS",
      refundableFor: "PT24H"
    };

    // Create payment using direct API call
    const apiUrl = 'https://fib.dev.fib.iq/protected/v1/payments';
    const paymentRes = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${access_token}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      },
      body: JSON.stringify(paymentInput)
    });

    if (!paymentRes.ok) {
      const errorData = await paymentRes.json();
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `FIB payment creation failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
      );
    }

    const paymentData = await paymentRes.json();

    // Update the payment transaction with the FIB payment ID
    await PaymentTransaction.update(
      {
        metadata: {
          ...metadata,
          fibPaymentId: paymentData.paymentId
        }
      },
      {
        where: { id: metadata.paymentTransactionId }
      }
    );

    // Return the payment details
    return {
      paymentUrl: paymentData.personalAppLink, // Use the personal app link as the primary payment URL
      qrCode: paymentData.qrCode, // QR code for scanning
      readableCode: paymentData.readableCode, // Code for manual entry
      personalAppLink: paymentData.personalAppLink, // Deep link for personal FIB app
      businessAppLink: paymentData.businessAppLink, // Deep link for business FIB app
      corporateAppLink: paymentData.corporateAppLink, // Deep link for corporate FIB app
      validUntil: paymentData.validUntil, // Expiration time
      transactionId: paymentData.paymentId
    };
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      httpStatus.BAD_GATEWAY,
      `FIB payment initialization failed: ${error.message}`
    );
  }
};

/**
 * Verify a ZainCash payment callback for vendor subscription
 * @param {string} token - JWT token from ZainCash callback
 * @returns {Promise<Object>} - Verified payment data
 */
const verifyZaincashSubscriptionPayment = async (token) => {
  if (!token) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token is required');
  }

  try {
    // Verify and decode the JWT token
    const decoded = jwt.verify(token, config.zaincash.secret);

    // Extract the necessary data
    const { id, msisdn, operationid, orderid, status, msg } = decoded;

    if (!id || !orderid || !status) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid payment data');
    }

    // Get the payment transaction
    const paymentTransaction = await PaymentTransaction.findOne({
      where: {
        status: 'pending',
        referenceId: orderid
      }
    });

    if (!paymentTransaction) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Payment transaction not found');
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      if (status !== 'success') {
        // Update transaction for failed payment
        await paymentTransaction.update({
          status: 'failed',
          remarks: `ZainCash payment failed: ${msg || 'Unknown error'}`,
          metadata: {
            ...paymentTransaction.metadata,
            failureReason: msg,
          }
        }, { transaction });

        await transaction.commit();

        throw new ApiError(
          httpStatus.PAYMENT_REQUIRED,
          `Payment failed: ${msg || 'Unknown error'}`
        );
      }

      // Get the subscription
      const subscriptionId = paymentTransaction.metadata.subscriptionId;
      const subscription = await VendorSubscription.findByPk(subscriptionId);

      if (!subscription) {
        await transaction.rollback();
        throw new ApiError(httpStatus.NOT_FOUND, 'Subscription not found');
      }

      // Update the subscription to active
      await subscription.update({
        isActive: true
      }, { transaction });

      // Update the payment transaction
      await paymentTransaction.update({
        status: 'success',
        remarks: 'ZainCash payment successful',
        metadata: {
          ...paymentTransaction.metadata,
          paymentStatus: 'success',
          paidAt: new Date()
        }
      }, { transaction });

      await transaction.commit();

      return {
        success: true,
        subscriptionId,
        vendorId: subscription.vendorId,
        amount: paymentTransaction.amount,
        transactionId: operationid,
        orderId: orderid
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid payment token');
    }
    if (error.name === 'TokenExpiredError') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token expired');
    }
    throw error;
  }
};

/**
 * Verify a FIB payment callback for vendor subscription
 * @param {Object} callbackData - Callback data from FIB
 * @returns {Promise<Object>} - Verified payment data
 */
const verifyFibSubscriptionPayment = async (callbackData) => {
  if (!callbackData || !callbackData.paymentId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid callback data');
  }

  const { paymentId } = callbackData;
  const statusFromCallback = callbackData.status;

  try {
    // Get the payment transaction
    const paymentTransaction = await PaymentTransaction.findOne({
      where: {
        status: 'pending',
        metadata: {
          fibPaymentId: paymentId
        }
      }
    });

    if (!paymentTransaction) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Payment transaction not found');
    }

    // Start a transaction
    const transaction = await sequelize.transaction();

    try {
      let statusData;

      if (statusFromCallback) {
        statusData = statusFromCallback;
      } else {
        // Get FIB client from the existing service
        const FIB = require('@first-iraqi-bank/sdk/payment').PaymentSDK.getClient(
          process.env.FIB_CLIENT_ID || 'your_fib_client_id',
          process.env.FIB_CLIENT_SECRET || 'your_fib_client_secret',
          process.env.NODE_ENV === 'production' ? 'prod' : 'dev'
        );

        // Authenticate with FIB
        const authRes = await FIB.authenticate();

        if (!authRes.ok) {
          const errorData = await authRes.json();
          throw new ApiError(
            httpStatus.BAD_GATEWAY,
            `FIB authentication failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
          );
        }

        const authData = await authRes.json();
        const { access_token } = authData;

        // Get payment status from FIB
        const statusRes = await FIB.getPaymentStatus(paymentId, access_token);

        if (!statusRes.ok) {
          const errorData = await statusRes.json();
          throw new ApiError(
            httpStatus.BAD_GATEWAY,
            `FIB payment status check failed: ${errorData.errors?.[0]?.message || 'Unknown error'}`
          );
        }

        statusData = await statusRes.json();
      }

      // Check payment status
      if (statusData.status !== 'PAID') {
        // Update transaction for failed payment
        await paymentTransaction.update({
          status: 'failed',
          remarks: `FIB payment failed: ${statusData.decliningReason || 'Unknown reason'}`,
          metadata: {
            ...paymentTransaction.metadata,
            paymentStatus: statusData.status,
            decliningReason: statusData.decliningReason,
            declinedAt: statusData.declinedAt
          }
        }, { transaction });

        await transaction.commit();

        throw new ApiError(
          httpStatus.PAYMENT_REQUIRED,
          `Payment failed: ${statusData.decliningReason || 'Unknown reason'}`
        );
      }

      // Get the subscription
      const subscriptionId = paymentTransaction.metadata.subscriptionId;
      const subscription = await VendorSubscription.findByPk(subscriptionId);

      if (!subscription) {
        await transaction.rollback();
        throw new ApiError(httpStatus.NOT_FOUND, 'Subscription not found');
      }

      // Update the subscription to active
      await subscription.update({
        isActive: true
      }, { transaction });

      // Update the payment transaction
      await paymentTransaction.update({
        status: 'success',
        remarks: 'FIB payment successful',
        metadata: {
          ...paymentTransaction.metadata,
          paymentStatus: 'success',
          paidAt: statusData.paidAt,
          paidBy: statusData.paidBy
        }
      }, { transaction });

      await transaction.commit();

      return {
        success: true,
        subscriptionId,
        vendorId: subscription.vendorId,
        amount: paymentTransaction.amount,
        transactionId: paymentId
      };
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    throw new ApiError(
      httpStatus.INTERNAL_SERVER_ERROR,
      `Failed to verify FIB payment: ${error.message}`
    );
  }
};

/**
 * Get vendor subscription by ID
 * @param {number} subscriptionId - Subscription ID
 * @param {number} userId - User ID for authorization
 * @returns {Promise<VendorSubscription>}
 */
const getSubscriptionById = async (subscriptionId, userId) => {
  const subscription = await VendorSubscription.findByPk(subscriptionId, {
    include: [
      {
        model: Vendor,
        attributes: ['id', 'businessName', 'email', 'phone', 'userId']
      },
      {
        model: SubscriptionPlan,
        attributes: ['id', 'name', 'description', 'durationValue', 'durationType', 'price', 'currency', 'benefits']
      }
    ]
  });

  if (!subscription) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription not found');
  }

  // Check if user is authorized to view this subscription
  if (subscription.Vendor.userId !== userId) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this subscription');
  }

  return subscription;
};

/**
 * Get active subscription for a vendor
 * @param {number} vendorId - Vendor ID
 * @param {number} userId - User ID for authorization
 * @returns {Promise<VendorSubscription|null>}
 */
const getActiveSubscription = async (vendorId, userId) => {
  // Check if user is authorized to view this vendor's subscriptions
  const vendor = await Vendor.findOne({ where: { id: vendorId, userId } });
  if (!vendor) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to view this vendor\'s subscriptions');
  }

  const subscription = await VendorSubscription.findOne({
    where: { vendorId, isActive: true },
    include: [
      {
        model: SubscriptionPlan,
        attributes: ['id', 'name', 'description', 'durationValue', 'durationType', 'price', 'currency', 'benefits']
      }
    ]
  });

  return subscription;
};

/**
 * Cancel a vendor subscription
 * @param {number} subscriptionId - Subscription ID
 * @param {number} userId - User ID for authorization
 * @returns {Promise<VendorSubscription>}
 */
const cancelSubscription = async (subscriptionId, userId) => {
  const subscription = await VendorSubscription.findByPk(subscriptionId, {
    include: [
      {
        model: Vendor,
        attributes: ['id', 'userId']
      }
    ]
  });

  if (!subscription) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Subscription not found');
  }

  // Check if user is authorized to cancel this subscription
  if (subscription.Vendor.userId !== userId) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to cancel this subscription');
  }

  if (!subscription.isActive) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Subscription is not active');
  }

  await subscription.update({
    isActive: false,
    autoRenew: false
  });

  return subscription;
};

module.exports = {
  initializeSubscriptionPayment,
  verifyZaincashSubscriptionPayment,
  verifyFibSubscriptionPayment,
  getSubscriptionById,
  getActiveSubscription,
  cancelSubscription
};
