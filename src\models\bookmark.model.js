const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Bookmark = sequelize.define(
    'bookmarks',
    {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true,
        },
        userId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users', // Reference the Users table
                key: 'id',
            },
        },
        stationId: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'stations', // Reference the Stations table
                key: 'id',
            },
        },
    },
    {
        tableName: 'bookmarks',
        timestamps: true
    }
);

module.exports = Bookmark;
