const Joi = require('joi');
const moment = require('moment');

const getUsers = {
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be greater than or equal to 1'
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be greater than or equal to 1',
        'number.max': 'Limit cannot exceed 100'
      }),
    sortBy: Joi.string()
      .optional()
      .default('createdAt:desc')
      .custom((value, helpers) => {
        // Validate sort format: field:direction,field2:direction2
        const sortPattern = /^[\w]+(:(asc|desc))?(,[\w]+(:(asc|desc))?)*$/i;
        if (!sortPattern.test(value)) {
          return helpers.error('string.sortFormat');
        }

        // Validate allowed fields
        const allowedFields = [
          'id',
          'name',
          'isActive',
          'createdAt',
          'updatedAt'
        ];

        const sortParts = value.split(',');
        for (const part of sortParts) {
          const [field] = part.split(':');
          if (!allowedFields.includes(field)) {
            return helpers.error('string.sortField', { field });
          }
        }

        return value;
      })
      .messages({
        'string.sortFormat': 'Invalid sort format. Example: field:asc,field2:desc',
        'string.sortField': '{{#field}} is not a valid sort field'
      }),
    name: Joi.string()
      .trim()
      .optional()
      .max(50)
      .messages({
        'string.max': 'Name filter cannot exceed 50 characters'
      }),
    email: Joi.string()
      .trim()
      .optional()
      .max(100)
      .email()
      .messages({
        'string.email': 'Email must be a valid email address',
        'string.max': 'Email filter cannot exceed 100 characters'
      }),
    phone: Joi.string()
      .trim()
      .optional()
      .max(20)
      .messages({
        'string.max': 'Phone filter cannot exceed 20 characters'
      }),
    isActive: Joi.boolean()
      .optional()
      .messages({
        'boolean.base': 'isActive must be a boolean'
      }),
    fromDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'From date must be in YYYY-MM-DD format'
      }),
    toDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        // Check if toDate is after fromDate
        const fromDate = helpers.state.ancestors[0].fromDate;
        if (fromDate && moment(value).isBefore(moment(fromDate))) {
          return helpers.error('string.dateRange');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'To date must be in YYYY-MM-DD format',
        'string.dateRange': 'To date must be after from date'
      })
  })
};

const toggleUserStatus = {
  params: Joi.object().keys({
    userId: Joi.string().required().description('User ID'),
  }),
  body: Joi.object().keys({
  isActive: Joi.boolean().required().description('User active status'),
  suspendedReason: Joi.string().when('isActive', {
    is: false,
    then: Joi.required().description('Reason for suspension'),
    otherwise: Joi.optional().description('Reason for suspension (optional when active)'),
  }),
  suspendedComment: Joi.string().when('isActive', {
    is: false,
    then: Joi.required().description('Additional comment for suspension'),
    otherwise: Joi.optional().description('Additional comment for suspension (optional when active)'),
  }),
}),
};


const deleteUser = {
  params: Joi.object().keys({
    userId: Joi.string().required().description('User ID'),
  }),
};

const createUser = {
  body: Joi.object().keys({
    email: Joi.string().required().email(),
    password: Joi.string().required().min(6),
    name: Joi.string().required(),
    phone: Joi.string().optional(),
    dob: Joi.date().optional(),
    // Additional optional fields
    gender: Joi.string().valid('male', 'female', 'other').optional(),
    country: Joi.string().optional(),
  }),
};

const resetPassword = {
  body: Joi.object().keys({
    userId: Joi.number().required(),
    email: Joi.string().required().email(),
    oldPassword: Joi.string().required(),
    newPassword: Joi.string().required(),
  }),
};
const getUserById = {
  params: Joi.object().keys({
    userId:Joi.number().required(),
  }),
};

const getUserBookings = {
  params: Joi.object().keys({
    userId: Joi.number().required().description('User ID'),
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    status: Joi.string().valid('completed', 'cancelled', 'pending').optional(),
    fromDate: Joi.string().optional(),
    toDate: Joi.string().optional(),
    region: Joi.string().optional().description('Filter by vehicle manufacturing unit region'),
    manufacturingUnitName: Joi.string().optional().description('Filter by manufacturing unit name'),
    minPrice: Joi.number().min(0).optional().description('Minimum booking amount'),
  maxPrice: Joi.number().min(0).optional().description('Maximum booking amount')
    .custom((value, helpers) => {
      const minPrice = helpers.state.ancestors[0].minPrice;
      if (minPrice !== undefined && value < minPrice) {
        return helpers.error('number.minPrice');
      }
      return value;
    })
    .messages({
      'number.minPrice': 'Maximum price must be greater than or equal to minimum price'
    }),
}).custom((obj, helpers) => {
  const { minPrice, maxPrice } = obj;

  // If either minPrice or maxPrice is present, both must be present
  if ((minPrice !== undefined && maxPrice === undefined) || (minPrice === undefined && maxPrice !== undefined)) {
    return helpers.error('object.bothRequired');
  }

  // If both are present, validate the range
  if (minPrice !== undefined && maxPrice !== undefined && maxPrice < minPrice) {
    return helpers.error('object.priceRange');
  }

  return obj;
}).messages({
  'object.bothRequired': 'Both minPrice and maxPrice must be provided if one is present',
  'object.priceRange': 'Maximum price must be greater than or equal to minimum price',
})
};

const getBookingDetails = {
  params: Joi.object().keys({
    bookingId: Joi.number().required().description('Booking ID'),
  }),
};
module.exports = {
  getUsers,
  toggleUserStatus,
  deleteUser,
  createUser,
  resetPassword,
  getUserById,
  getUserBookings,
  getBookingDetails
};
