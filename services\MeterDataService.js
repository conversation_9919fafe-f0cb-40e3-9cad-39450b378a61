const { MeterValue, ChargingSession, Charger } = require('../models');
const { Op } = require('sequelize');

class MeterDataService {
    constructor() {
        this.meterValueBuffer = new Map(); // Buffer for batch processing
        this.bufferSize = 100; // Process in batches of 100
        this.flushInterval = 5000; // Flush every 5 seconds
        this.startBufferFlush();
    }

    /**
     * Process and store meter values from OCPP message
     * @param {Object} payload - OCPP MeterValues payload
     * @param {number} sessionId - Charging session ID
     * @param {number} chargerId - Charger ID
     * @returns {Object} Processing result
     */
    async processMeterValues(payload, sessionId, chargerId) {
        try {
            const { connectorId, transactionId, meterValue } = payload;
            const processedValues = [];

            for (const mv of meterValue) {
                const timestamp = new Date(mv.timestamp);

                for (const sample of mv.sampledValue) {
                    const meterValueData = {
                        sessionId,
                        chargerId,
                        connectorId,
                        transactionId,
                        timestamp,
                        measurand: sample.measurand || 'Energy.Active.Import.Register',
                        value: parseFloat(sample.value),
                        unit: sample.unit || this.getDefaultUnit(sample.measurand),
                        phase: sample.phase || null,
                        location: sample.location || null,
                        context: sample.context || 'Sample.Periodic'
                    };

                    // Validate the meter value
                    if (this.validateMeterValue(meterValueData)) {
                        processedValues.push(meterValueData);
                        
                        // Add to buffer for batch processing
                        this.addToBuffer(meterValueData);
                    } else {
                        console.warn('Invalid meter value:', meterValueData);
                    }
                }
            }

            // Update session last activity
            if (sessionId) {
                await this.updateSessionActivity(sessionId);
            }

            return {
                success: true,
                processedCount: processedValues.length,
                timestamp: new Date()
            };

        } catch (error) {
            console.error('Error processing meter values:', error);
            throw error;
        }
    }

    /**
     * Store meter values directly (for immediate processing)
     * @param {Array} meterValues - Array of meter value objects
     * @returns {Object} Storage result
     */
    async storeMeterValues(meterValues) {
        try {
            // Step 1: Filter only Wh values
            const whValues = meterValues.filter(mv => 
                mv.measurand === 'Energy.Active.Import.Register' && 
                this.validateMeterValue(mv)
            );
            
            // Step 2: Check if we have any valid Wh values
            if (whValues.length === 0) {
                return { success: true, storedCount: 0 };
            }

            // Step 3: Store only the first Wh value
            const result = await MeterValue.create(whValues[0]);

            // Step 4: Log the stored value
            console.log(`Stored Wh meter value: ${whValues[0].value} Wh`);

            // Step 5: Return success
            return {
                success: true,
                storedCount: 1
            };

        } catch (error) {
            console.error('Error storing meter value:', error);
            throw error;
        }
    }

    /**
     * Get meter values for a session
     * @param {number} sessionId - Session ID
     * @param {string} measurand - Optional filter by measurand
     * @param {Date} startTime - Optional start time filter
     * @param {Date} endTime - Optional end time filter
     * @returns {Array} Meter values
     */
    async getSessionMeterValues(sessionId, measurand = null, startTime = null, endTime = null) {
        try {
            const whereClause = { sessionId };

            if (measurand) {
                whereClause.measurand = measurand;
            }

            if (startTime || endTime) {
                whereClause.timestamp = {};
                if (startTime) whereClause.timestamp[Op.gte] = startTime;
                if (endTime) whereClause.timestamp[Op.lte] = endTime;
            }

            const meterValues = await MeterValue.findAll({
                where: whereClause,
                order: [['timestamp', 'ASC']],
                attributes: ['timestamp', 'measurand', 'value', 'unit', 'phase', 'location', 'context']
            });

            return meterValues;

        } catch (error) {
            console.error('Error getting session meter values:', error);
            throw error;
        }
    }

    /**
     * Get latest meter values for a charger
     * @param {number} chargerId - Charger ID
     * @param {number} connectorId - Optional connector filter
     * @returns {Object} Latest meter values by measurand
     */
    async getLatestMeterValues(chargerId, connectorId = null) {
        try {
            const whereClause = { chargerId };
            if (connectorId) {
                whereClause.connectorId = connectorId;
            }

            // Get the latest value for each measurand
            const latestValues = await MeterValue.findAll({
                where: whereClause,
                attributes: [
                    'measurand',
                    'value',
                    'unit',
                    'phase',
                    'timestamp',
                    [MeterValue.sequelize.fn('MAX', MeterValue.sequelize.col('timestamp')), 'maxTimestamp']
                ],
                group: ['measurand', 'phase'],
                order: [['timestamp', 'DESC']]
            });

            // Format the result
            const result = {};
            latestValues.forEach(mv => {
                const key = mv.phase ? `${mv.measurand}_${mv.phase}` : mv.measurand;
                result[key] = {
                    value: mv.value,
                    unit: mv.unit,
                    timestamp: mv.timestamp,
                    phase: mv.phase
                };
            });

            return result;

        } catch (error) {
            console.error('Error getting latest meter values:', error);
            throw error;
        }
    }

    /**
     * Calculate energy consumption for a session
     * @param {number} sessionId - Session ID
     * @returns {Object} Energy consumption data
     */
    async calculateEnergyConsumption(sessionId) {
        try {
            const energyValues = await MeterValue.findAll({
                where: {
                    sessionId,
                    measurand: 'Energy.Active.Import.Register'
                },
                order: [['timestamp', 'ASC']],
                attributes: ['value', 'timestamp']
            });

            if (energyValues.length < 2) {
                return {
                    startValue: energyValues[0]?.value || 0,
                    endValue: energyValues[0]?.value || 0,
                    consumption: 0,
                    unit: 'Wh'
                };
            }

            const startValue = energyValues[0].value;
            const endValue = energyValues[energyValues.length - 1].value;
            const consumption = endValue - startValue;

            return {
                startValue,
                endValue,
                consumption,
                unit: 'Wh',
                dataPoints: energyValues.length
            };

        } catch (error) {
            console.error('Error calculating energy consumption:', error);
            throw error;
        }
    }

    /**
     * Get power consumption trend
     * @param {number} sessionId - Session ID
     * @param {number} intervalMinutes - Interval for aggregation
     * @returns {Array} Power trend data
     */
    async getPowerTrend(sessionId, intervalMinutes = 5) {
        try {
            const powerValues = await MeterValue.findAll({
                where: {
                    sessionId,
                    measurand: 'Power.Active.Import'
                },
                order: [['timestamp', 'ASC']],
                attributes: ['value', 'timestamp', 'phase']
            });

            // Group by time intervals
            const intervalMs = intervalMinutes * 60 * 1000;
            const trends = {};

            powerValues.forEach(pv => {
                const intervalStart = new Date(Math.floor(pv.timestamp.getTime() / intervalMs) * intervalMs);
                const key = intervalStart.toISOString();
                
                if (!trends[key]) {
                    trends[key] = {
                        timestamp: intervalStart,
                        totalPower: 0,
                        phaseCount: 0,
                        phases: {}
                    };
                }

                trends[key].totalPower += pv.value;
                trends[key].phaseCount++;
                
                if (pv.phase) {
                    trends[key].phases[pv.phase] = pv.value;
                }
            });

            // Convert to array and calculate averages
            return Object.values(trends).map(trend => ({
                timestamp: trend.timestamp,
                averagePower: trend.totalPower / trend.phaseCount,
                totalPower: trend.totalPower,
                phases: trend.phases
            }));

        } catch (error) {
            console.error('Error getting power trend:', error);
            throw error;
        }
    }

    /**
     * Validate meter value data
     * @param {Object} meterValue - Meter value object
     * @returns {boolean} Is valid
     */
    validateMeterValue(meterValue) {
        const required = ['sessionId', 'chargerId', 'connectorId', 'timestamp', 'measurand', 'value', 'unit'];
        
        for (const field of required) {
            if (meterValue[field] === undefined || meterValue[field] === null) {
                return false;
            }
        }

        // Validate value is a number
        if (isNaN(parseFloat(meterValue.value))) {
            return false;
        }

        // Validate timestamp
        if (!(meterValue.timestamp instanceof Date) || isNaN(meterValue.timestamp.getTime())) {
            return false;
        }

        return true;
    }

    /**
     * Get default unit for measurand
     * @param {string} measurand - Measurand type
     * @returns {string} Default unit
     */
    getDefaultUnit(measurand) {
        const defaults = {
            "Energy.Active.Import.Register": "Wh",
            "Energy.Active.Export.Register": "Wh",
            "Energy.Reactive.Import.Register": "varh",
            "Energy.Reactive.Export.Register": "varh",
            "Power.Active.Import": "W",
            "Power.Active.Export": "W",
            "Power.Reactive.Import": "var",
            "Power.Reactive.Export": "var",
            "Current.Import": "A",
            "Current.Export": "A",
            "Voltage": "V",
            "Temperature": "Celsius",
            "SoC": "%",
            "Frequency": "Hz"
        };
        return defaults[measurand] || "Unknown";
    }

    /**
     * Add meter value to buffer for batch processing
     * @param {Object} meterValue - Meter value object
     */
    addToBuffer(meterValue) {
        const bufferId = `${meterValue.chargerId}_${meterValue.connectorId}`;
        
        if (!this.meterValueBuffer.has(bufferId)) {
            this.meterValueBuffer.set(bufferId, []);
        }

        this.meterValueBuffer.get(bufferId).push(meterValue);

        // Flush if buffer is full
        if (this.meterValueBuffer.get(bufferId).length >= this.bufferSize) {
            this.flushBuffer(bufferId);
        }
    }

    /**
     * Flush buffer for a specific charger/connector
     * @param {string} bufferId - Buffer ID
     */
    async flushBuffer(bufferId) {
        const values = this.meterValueBuffer.get(bufferId);
        if (!values || values.length === 0) return;

        try {
            await this.storeMeterValues(values);
            this.meterValueBuffer.set(bufferId, []); // Clear buffer
        } catch (error) {
            console.error(`Error flushing buffer ${bufferId}:`, error);
        }
    }

    /**
     * Start periodic buffer flush
     */
    startBufferFlush() {
        setInterval(() => {
            for (const bufferId of this.meterValueBuffer.keys()) {
                this.flushBuffer(bufferId);
            }
        }, this.flushInterval);
    }

    /**
     * Update session last activity
     * @param {number} sessionId - Session ID
     */
    async updateSessionActivity(sessionId) {
        try {
            await ChargingSession.update(
                { lastActivity: new Date() },
                { where: { id: sessionId } }
            );
        } catch (error) {
            console.error('Error updating session activity:', error);
        }
    }

    /**
     * Get meter value statistics for a time period
     * @param {number} chargerId - Charger ID
     * @param {Date} startTime - Start time
     * @param {Date} endTime - End time
     * @returns {Object} Statistics
     */
    async getMeterValueStatistics(chargerId, startTime, endTime) {
        try {
            const stats = await MeterValue.findAll({
                where: {
                    chargerId,
                    timestamp: {
                        [Op.between]: [startTime, endTime]
                    }
                },
                attributes: [
                    'measurand',
                    [MeterValue.sequelize.fn('COUNT', MeterValue.sequelize.col('id')), 'count'],
                    [MeterValue.sequelize.fn('AVG', MeterValue.sequelize.col('value')), 'average'],
                    [MeterValue.sequelize.fn('MIN', MeterValue.sequelize.col('value')), 'minimum'],
                    [MeterValue.sequelize.fn('MAX', MeterValue.sequelize.col('value')), 'maximum']
                ],
                group: ['measurand']
            });

            return stats.reduce((acc, stat) => {
                acc[stat.measurand] = {
                    count: parseInt(stat.dataValues.count),
                    average: parseFloat(stat.dataValues.average),
                    minimum: parseFloat(stat.dataValues.minimum),
                    maximum: parseFloat(stat.dataValues.maximum)
                };
                return acc;
            }, {});

        } catch (error) {
            console.error('Error getting meter value statistics:', error);
            throw error;
        }
    }
}

module.exports = MeterDataService;
