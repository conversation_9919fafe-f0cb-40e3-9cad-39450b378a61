'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Seed connectors
    await queryInterface.bulkInsert('connectors', [
      { name: 'Type 1', type: 'american' },
      { name: 'Type 2', type: 'european' },
      { name: 'GB/T', type: 'chinese' },
      { name: 'CCS1', type: 'american' },
      { name: 'CCS2', type: 'european' },
      { name: 'CHAdeMO', type: 'japanese' }
    ]);

    // Seed powers
    await queryInterface.bulkInsert('powers', [
      { value: 3.3, unit: 'kW' },
      { value: 7.4, unit: 'kW' },
      { value: 11, unit: 'kW' },
      { value: 22, unit: 'kW' },
      { value: 43, unit: 'kW' },
      { value: 50, unit: 'kW' },
      { value: 150, unit: 'kW' },
      { value: 350, unit: 'kW' }
    ]);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('powers', null, {});
    await queryInterface.bulkDelete('connectors', null, {});
  }
}; 