const {UserVehicle, Station, Charger, Connector, Power, ManufacturingUnit} = require('../models');
const {REGION_CONNECTOR_MAP} = require('../constants/station');
const logger = require('../config/logger');

const getStationChargerCompatibility = async (vehicleId, stationId) => {
  try {
    logger.info(`[Compatibility] Checking compatibility for vehicleId=${vehicleId}, stationId=${stationId}`);

    const userVehicle = await UserVehicle.findByPk(vehicleId, {
      attributes: [],
      include: [
        {
          model: ManufacturingUnit,
          attributes: ['location'],
          required: true,
        },
      ],
    });

    if (!userVehicle || !userVehicle.ManufacturingUnit) {
      logger.warn(`[Compatibility] Manufacturing info not found for vehicleId=${vehicleId}`);
      throw new Error('Vehicle manufacturing information not found');
    }

    const region = userVehicle.ManufacturingUnit.location;
    const compatibleTypes = REGION_CONNECTOR_MAP[region] || [];

    logger.info(`[Compatibility] Vehicle region=${region}`);
    logger.info(`[Compatibility] Compatible connector types=${compatibleTypes.join(', ') || 'None'}`);

    if (compatibleTypes.length === 0) {
      return {
        stationId,
        isCompatible: false,
        message: `No compatible connector types defined for ${region} region`,
        vehicleRegion: region,
      };
    }

    const station = await Station.findByPk(stationId, {
      attributes: ['id', 'stationName'],
      include: [
        {
          model: Charger,
          attributes: ['id', 'pricePerHour'],
          include: [
            {
              model: Connector,
              attributes: ['id', 'name', 'type'],
              where: {type: compatibleTypes},
            },
            {
              model: Power,
              attributes: ['value', 'unit'],
            },
          ],
        },
      ],
    });

    if (!station) {
      logger.warn(`[Compatibility] Station not found with stationId=${stationId}`);
      throw new Error('Station not found');
    }

    if (!station.chargers || station.chargers.length === 0) {
      logger.info(`[Compatibility] No compatible chargers at stationId=${station.id} for region=${region}`);
      return {
        stationId: station.id,
        stationName: station.stationName,
        isCompatible: false,
        message: `No compatible chargers found for ${region} region vehicles`,
        vehicleRegion: region,
      };
    }

    const compatibleChargers = station.chargers.map(charger => ({
      chargerId: charger.id,
      connectorName: charger.connector.name,
      connectorType: charger.connector.type,
      power: {
        value: charger.power?.value,
        unit: charger.power?.unit,
      },
      pricePerHour: charger.pricePerHour,
    }));

    logger.info(`[Compatibility] Found ${compatibleChargers.length} compatible charger(s) at stationId=${station.id}`);

    return {
      stationId: station.id,
      stationName: station.stationName,
      isCompatible: true,
      message: 'Compatible chargers found',
      vehicleRegion: region,
      compatibleChargers,
    };
  } catch (error) {
    logger.error(`[Compatibility] Error for vehicleId=${vehicleId}, stationId=${stationId} → ${error.message}`);
    throw error;
  }
};

module.exports = {
  getStationChargerCompatibility,
};
