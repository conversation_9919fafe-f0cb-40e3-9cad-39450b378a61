# EV.IQ Backend Coding Standards and Project Structure

## Project Structure

```
src/
├── config/         # Configuration files
├── constants/      # Application constants
├── controllers/    # Route controllers
├── middlewares/    # Custom middleware functions
├── models/         # Database models
├── routes/         # API routes
├── services/       # Business logic
├── utils/          # Utility functions
├── validations/    # Request validation schemas
├── microservices/  # External service integrations
├── app.js          # Express app configuration
└── index.js        # Application entry point
```

## Coding Standards

### 1. File Naming Conventions
- Use kebab-case for file names: `user.service.js`, `auth.middleware.js`
- Use kebab-case for model files: `user.model.js`, `product.model.js`
- Use camelCase for variable and function names
- Use UPPER_SNAKE_CASE for constants

### 2. Code Organization

#### Services (`src/services/`)
- Handle business logic
- Should be stateless
- Follow single responsibility principle
- Example structure:
```javascript
const serviceName = async (params) => {
    // Input validation
    // Business logic
    // Database operations
    // Return results
};
```

#### Controllers (`src/controllers/`)
- Handle HTTP requests and responses
- Should be thin and delegate to services
- Example structure:
```javascript
const controllerName = async (req, res, next) => {
    try {
        const result = await serviceName(req.body);
        res.status(httpStatus.OK).send(result);
    } catch (error) {
        next(error);
    }
};
```

#### Models (`src/models/`)
- Define database schemas
- Include model associations
- Example structure:
```javascript
const { Model, DataTypes } = require('sequelize');

class ModelName extends Model {
    static init(sequelize) {
        super.init({
            // schema definition
        }, {
            sequelize,
            modelName: 'ModelName'
        });
    }
}
```

### 3. Error Handling

#### Custom Error Class
```javascript
const ApiError = require('../utils/ApiError');

// Usage
throw new ApiError(httpStatus.BAD_REQUEST, 'Error message');
```

#### Error Handling Pattern
```javascript
try {
    // Operation
} catch (error) {
    // Log error
    console.error(error);
    // Throw custom error
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Error message');
}
```

### 4. File Upload Standards

#### S3 Upload Pattern
```javascript
const [uploadedFile] = await fileUploadService.s3Upload([file], 'bucketName');
```

#### File Cleanup Pattern
```javascript
if (oldFileKey) {
    await fileUploadService.s3Delete(oldFileKey)
        .catch(err => console.error('Failed to delete file:', err));
}
```

### 5. Validation Standards

#### Input Validation
- Use validation schemas in `validations/` directory
- Validate all user inputs
- Example:
```javascript
const addProduct = {
  body: Joi.object().keys({
    name: Joi.string().trim().required(),
    description: Joi.string().trim().required(),
    type: Joi.string().custom(objectId),
    benefits: Joi.array().items(Joi.string().trim()),
    testimonials: Joi.array().items(Joi.object({
      user: Joi.string().custom(objectId),
      title: Joi.string().trim().required(),
      description: Joi.string().trim().required(),
    })),
  }),
};
```

### 6. Database Operations

#### Query Pattern
```javascript
const result = await Model.findByPk(id);
if (!result) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Resource not found');
}
```

### 7. Response Format

#### Success Response
```javascript
res.status(httpStatus.OK).send({
    status: true,
    data: result
});
```

#### Error Response
```javascript
    throw new ApiError(httpStatus.NOT_FOUND, 'Error Msg');
```

### 8. Code Documentation

#### Function Documentation
```javascript
/**
 * Function description
 * @param {string} param1 - Parameter description
 * @param {Object} param2 - Parameter description
 * @returns {Promise<Object>} Return description
 * @throws {ApiError} Error description
 */
```

### 9. Best Practices

1. **Async/Await**
   - Use async/await instead of callbacks
   - Always use try/catch blocks
   - Avoid nested promises

2. **Error Handling**
   - Use custom ApiError class
   - Log errors appropriately
   - Handle cleanup operations even if they fail

3. **Security**
   - Validate all inputs
   - Sanitize user data
   - Use proper authentication middleware
   - Implement rate limiting

4. **Performance**
   - Use appropriate database indexes
   - Implement caching where necessary
   - Optimize database queries

5. **Testing**
   - Write unit tests for services
   - Write integration tests for API endpoints
   - Mock external services in tests

### 10. Version Control

1. **Branch Naming**
   - feature/feature-name
   - bugfix/bug-description
   - hotfix/issue-description

2. **Commit Messages**
   - Use present tense
   - Be descriptive
   - Reference issue numbers

3. **Code Review**
   - Review for security issues
   - Check error handling
   - Verify input validation
   - Ensure proper documentation 