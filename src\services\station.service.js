const moment = require('moment');
const { sequelize } = require('../config/database');
const logger = require('../config/logger');
const Station = require('../models/station.model');
const ApiError = require('../utils/ApiError');
const httpStatus = require('http-status');
const Vendor = require('../models/vendor.model');
const StationAmenity = require('../models/stationAmenity.model');
const Amenity = require('../models/amenity.model');
const uploadAllOrRollback = require('../utils/fileUpload');
const Connector = require('../models/connector.model');
const Power = require('../models/power.model');
const Booking = require('../models/booking.model');
const Charger = require('../models/charger.model');
const UserVehicle = require('../models/userVehicle.model');
const ManufacturingUnit = require('../models/vehicleManufacturing.model');
const { Op, Sequelize } = require('sequelize');
const StationBlock = require('../models/stationBlock.model');
const { buildWhereClause, buildIncludeClause } = require('../utils/stationQueryBuilder');
const User = require('../models/user.model');
const Review = require('../models/review.model');
const {getStationChargerCompatibility} = require('../utils/station');
const Bookmark = require('../models/bookmark.model');
const { REGION_CONNECTOR_MAP } = require('../constants/station');

const addStation = async ({
  city,
  pinCode,
  state,
  address,
  latitude,
  longitude,
  operationalSchedule,
  userId,
  amenities,
  stationName,
  files,
  phoneNumber
}) => {
  const t = await sequelize.transaction();

  logger.info('Starting station creation transaction', {
    userId,
    stationName,
    hasFiles: !!files?.length
  });

  try {
    const vendor = await Vendor.findOne({ where: { userId }, transaction: t });
    if (!vendor) {
      logger.warn('Vendor not found during station creation', { userId });
      throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }

    if (!vendor.isKycApproved) {
      logger.warn('Attempted to create station with unapproved KYC', {
        userId,
        vendorId: vendor.id
      });
      throw new ApiError(httpStatus.BAD_REQUEST, 'Vendor KYC is not approved');
    }

    // Validate Amenities
    const validAmenities = await Amenity.findAll({
      where: { id: amenities },
      attributes: ['id'],
      raw: true,
      transaction: t,
    });

    if (validAmenities.length !== amenities.length) {
      logger.warn('Invalid amenities provided', {
        providedAmenities: amenities,
        validAmenities: validAmenities.map(a => a.id)
      });
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid amenity ID(s) provided');
    }

    // Upload Images
    let uploadedImages = [];
    if (files?.length > 0) {
      logger.debug('Uploading station images', { fileCount: files.length });
      uploadedImages = await uploadAllOrRollback(files, 'stationImages');
    }

    const newStation = await Station.create(
      {
        city,
        pincode: pinCode,
        state,
        address,
        latitude,
        longitude,
        operationalSchedule,
        images: uploadedImages,
        vendorId: vendor.id,
        stationName,
        phoneNumber
      },
      { transaction: t }
    );

    logger.debug('Station created, adding amenities', {
      stationId: newStation.id,
      amenityCount: validAmenities.length
    });

    const stationAmenities = validAmenities.map(amenity => ({
      stationId: newStation.id,
      amenityId: amenity.id,
    }));

    await StationAmenity.bulkCreate(stationAmenities, { transaction: t });

    await t.commit();
    logger.info('Station creation completed successfully', {
      stationId: newStation.id,
      vendorId: vendor.id,
      userId
    });

    return newStation;
  } catch (error) {
    await t.rollback();
    logger.error('Error in station creation', {
      userId,
      stationName,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

/**
 * Fetch charging stations based on filters
 */

const getStations = async (filters = {}) => {
  const {
    latitude,
    longitude,
    useCurrentLocation,
    stationName,
    chargingTypes,
    stationAmenities,
    vehicleId,
    page,
    limit
  } = filters;


      const userVehicle = await UserVehicle.findByPk(vehicleId, {
        attributes: [],
        include: [
          {
            model: ManufacturingUnit,
            attributes: ['location'],
            required: true,
          },
        ],
      });

    if (!userVehicle || !userVehicle.ManufacturingUnit) {
      logger.warn(`[Compatibility] Manufacturing info not found for vehicleId=${vehicleId}`);
      throw new ApiError(httpStatus.NOT_FOUND, 'Vehicle manufacturing information not found');
    }

    let vehicleRegion = userVehicle.ManufacturingUnit.location;
   let compatibleConnectorTypes = REGION_CONNECTOR_MAP[vehicleRegion] || [];
    
    logger.info(`[Compatibility] Vehicle region=${vehicleRegion}, compatible types=${compatibleConnectorTypes.join(', ') || 'None'}`);
    
    if (compatibleConnectorTypes.length === 0) {
      // Return empty result if no compatible connector types
      return {
        stations: [],
        pagination: {
          totalItems: 0,
          totalPages: 0,
          currentPage: page,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPreviousPage: page > 1
        }
      };
    }
 

  const offset = (page - 1) * limit;
  
  // Build where and include clauses
  const whereClause = buildWhereClause({ 
    latitude, 
    longitude, 
    useCurrentLocation, 
    stationName, 
    chargingTypes, 
    stationAmenities 
  });
  
  // Always use compatible connector types from the vehicle's region
  const includeClause = buildIncludeClause({ 
    chargingTypes, 
    connectorTypes: compatibleConnectorTypes, 
    stationAmenities 
  });

  // Get stations with pagination directly from database
  const { rows: stations, count: totalCount } = await Station.findAndCountAll({
    where: whereClause,
    include: includeClause,
    attributes: ["id", "stationName", "address", "city", "state", "latitude", "longitude", "pincode", "operationalSchedule", "images", "totalReviews", "averageRating"],
    limit,
    offset,
    distinct: true,
    subQuery: false // This can help with complex queries
  });

  // Add compatibility details to all stations
  if (stations.length > 0) {
    for (const station of stations) {
      station.dataValues.compatibilityDetails = {
        isCompatible: true,
        vehicleRegion,
        compatibleConnectorTypes
      };
    }
  }

  const totalPages = Math.ceil(totalCount / limit);

  return {
    stations,
    pagination: {
      totalItems: totalCount,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  };

}

const getStationById = async (stationId, userId) => {
  // Prepare the base station query
  const stationQuery = Station.findByPk(stationId, {
    include: [
      {
        model: Charger,
        attributes: ["id", "connectorId", "powerId", "pricePerHour"],
        include: [
          { 
            model: Connector, 
            attributes: ["id", "name", "type"] 
          },
          { 
            model: Power, 
            attributes: ["id", "value", "unit"] 
          }
        ]
      },
      {
        model: Amenity,
        as: "amenities",
        attributes: ["id", "name"],
        through: { attributes: [] }
      }
    ],
    attributes: {
      exclude: ["reason", "totalRatingSum"]
    }
  });

  // Optimized bookmark check using findOne with attributes: ['id']
  const bookmarkQuery = Bookmark.findOne({
    where: { userId, stationId },
    attributes: ['id'],
    raw: true
  }).then(bookmark => !!bookmark);

  const [station, isBookmarked] = await Promise.all([
    stationQuery,
    bookmarkQuery
  ]);

  if (!station) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
  }

  // Convert station to plain object and ensure amenities is an array
  const plainStation = station.toJSON();

  return { 
    ...plainStation,
    isBookmarked 
  };
};

const toggleStationStatus = async (stationId, userId, isEnabled, reason) => {
  logger.info('Attempting to toggle station status', {
    stationId,
    userId,
    isEnabled,
    reason
  });

  const station = await Station.findOne({
    where: { id: stationId },
    include: [{
      model: Vendor,
      where: { userId },
      attributes: [],
      required: true
    }],
    attributes: ['id', 'isEnabled']
  });

  if (!station) {
    logger.warn('Station not found or unauthorized access attempt', {
      stationId,
      userId
    });
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found or does not belong to this vendor');
  }

  if (station.isEnabled === isEnabled) {
    logger.warn('Redundant station status update attempted', {
      stationId,
      currentStatus: station.isEnabled,
      requestedStatus: isEnabled
    });
    throw new ApiError(httpStatus.BAD_REQUEST, `Station is already ${isEnabled ? 'enabled' : 'disabled'}.`);
  }

  // Prepare update data
  const updateData = { isEnabled };
  if (!isEnabled) {
    updateData.reason = reason; // Only add reason when disabling
  } else {
    updateData.reason = null; // Clear reason when re-enabling
  }

  // Update station status
  await Station.update(updateData, {
    where: { id: stationId }
  });
};

const addChargerToStation = async (chargerData, userId) => {
  logger.info('Starting charger addition process', {
    userId,
    stationId: chargerData.stationId
  });

  try {
    const vendor = await Vendor.findOne({
      where: { userId },
      attributes: ['id'],
    });

    if (!vendor) {
      logger.warn('Vendor not found for user', { userId });
      throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
    }

    const station = await Station.findOne({
      where: {
        id: chargerData.stationId,
        vendorId: vendor.id,
      },
    });

    if (!station) {
      logger.warn('Station not found or unauthorized access', {
        stationId: chargerData.stationId,
        vendorId: vendor.id
      });
      throw new ApiError(httpStatus.NOT_FOUND, 'Station not found or does not belong to this vendor');
    }

    if (station.approvalStatus !== 'approved') {
      logger.warn('Attempted to add charger to unapproved station', {
        stationId: station.id,
        status: station.approvalStatus
      });
      throw new ApiError(httpStatus.FORBIDDEN, 'Station is not approved. Cannot add charger.');
    }

    // Validate connector exists
    const validConnector = await Connector.findByPk(chargerData.connectorId, {
      attributes: ['id', 'name', 'type'],
    });

    if (!validConnector) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid connector ID provided');
    }

    // Validate power exists
    const validPower = await Power.findByPk(chargerData.powerId, {
      attributes: ['id', 'value', 'unit'],
    });

    if (!validPower) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid power ID provided');
    }

    // Create single charger
    const createdCharger = await Charger.create({
      stationId: station.id,
      connectorId: chargerData.connectorId,
      powerId: chargerData.powerId,
      pricePerHour: chargerData.pricePerHour,
    });

    // Return created charger with its details
    return {
      stationId: station.id,
      charger: {
        id: createdCharger.id,
        pricePerHour: createdCharger.pricePerHour,
        connector: {
          name: validConnector.name,
          type: validConnector.type,
        },
        power: {
          value: validPower.value,
          unit: validPower.unit,
        },
      },
    };
  } catch (error) {
    logger.error('Error adding charger to station', {
      stationId: chargerData.stationId,
      userId,
      error: error.message,
      stack: error.stack
    });
    throw error;
  }
};

const getStationChargers = async (stationId, userId) => {
  try {
    // Get station with its chargers and verify it belongs to the vendor
    const station = await Station.findOne({
      where: {
        id: stationId,
      },
      attributes: ['id', 'stationName', 'address', 'city', 'state'],
      include: [
        {
          model: Charger,
          include: [
            {
              model: Connector,
              attributes: ['name', 'type'],
            },
            {
              model: Power,
              attributes: ['value', 'unit'],
            },
          ],
        },
      ],
    });

    if (!station) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
    }

    // Format the response
    return {
      stationId: station.id,
      stationName: station.stationName,
      address: station.address,
      city: station.city,
      state: station.state,
      chargers: station.chargers.map(charger => ({
        id: charger.id,
        pricePerHour: charger.pricePerHour,
        connector: {
          name: charger.connector.name,
          type: charger.connector.type,
        },
        power: {
          value: charger.power.value,
          unit: charger.power.unit,
        },
      })),
    };
  } catch (error) {
    console.error('Error in getStationChargers:', {
      stationId,
      userId,
      error: error.message,
    });
    throw error;
  }
};

const deleteCharger = async (chargerId, userId) => {
  // First, find the vendor associated with the user
  const vendor = await Vendor.findOne({
    where: { userId },
    attributes: ['id'],
  });

  if (!vendor) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
  }

  // Check if the charger exists and belongs to the vendor
  const charger = await Charger.findOne({
    where: {
      id: chargerId,
      '$Station.vendorId$': vendor.id, // Using association to check if the charger belongs to the vendor
    },
    include: [
      {
        model: Station,
        attributes: [], // No need to fetch station data
      },
    ],
  });

  if (!charger) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Charger not found or does not belong to this vendor');
  }

  // Delete the charger
  await charger.destroy();
  return;
};

const getChargerById = async (chargerId) => {
  const charger = await Charger.findOne({
    where: { id: chargerId },
    attributes: { exclude: ['createdAt', 'updatedAt'] },
    include: [
      {
        model: Connector,
        attributes: ['id', 'name', 'type']
      },
      {
        model: Power,
        attributes: ['id', 'value', 'unit']
      }
    ]
  });

  if (!charger) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Charger not found');
  }

  return charger;
};

const blockStation = async (stationId, userId, blockData) => {
  // Check if user is a vendor
  const vendor = await Vendor.findOne({ where: { userId }, attributes: ['id'] });
  if (!vendor) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to block stations');
  }

  // Ensure station exists and belongs to the user
  const station = await Station.findOne({
    where: { id: stationId, vendorId: vendor.id },
    attributes: ['id'],
  });

  if (!station) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to block this station');
  }

  // Convert input dates to UTC and set them to full-day ranges
  const startDate = moment.utc(blockData.startDate).startOf('day');
  const endDate = moment.utc(blockData.endDate).endOf('day');

  // Ensure endDate is not before startDate
  if (endDate.isBefore(startDate)) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'End date cannot be before start date');
  }

  // Check for existing bookings in the same time range
  const existingBooking = await Booking.findOne({
    where: {
      stationId,
      [Op.or]: [
        {
          [Op.and]: [{ startTime: { [Op.lte]: endDate.toDate() } }, { endTime: { [Op.gte]: startDate.toDate() } }],
        },
      ],
    },
  });

  if (existingBooking) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Station has active bookings in the selected period. Cannot block.');
  }

  // Check for overlapping blocks
  const existingBlock = await StationBlock.findOne({
    where: {
      stationId,
      isActive: true,
      [Op.or]: [
        {
          [Op.and]: [{ startDate: { [Op.lte]: endDate.toDate() } }, { endDate: { [Op.gte]: startDate.toDate() } }],
        },
      ],
    },
  });

  if (existingBlock) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Station already has an active block during this period');
  }

  // Create the new block entry
  return await StationBlock.create({
    stationId,
    startDate: startDate.toDate(),
    endDate: endDate.toDate(),
    reason: blockData.reason,
    isActive: true,
  });
};

const getStationBlocks = async stationId => {
  const blocks = await StationBlock.findAll({
    where: {
      stationId,
      isActive: true,
      endDate: { [Op.gte]: new Date() },
    },
  });
  return blocks;
};

const removeStationBlock = async (blockId, userId) => {
  // Find the block along with the vendor and station
  const block = await StationBlock.findOne({
    where: { id: blockId, isActive: true },
    include: {
      model: Station,
      attributes: ['vendorId'],
      include: { model: Vendor, attributes: ['id'], where: { userId } }, // Ensures station belongs to the user
    },
  });

  if (!block) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Block not found or you are not authorized to remove it');
  }

  // Soft delete the block
  await StationBlock.update({ isActive: false }, { where: { id: blockId } });

  return { message: 'Block removed successfully' };
};
const getConnectors = async () => {
  const connectors = await Connector.findAll({
    attributes: ['id', 'name', 'type'],
  });
  return connectors;
};

const getAmenities = async () => {
  const amenities = await Amenity.findAll({
    attributes: ['id', 'name'],
  });
  return amenities;
};
const getPowers = async () => {
  const powers = await Power.findAll({
    attributes: ['id', 'value', 'unit']
  });
  return powers;
}

const updateStation = async (stationId, updateData) => {
  const {
    stationName,
    city,
    pinCode,
    state,
    address,
    latitude,
    longitude,
    operationalSchedule,
    amenities,
    userId,
    files,
    phoneNumber
  } = updateData;

  // Check if station exists and user has permission to edit it
  const station = await Station.findOne({
    where: { id: stationId },
    include: [
      {
        model: Vendor,
        attributes: ['id', 'userId'],
      },
    ],
  });

  if (!station) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
  }

  // Check if user has permission to edit this station
  if (station.Vendor.userId !== userId) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You do not have permission to edit this station');
  }

  // Start a transaction
  const transaction = await sequelize.transaction();

  try {
    // Create an update object with only the fields that are provided
    const updateFields = {};

    if (stationName !== undefined) updateFields.stationName = stationName;
    if (city !== undefined) updateFields.city = city;
    if (pinCode !== undefined) updateFields.pinCode = pinCode;
    if (state !== undefined) updateFields.state = state;
    if (address !== undefined) updateFields.address = address;
    if (latitude !== undefined) updateFields.latitude = latitude;
    if (longitude !== undefined) updateFields.longitude = longitude;
    if (operationalSchedule !== undefined) updateFields.operationalSchedule = operationalSchedule;
    if (phoneNumber !== undefined) updateFields.phoneNumber = phoneNumber;

    // Only update if there are fields to update
    if (Object.keys(updateFields).length > 0) {
      await station.update(updateFields, { transaction });
    }

    // Handle images if provided
    if (files && files.length > 0) {
      // Get current images or initialize empty array
      const currentImages = station.images || [];

      // Check if adding new images would exceed the maximum limit
      const MAX_IMAGES = 10;
      if (currentImages.length + files.length > MAX_IMAGES) {
        throw new ApiError(
          httpStatus.BAD_REQUEST,
          `Cannot add more images. Maximum of ${MAX_IMAGES} images allowed. You already have ${currentImages.length} images.`
        );
      }

      // Upload new images using the utility function
      const newImageUrls = await uploadAllOrRollback(files, 'stationImages');

      // Combine existing images with new ones
      const updatedImages = [...currentImages, ...newImageUrls];

      // Update station images
      await station.update({
        images: updatedImages,
      }, { transaction });
    }

    // Update amenities if provided
    if (amenities && amenities.length > 0) {
      // Remove existing amenities
      await StationAmenity.destroy({
        where: { stationId },
        transaction,
      });

      // Add new amenities
      await Promise.all(
        amenities.map(async (amenityId) => {
          return StationAmenity.create(
            {
              stationId,
              amenityId,
            },
            { transaction }
          );
        })
      );
    }

    // Commit transaction
    await transaction.commit();

    // Fetch updated station with all relations
    const updatedStation = await Station.findByPk(stationId, {
      include: [
        {
          model: Amenity,
          as: 'amenities',
          through: { attributes: [] },
        }
      ],
    });

    return updatedStation;
  } catch (error) {
    // Rollback transaction on error
    await transaction.rollback();
    throw error;
  }
};

const toggleChargerStatus = async (chargerId, userId, isEnabled, reason) => {
  const vendor = await Vendor.findOne({ where: { userId }, attributes: ['id'] });
  if (!vendor) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
  }

  // Find the charger along with its station to verify vendor ownership
  const charger = await Charger.findOne({
    where: { id: chargerId },
    include: [
      {
        model: Station,
        where: { vendorId: vendor.id },
        attributes: [], // No need to fetch station attributes
      },
    ],
  });

  if (!charger) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Charger not found or does not belong to this vendor');
  }

  // Update charger status
  await charger.update({
    isEnabled,
    reason: !isEnabled ? reason : null, // Set reason when disabling, clear when enabling
  });

  return charger; // Return updated charger
};

/**
 * Get list of users who have booked at the station
 * @param {number} stationId - Station ID
 * @param {number} userId - User ID (to verify vendor ownership)
 * @param {Object} options - Query options
 * @param {number} options.page - Page number
 * @param {number} options.limit - Items per page
 * @param {string} options.timeFilter - Time filter (3months, 6months, all)
 * @returns {Promise<Object>} - Paginated user list
 */
const getStationVisitors = async (stationId, userId, { page, limit, timeFilter }) => {
  // Get vendor details from user ID
  const vendor = await Vendor.findOne({ where: { userId } });
  if (!vendor) {
    throw new ApiError(httpStatus.FORBIDDEN, 'Only vendors can view station visitors');
  }

  // Verify if the vendor owns the station
  const stationExists = await Station.count({
    where: { id: stationId, vendorId: vendor.id }
  });
  if (!stationExists) throw new ApiError(httpStatus.FORBIDDEN, 'Unauthorized access to station');

  const offset = (page - 1) * limit;

  // Calculate the date threshold based on the time filter
  let dateThreshold = null;
  if (timeFilter !== 'all') {
    const months = timeFilter === '3months' ? 3 : 6;
    dateThreshold = moment().subtract(months, 'months').toDate();
  }

  // Base booking where clause
  const bookingWhere = { stationId };
  if (dateThreshold) {
    bookingWhere.startTime = {
      [Op.gte]: dateThreshold
    };
  }

  // Get users who have made bookings along with their reviews
  const visitors = await User.findAll({
    attributes: [
      'id',
      'name',
      'email',
      'profilePicUrl',
      'phone'
    ],
    include: [
      {
        model: Booking,
        as: 'bookings',
        where: bookingWhere,
        attributes: [],
        required: true
      },
      {
        model: Review,
        where: { stationId },
        required: false,
        attributes: ['rating', 'comment', 'createdAt']
      }
    ],
    limit,
    offset,
    subQuery: false,
    group: ['User.id', 'Reviews.id', 'Reviews.rating', 'Reviews.comment', 'Reviews.createdAt']
  });

  // Get total count for pagination
  const totalCount = await User.count({
    distinct: true,
    include: [{
      model: Booking,
      as: 'bookings',
      where: bookingWhere,
      attributes: [],
      required: true
    }]
  });

  const totalPages = Math.ceil(totalCount / limit);

  return {
    visitors: visitors.map(visitor => ({
      id: visitor.id,
      name: visitor.name,
      email: visitor.email,
      profilePicUrl: visitor.profilePicUrl,
      phone: visitor.phone,
      review: visitor.Reviews && visitor.Reviews.length > 0 ? {
        rating: visitor.Reviews[0].rating,
        comment: visitor.Reviews[0].comment,
        createdAt: visitor.Reviews[0].createdAt
      } : null
    })),
    pagination: {
      totalItems: totalCount,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  };
};

const checkStationCompatibility = async (vehicleId, stationId) => {
  const result = getStationChargerCompatibility(vehicleId, stationId);
  return result;

}

module.exports = {
  addStation,
  getAmenities,
  deleteCharger,
  getStations,
  addChargerToStation,
  getStationChargers,
  blockStation,
  getStationBlocks,
  removeStationBlock,
  toggleStationStatus,
  getStationById,
  getConnectors,
  getPowers,
  getChargerById,
  updateStation,
  toggleChargerStatus,
  getStationVisitors,
  checkStationCompatibility
};
