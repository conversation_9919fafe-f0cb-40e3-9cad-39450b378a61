'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.removeColumn('user_vehicles', 'trimId');
    await queryInterface.removeColumn('user_vehicles', 'trimName');
    await queryInterface.removeColumn('user_vehicles', 'registrationNumber');

    await queryInterface.addColumn('stations', 'phoneNumber', {
      type: Sequelize.STRING,
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.addColumn('user_vehicles', 'trimId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'vehicle_trims',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('user_vehicles', 'trimName', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('user_vehicles', 'registrationNumber', {
      type: Sequelize.STRING,
      allowNull: false,
      unique: true,
    });
  }
};
