const Joi = require('joi');
const { WALLET_TRANSACTION_TYPE } = require('../constants');

const createWallet = {
  body: Joi.object().keys({
    userId: Joi.number().integer().optional(),
    vendorId: Joi.number().integer().optional(),
    currency: Joi.string().length(3).optional(),
  }).xor('userId', 'vendorId'), // Either userId or vendorId must be provided, but not both
};

const createUserWallet = {
  body: Joi.object().keys({
    currency: Joi.string().length(3).optional().default('USD'),
  })
};

const createVendorWallet = {
  body: Joi.object().keys({
    currency: Joi.string().length(3).optional().default('USD'),
  })
};


const topUpWallet = {
  params: Joi.object().keys({
    walletId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    amount: Joi.number().positive().required(),
    metadata: Joi.object().optional(),
    referenceId: Joi.string().optional(),
  }),
};

const debitFromWallet = {
  params: Joi.object().keys({
    walletId: Joi.number().integer().required(),
  }),
  body: Joi.object().keys({
    amount: Joi.number().positive().required(),
    type: Joi.string().valid(
      WALLET_TRANSACTION_TYPE.PAYMENT,
      WALLET_TRANSACTION_TYPE.WITHDRAWAL,
      WALLET_TRANSACTION_TYPE.BOOKING_PAYMENT
    ).required(),
    description: Joi.string().required(),
    metadata: Joi.object().optional(),
    referenceId: Joi.string().optional(),
  }),
};


const getWalletTransactions = {
  params: Joi.object().keys({
    walletId: Joi.number().integer().required(),
  }),
  query: Joi.object().keys({
    type: Joi.string().valid(
      WALLET_TRANSACTION_TYPE.TOPUP,
      WALLET_TRANSACTION_TYPE.PAYMENT,
      WALLET_TRANSACTION_TYPE.REFUND,
      WALLET_TRANSACTION_TYPE.WITHDRAWAL,
      WALLET_TRANSACTION_TYPE.TRANSFER,
      WALLET_TRANSACTION_TYPE.ADJUSTMENT,
      WALLET_TRANSACTION_TYPE.BOOKING_PAYMENT,
      WALLET_TRANSACTION_TYPE.BOOKING_REFUND
    ).optional(),
    status: Joi.string().valid('pending', 'completed', 'failed', 'cancelled').optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
    sortBy: Joi.string().valid('createdAt', 'amount', 'processedAt').optional().default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').optional().default('desc'),
  }),
};

const getTransaction = {
  params: Joi.object().keys({
    walletId: Joi.number().integer().required(),
    transactionId: Joi.number().integer().required(),
  }),
};

module.exports = {
  createWallet,
  createUserWallet,
  createVendorWallet,
  topUpWallet,
  debitFromWallet,
  getWalletTransactions,
  getTransaction,
};
