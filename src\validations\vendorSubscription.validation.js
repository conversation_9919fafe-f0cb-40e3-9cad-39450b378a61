const Joi = require('joi');

const initializeSubscriptionPayment = {
  body: Joi.object().keys({
    provider: Joi.string()
      .required()
      .valid('zaincash', 'fib')
      .messages({
        'any.required': 'Payment provider is required',
        'string.empty': 'Payment provider cannot be empty',
        'any.only': 'Payment provider must be one of: zaincash, fib'
      }),
    vendorId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Vendor ID must be a number',
        'number.integer': 'Vendor ID must be an integer',
        'any.required': 'Vendor ID is required'
      }),
    subscriptionPlanId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Subscription plan ID must be a number',
        'number.integer': 'Subscription plan ID must be an integer',
        'any.required': 'Subscription plan ID is required'
      }),
    autoRenew: Joi.boolean()
      .default(false)
      .messages({
        'boolean.base': 'Auto-renew must be a boolean'
      })
  })
};

const handleZaincashCallback = {
  query: Joi.object().keys({
    token: Joi.string()
      .required()
      .messages({
        'any.required': 'Payment token is required',
        'string.empty': 'Payment token cannot be empty'
      })
  })
};

const handleFibCallback = {
  body: Joi.object().keys({
    id: Joi.string()
      .required()
      .messages({
        'any.required': 'Payment ID is required',
        'string.empty': 'Payment ID cannot be empty'
      }),
    status: Joi.object()
      .required()
      .messages({
        'any.required': 'Payment status is required'
      })
  })
};

const handleFibRedirect = {
  query: Joi.object().keys({
    paymentId: Joi.string()
      .required()
      .messages({
        'any.required': 'Payment ID is required',
        'string.empty': 'Payment ID cannot be empty'
      })
  })
};

const getSubscriptionById = {
  params: Joi.object().keys({
    subscriptionId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Subscription ID must be a number',
        'number.integer': 'Subscription ID must be an integer',
        'any.required': 'Subscription ID is required'
      })
  })
};

const getActiveSubscription = {
  params: Joi.object().keys({
    vendorId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Vendor ID must be a number',
        'number.integer': 'Vendor ID must be an integer',
        'any.required': 'Vendor ID is required'
      })
  })
};

const cancelSubscription = {
  params: Joi.object().keys({
    subscriptionId: Joi.number()
      .integer()
      .required()
      .messages({
        'number.base': 'Subscription ID must be a number',
        'number.integer': 'Subscription ID must be an integer',
        'any.required': 'Subscription ID is required'
      })
  })
};

module.exports = {
  initializeSubscriptionPayment,
  handleZaincashCallback,
  handleFibCallback,
  handleFibRedirect,
  getSubscriptionById,
  getActiveSubscription,
  cancelSubscription
};
