const catchAsync = require('../utils/catchAsync');
const { bookmarkService } = require('../services');
const httpStatus = require('http-status');

const addBookmark = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { stationId } = req.params;
    const result = await bookmarkService.addBookmark(userId, stationId);
    res.status(httpStatus.CREATED).json({
        status: true,
        data: result,
        message: 'Bookmark added successfully',
    });
});

const removeBookmark = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { stationId } = req.params;
    await bookmarkService.removeBookmark(userId, stationId);
    res.status(httpStatus.OK).json({
        status: true,
        message: 'Bookmark removed successfully',
    });
});

const getBookmarks = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { page, limit } = req.query;

    const result = await bookmarkService.getBookmarks(userId, page, limit);
    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: 'Bookmarks fetched successfully',
    });
});
module.exports = {
    addBookmark,
    removeBookmark,
    getBookmarks,
};
