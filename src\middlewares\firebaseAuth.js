const admin = require("firebase-admin");
const httpStatus = require("http-status");
const ApiError = require("../utils/ApiError");
const { User } = require("../models");
const { userService, authService } = require("../services");
const { FIREBASE_API_KEY } = require("../config/config");
const { default: axios } = require("axios");
require("../../firebase-info")


const firebaseAuth = async (req,res,next) =>{
    return new Promise( async (resolve, reject) => {
    try {
        let idToken;
        if(req.headers.authorization && req.headers.authorization.startsWith('Bearer')){
            idToken = req.headers.authorization.split(' ')[1];
        }
        if(!idToken){
            return next(new ApiError('Please pass firebase auth token ',400));
        }
        let decodedIdToken;
        try{
            decodedIdToken = await admin.auth().verifyIdToken(idToken,true)
        }catch(error){
            if (error.code === 'auth/user-disabled') {
                return next(new ApiError(httpStatus.FORBIDDEN, "User is suspended. Please contact support."));
              }
              return next(error);
        }
        req.decodedToken = decodedIdToken;

        // Just check baseUrl for admin routes
        if (req.baseUrl.includes('/admin')) {
            // For admin routes, just pass the decoded token
            // adminAuth middleware will handle admin validation
            resolve();
            return;
        }

        // For non-admin routes, proceed with user validation
        const user = await authService.getUserByFirebaseUId(decodedIdToken.uid);
        if(!user) {
            if(req.path === "/register") {
                req.newUser = decodedIdToken;
            } else reject(new ApiError(httpStatus.NOT_FOUND, "User doesn't exist. Please create account"));
        } else {
            if(user.isBlocked) { throw new ApiError(httpStatus.FORBIDDEN, "User is blocked"); }
            if(user.isDeleted) { throw new ApiError(httpStatus.GONE, "User doesn't exist anymore"); }
            if (user.isEmailVerified === 0) {
                throw new ApiError(httpStatus.FORBIDDEN, "Please verify your email before continuing.");
            }
            req.user = user;
        }
        resolve(); 
    } catch(err) {
        console.log("FirebaseAuthError:", err);
        reject(new ApiError(httpStatus.UNAUTHORIZED, err))
    }
    }).then(() => next()).catch((err) => next(err));
}
//Only for backend developer to generate token to call APIS
const generateToken = async(req,res,next) => {

    try{
        const token =  await admin.auth().createCustomToken(req.params.uid);
        // console.log(getAuth(restApp));
        // const FIREBASE_API_KEY = fir;
        const resp = await axios({
          url: `https://www.googleapis.com/identitytoolkit/v3/relyingparty/verifyCustomToken?key=${FIREBASE_API_KEY}`,
          method: 'post',
          data: {
            token: token,
            returnSecureToken: true
          },
          json: true,
        });
  
        const idToken = resp.data.idToken;
    
  
        return res.status(200).json({
            status: true,
            token: idToken
        });
  
    }catch(err){
        console.log(err)
        return res.status(500).json({
            status:false,
            msg:err.message
        })
    }
  }


module.exports = {firebaseAuth,generateToken}
