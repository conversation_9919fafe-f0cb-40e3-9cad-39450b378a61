const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Admin } = require('../models');
const jwt = require('jsonwebtoken');
const config = require('../config/config');

const verifyToken = async (req) => {
  let token;
  if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
    token = req.headers.authorization.split(' ')[1];
  }

  if (!token) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Please authenticate');
  }

  try {
    const decoded = jwt.verify(token, config.jwt.secret);
    return decoded;
  } catch (error) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Invalid token');
  }
};

const adminAuth = async (req, res, next) => {
  try {
    const decoded = await verifyToken(req);
    
    const admin = await Admin.findOne({
      where: {
        id: decoded.id,
        email: decoded.email,
        isActive: true
      }
    });

    if (!admin) {
      throw new ApiError(httpStatus.FORBIDDEN, 'Admin access required');
    }

    req.admin = admin;
    next();
  } catch (error) {
    next(error);
  }
};

const superAdminAuth = async (req, res, next) => {
  try {
    const decoded = await verifyToken(req);
    
    const admin = await Admin.findOne({
      where: {
        id: decoded.id,
        email: decoded.email,
        role: 'super_admin',
        isActive: true
      }
    });

    if (!admin) {
      throw new ApiError(httpStatus.FORBIDDEN, 'Super admin access required');
    }

    req.admin = admin;
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  adminAuth,
  superAdminAuth
}; 