const express = require('express');
const router = express.Router();
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');

const { vehicleController } = require('../../controllers');
const userValidation = require('../../validations/user.validation');

router.post('/',firebaseAuth,validate(userValidation.addUserVehicle), vehicleController.addUserVehicle);
router.get('/',firebaseAuth, vehicleController.getUserVehicles);
router.put('/',firebaseAuth,vehicleController.updateUserVehicle);
router.delete('/:vehicleId',firebaseAuth, vehicleController.deleteUserVehicle);
router.patch('/primary',firebaseAuth, vehicleController.createVehiclePrimary);
router.get('/brands', vehicleController.getAllBrands);
router.get('/brands/:brandId/models', vehicleController.getModelsByBrand);

router.get('/models/:modelId/trims', vehicleController.getTrimsByModel);
router.get('/manufacturing-units', vehicleController.getAllManufacturingUnits);
router.get('/battery-capacities', vehicleController.getAllBatteryCapacities);
module.exports = router;
