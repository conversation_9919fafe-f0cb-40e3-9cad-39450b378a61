const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const Transaction = sequelize.define(
    'transactions',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      transactionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        unique: true,
        comment: 'OCPP Transaction ID'
      },
      sessionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'charging_sessions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      chargerId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'chargers',
          key: 'id'
        }
      },
      connectorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      idTag: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      startTimestamp: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      stopTimestamp: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      meterStart: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Starting meter value in Wh'
      },
      meterStop: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Ending meter value in Wh'
      },
      stopReason: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      reservationId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      }
    },
    {
      tableName: 'transactions',
      timestamps: true,
      indexes: [
        { fields: ['transactionId'] },
        { fields: ['sessionId'] },
        { fields: ['chargerId'] },
        { fields: ['connectorId'] },
        { fields: ['idTag'] },
        { fields: ['startTimestamp'] },
        { fields: ['stopTimestamp'] }
      ]
    }
  );

  return Transaction;
};
