const { Admin } = require('../src/models');
const { sequelize } = require('../src/config/database');
const { isEmail } = require('validator');
const { ADMIN_ROLE } = require('../src/constants/index');

async function createSuperAdmin(email, password, name = 'Super Admin') {
  if (!isEmail(email)) {
    throw new Error('Invalid email format');
  }

  console.log('Creating super admin with:');
  console.log('Email:', email);
  console.log('Password (received):', password);
  console.log('Name:', name);

  const transaction = await sequelize.transaction();
  try {
    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ where: { email } });
    if (existingAdmin) {
      throw new Error('Admin with this email already exists');
    }

    // Create database record with hashed password
    const superAdmin = await Admin.create({
      email,
      password,
      name,
      role: ADMIN_ROLE.SUPER_ADMIN
    }, { transaction });

    console.log('Created admin with hashed password:', superAdmin.password);

    await transaction.commit();
    return superAdmin;
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
}

// Execution
(async () => {
  try {
    const email = process.argv[2];
    const password = process.argv[3];
    const name = process.argv[4] || 'Super Admin';

    if (!email || !password) {
      throw new Error('Usage: node script.js <email> <password> [name]');
    }

    const result = await createSuperAdmin(email, password, name);
    console.log('Super admin created successfully:', result.email);
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
})();