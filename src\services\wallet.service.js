const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Wallet, WalletTransaction, User, Vendor } = require('../models');
const { sequelize } = require('../config/database');
const { WALLET_TRANSACTION_TYPE, WALLET_TRANSACTION_STATUS } = require('../constants');
const logger = require('../config/logger');

/**
 * Create a wallet for a user or vendor
 * @param {Object} walletData
 * @returns {Promise<Wallet>}
 */
const createWallet = async (walletData) => {
  const { userId, vendorId, currency = 'USD' } = walletData;

  if (!userId && !vendorId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Either userId or vendorId is required');
  }

  if (userId && vendorId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Cannot create wallet for both user and vendor');
  }

  const ownerId = userId || vendorId;
  const ownerType = userId ? 'user' : 'vendor';

  // Check if wallet already exists
  const existingWallet = await Wallet.findOne({
    where: { ownerId, ownerType },
  });

  if (existingWallet) {
    throw new ApiError(httpStatus.CONFLICT, 'Wallet already exists');
  }

  // Verify user/vendor exists
  if (ownerType === 'user') {
    const user = await User.findByPk(ownerId);
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
    }
  } else {
    const vendor = await Vendor.findByPk(ownerId);
    if (!vendor) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found');
    }
  }

  // Create wallet
  return Wallet.create({
    ownerId,
    ownerType,
    balance: 0,
    currency,
    isActive: true,
  });
};

/**
 * Create a wallet for a user
 * @param {number} userId - User ID
 * @param {string} currency - Currency code (default: USD)
 * @returns {Promise<Wallet>}
 */
const createUserWallet = async (userId, currency = 'USD') => {
  // Check if user exists
  const user = await User.findByPk(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND, 'User not found');
  }

  // Check if wallet already exists
  const existingWallet = await Wallet.findOne({
    where: {
      ownerId: userId,
      ownerType: 'user'
    }
  });

  if (existingWallet) {
    throw new ApiError(httpStatus.CONFLICT, 'Wallet already exists for this user');
  }

  // Create wallet
  return Wallet.create({
    ownerId: userId,
    ownerType: 'user',
    balance: 0,
    currency,
    isActive: true,
  });
};

/**
 * Create a wallet for a vendor
 * @param {number} userId - User ID of the vendor
 * @param {string} currency - Currency code (default: USD)
 * @returns {Promise<Wallet>}
 */
const createVendorWallet = async (userId, currency = 'USD') => {
  // Check if vendor exists
  const vendor = await Vendor.findOne({ where: { userId } });
  if (!vendor) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Vendor not found for this user');
  }

  // Check if wallet already exists
  const existingWallet = await Wallet.findOne({
    where: {
      ownerId: userId,
      ownerType: 'vendor'
    }
  });

  if (existingWallet) {
    throw new ApiError(httpStatus.CONFLICT, 'Wallet already exists for this vendor');
  }

  // Create wallet
  return Wallet.create({
    ownerId: userId,
    ownerType: 'vendor',
    balance: 0,
    currency,
    isActive: true,
  });
};

/**
 * Get wallet by ID
 * @param {number} walletId
 * @returns {Promise<Wallet>}
 */
const getWalletById = async (walletId) => {
  const wallet = await Wallet.findByPk(walletId);
  if (!wallet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found');
  }
  return wallet;
};

/**
 * Check if user is authorized to access a wallet
 * @param {number} walletId - Wallet ID
 * @param {number} userId - User ID
 * @returns {Promise<boolean>}
 */
const checkWalletOwnership = async (walletId, userId) => {
  // Use the getWalletById method to avoid code duplication
  const wallet = await getWalletById(walletId);

  // Direct ownership check for user wallets
  if (wallet.ownerType === 'user' && wallet.ownerId === userId) {
    return true;
  }

  // For vendor wallets, check if the user is the vendor
  if (wallet.ownerType === 'vendor') {
    // Check if the userId matches the vendor's userId
    if (wallet.ownerId === userId) {
      return true;
    }
  }

  return false;
};

/**
 * Get wallet by user ID
 * @param {number} userId
 * @param {string} ownerType - Type of owner ('user' or 'vendor'), defaults to 'user'
 * @returns {Promise<Wallet>}
 */
const getWalletByUserId = async (userId, ownerType = 'user') => {
  // We don't need to check if the user is a vendor here
  // That check should be done in the controller

  const wallet = await Wallet.findOne({
    where: {
      ownerId: userId,
      ownerType
    }
  });

  if (!wallet) {
    throw new ApiError(httpStatus.NOT_FOUND, `Wallet not found for this ${ownerType}`);
  }
  return wallet;
};

/**
 * Get wallet by vendor ID
 * @param {number} vendorId
 * @returns {Promise<Wallet>}
 */
const getWalletByVendorId = async (vendorId) => {
  const wallet = await Wallet.findOne({
    where: {
      ownerId: vendorId,
      ownerType: 'vendor'
    }
  });
  if (!wallet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found for this vendor');
  }
  return wallet;
};

/**
 * Top up wallet
 * @param {number} walletId
 * @param {number} amount
 * @param {Object} metadata
 * @param {string} referenceId
 * @returns {Promise<Object>}
 */
const topUpWallet = async (walletId, amount, metadata = {}, referenceId = null) => {
  if (amount <= 0) {
    logger.warn(`Invalid top-up amount attempted: ${amount} for walletId: ${walletId}`);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be greater than 0');
  }

  const transaction = await sequelize.transaction();
  logger.info(`Starting top-up transaction for walletId: ${walletId}, amount: ${amount}`);

  try {
    const wallet = await Wallet.findByPk(walletId, { transaction, lock: true });
    if (!wallet) {
      logger.error(`Wallet not found for top-up - walletId: ${walletId}`);
      throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found');
    }

    // Calculate how much to apply to penalties and how much to add to balance
    const appliedToDue = Math.min(wallet.dueAmount || 0, amount);
    const remainingToBalance = amount - appliedToDue;

    // Update wallet balance and due amount atomically
    await wallet.updateBalanceWithLock(remainingToBalance, -appliedToDue, transaction);
    logger.debug(`Wallet balance updated - walletId: ${walletId}, newBalance: ${wallet.balance}, remainingDue: ${wallet.dueAmount}`);

    // Create or update the transaction record
    const walletTransaction = await WalletTransaction.findOne({
      where: {
        referenceId,
        status: WALLET_TRANSACTION_STATUS.PENDING
      },
      transaction
    });

    if (!walletTransaction) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Pending transaction not found');
    }

    // Update the existing transaction
    await walletTransaction.update({
      balanceAfter: wallet.balance,
      status: WALLET_TRANSACTION_STATUS.COMPLETED,
      type: WALLET_TRANSACTION_TYPE.TOPUP,
      metadata: {
        ...metadata,
        appliedToDue,
        creditedToBalance: remainingToBalance
      },
      description: 'Wallet top up with penalty adjustment',
      referenceId,
      processedAt: new Date()
    }, { transaction });

    // If there are penalties to adjust, create penalty settlement transaction
    if (appliedToDue > 0) {
      await WalletTransaction.create({
        walletId,
        amount: -appliedToDue,
        balanceAfter: wallet.balance,
        type: WALLET_TRANSACTION_TYPE.PENALTY,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description: 'Penalty settlement from top-up',
        metadata: {
          ...metadata,
          settledFromTopUp: true,
          topUpTransactionId: walletTransaction.id
        },
        relatedTransactionId: walletTransaction.id,
        processedAt: new Date()
      }, { transaction });
    }

    await transaction.commit();
    logger.info(`Top-up transaction completed - walletId: ${walletId}, transactionId: ${walletTransaction.id}`);

    return { 
      wallet, 
      transaction: walletTransaction,
      appliedToDue,
      creditedToBalance: remainingToBalance
    };
  } catch (error) {
    await transaction.rollback();
    logger.error(`Top-up failed for walletId: ${walletId}`, { 
      error: error.message,
      stack: error.stack 
    });
    throw error;
  }
};

/**
 * Debit from wallet
 * @param {number} walletId
 * @param {number} amount
 * @param {string} type
 * @param {string} description
 * @param {Object} metadata
 * @param {string} referenceId
 * @returns {Promise<Object>}
 */
const debitFromWallet = async (walletId, amount, type, description, metadata = {}, referenceId = null) => {
  if (amount <= 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be greater than 0');
  }

  // Convert amount to negative for debit
  const debitAmount = -Math.abs(amount);

  const transaction = await sequelize.transaction();

  try {
    // Get wallet with lock for update
    const wallet = await Wallet.findByPk(walletId, { transaction, lock: true });
    if (!wallet) {
      await transaction.rollback();
      throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found');
    }

    if (!wallet.isActive) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Wallet is inactive');
    }

    // Check if wallet has sufficient balance
    if (wallet.balance < Math.abs(debitAmount)) {
      await transaction.rollback();
      throw new ApiError(httpStatus.BAD_REQUEST, 'Insufficient balance');
    }

    // Update wallet balance with optimistic locking
    await wallet.updateBalanceWithLock(debitAmount, transaction);

    // Create transaction record
    const walletTransaction = await WalletTransaction.create(
      {
        walletId,
        amount: debitAmount,
        balanceAfter: wallet.balance,
        type,
        status: WALLET_TRANSACTION_STATUS.COMPLETED,
        description,
        metadata,
        referenceId,
        processedAt: new Date(),
      },
      { transaction }
    );

    await transaction.commit();

    return {
      wallet,
      transaction: walletTransaction,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

/**
 * Get wallet transactions
 * @param {number} walletId
 * @param {Object} filter
 * @param {Object} options
 * @returns {Promise<Object>}
 */
const getWalletTransactions = async (walletId, filter = {}, options = {}) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
  const offset = (page - 1) * limit;

  const wallet = await Wallet.findByPk(walletId);
  if (!wallet) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Wallet not found');
  }

  const whereClause = { walletId, ...filter };

  const { count, rows } = await WalletTransaction.findAndCountAll({
    where: whereClause,
    order: [[sortBy, sortOrder.toUpperCase()]],
    limit,
    offset,
  });

  return {
    transactions: rows,
    totalCount: count,
    totalPages: Math.ceil(count / limit),
    currentPage: page,
  };
};

/**
 * Get transaction by ID
 * @param {number} transactionId
 * @returns {Promise<WalletTransaction>}
 */
const getTransactionById = async (transactionId) => {
  const transaction = await WalletTransaction.findByPk(transactionId, {
    include: [
      {
        model: WalletTransaction,
        as: 'relatedTransaction',
      },
    ],
  });

  if (!transaction) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Transaction not found');
  }

  return transaction;
};

module.exports = {
  createWallet,
  createUserWallet,
  createVendorWallet,
  getWalletById,
  checkWalletOwnership,
  getWalletByUserId,
  getWalletByVendorId,
  topUpWallet,
  debitFromWallet,
  getWalletTransactions,
  getTransactionById,
};


