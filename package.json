{"name": "ev-ocpp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node ocpp-server.js", "dev": "nodemon ocpp-server.js", "test": "node test-system.js", "test-with-axios": "npm install axios && node test-system.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "nodemon": "^3.1.9", "pg": "^8.14.1", "sequelize": "^6.37.6", "ws": "^8.18.0"}}