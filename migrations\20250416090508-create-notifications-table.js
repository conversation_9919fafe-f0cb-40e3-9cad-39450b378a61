'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('notifications', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        comment: 'User ID for user-specific notifications, null for admin notifications',
      },
      isAdminNotification: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        comment: 'Flag to indicate if this is an admin notification',
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      body: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Type of notification (e.g., chat, booking, system)',
      },
      referenceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        comment: 'ID of the related entity (e.g., chatId, bookingId)',
      },
      referenceType: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Type of the related entity (e.g., chat, booking)',
      },
      isRead: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      data: {
        type: Sequelize.JSON,
        allowNull: true,
        comment: 'Additional data related to the notification',
      },
      firebaseMessageId: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'ID of the Firebase message if applicable',
      },
      topic: {
        type: Sequelize.STRING,
        allowNull: true,
        comment: 'Firebase topic this notification was sent to',
      },
      recipientType: {
        type: Sequelize.ENUM('user', 'admin', 'both'),
        defaultValue: 'user',
        comment: 'Type of recipient (user, admin, or both)',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('notifications');
  }
};
