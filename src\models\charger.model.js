const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Charger = sequelize.define(
  'chargers',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    stationId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'stations',
        key: 'id'
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE'
    },
    connectorId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'connectors',
        key: 'id'
      }
    },
    powerId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'powers',
        key: 'id'
      }
    },
    pricePerHour: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    chargerId: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'UNKNOWN',
    },
    status: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'inactive',
    },
    lastUpdated: {
      type: DataTypes.DATE,
    },
    lastHeartbeat: {
      type: DataTypes.DATE,
    },
    lastMeterValue: {
      type: DataTypes.JSONB, 
      allowNull: true,
    },
    isEnabled:{
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true, // Default to enabled
      comment: 'Indicates if the charger is enabled or disabled'
    },
    reason: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: 'Reason for charger status change or maintenance notes'
    }
  },
  {
    tableName: 'chargers',
    timestamps: true
  }
);

module.exports = Charger; 