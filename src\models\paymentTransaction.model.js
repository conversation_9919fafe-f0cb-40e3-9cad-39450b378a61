const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const PaymentTransaction = sequelize.define('PaymentTransaction', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Transaction ID',
  },
  userId: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id',
    },
    comment: 'Who made the payment',
  },
  amount: {
    type: DataTypes.DECIMAL(15, 2),
    allowNull: false,
    comment: 'Positive for credit, negative for debit',
  },
  paymentMethod: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Method used for payment',
  },
  source: {
    type: DataTypes.ENUM('wallet', 'external'),
    allowNull: false,
    comment: 'Source of the payment',
  },
  purpose: {
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Purpose of the payment',
  },
  status: {
    type: DataTypes.ENUM('success', 'pending', 'failed', 'cancelled'),
    allowNull: false,
    defaultValue: 'pending',
    comment: 'Status of the payment',
  },
  referenceId: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Link to UserSubscription / Session etc.',
  },
  referenceType: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Type of reference (e.g., "subscription", "booking")',
  },
  remarks: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Optional notes',
  },
  metadata: {
    type: DataTypes.JSONB,
    allowNull: true,
    comment: 'Additional data related to the transaction',
  },
  transactionType: {
    type: DataTypes.ENUM('credit', 'debit'),
    allowNull: false,
    comment: 'Defines if the transaction is credit or debit',
  }
}, {
  tableName: 'payment_transactions',
  timestamps: true,
  indexes: [
    {
      fields: ['userId'],
    }, 
    {
      fields: ['status'],
    },
   
    {
      fields: ['createdAt'],
    },
  ]
});

module.exports = PaymentTransaction;
