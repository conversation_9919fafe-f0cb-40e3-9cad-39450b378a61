const { Op } = require('sequelize');
const { Station, Vendor, Booking, UserVehicle } = require('../../models');
const ApiError = require('../../utils/ApiError');
const { Charger, Amenity, Connector, Power, VehicleBrand, VehicleModel, StationBlock } = require('../../models');
const { STATION_APPROVAL_STATUS } = require('../../constants/station');
const { sequelize } = require('../../config/database');
const httpStatus = require('http-status');

const getStations = async filters => {
  const { search, status, sortBy, sortOrder, page, limit } = filters;

  const whereClause = {};
  const offset = (page - 1) * limit;

  // Build search conditions
  if (search) {
    whereClause[Op.or] = [{ stationName: { [Op.iLike]: `%${search}%` } }];
  }

  // Add status filter
  if (status) {
    whereClause.approvalStatus = status;
  }

  const { rows: stations, count: totalStations } = await Station.findAndCountAll({
    where: whereClause,
    order: [[sortBy, sortOrder]],
    limit,
    offset,
    distinct: true,
  });

  const transformedStations = stations.map(station => ({
    id: station.id,
    stationName: station.stationName,
    address: station.address,
    city: station.city,
    state: station.state,
    pinCode: station.pinCode,
    approvalStatus: station.approvalStatus,
    images: station.images,
    isEnabled: station.isEnabled,
  }));

  return {
    stations: transformedStations,
    pagination: {
      page: page,
      limit: limit,
      totalItems: totalStations,
      totalPages: Math.ceil(totalStations / limit),
    },
  };
};

/**
 * Approve a station if the vendor's KYC is approved
 * @param {string} stationId - ID of the station to approve
 * @returns {Promise<Object>} - Approved station details
 */
const approveStation = async stationId => {
  // Fetch station with its vendor in one query using association
  const station = await Station.findOne({
    where: { id: stationId },
    include: {
      model: Vendor,
      attributes: ['id', 'isKycApproved'],
    },
  });

  if (!station) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
  }

  if (!station.Vendor || !station.Vendor.isKycApproved) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Vendor KYC is not approved');
  }

  // Approve the station
  station.approvalStatus = STATION_APPROVAL_STATUS.APPROVED;
  await station.save();

  return station;
};

const getStationById = async stationId => {
  const station = await Station.findByPk(stationId, {
    include: [
      {
        model: Charger,
        attributes: ['id', 'connectorId', 'powerId', 'pricePerHour'],
        include: [
          {
            model: Connector,
            attributes: ['id', 'name', 'type'],
          },
          {
            model: Power,
            attributes: ['id', 'value', 'unit'],
          },
        ],
      },
      {
        model: Amenity,
        as: 'amenities',
        attributes: ['id', 'name'],
        through: { attributes: [] },
      },
    ],
    attributes: [
      'id',
      'stationName',
      'address',
      'city',
      'state',
      'pincode',
      'approvalStatus',
      [sequelize.literal(`"Station"."images"->0->>'url'`), 'stationImage'],
      'isEnabled',
    ],
  });

  if (!station) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
  }

  return station;
};

const getChargerById = async (chargerId, stationId) => {

  const charger = await Charger.findOne({
    where: { id: chargerId, stationId },
    attributes: { exclude: ['createdAt', 'updatedAt'] },
    include: [
      {
        model: Connector,
        attributes: ['id', 'name', 'type'],
      },
      {
        model: Power,
        attributes: ['id', 'value', 'unit'],
      },
    ],
  });

  if (!charger) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Charger not found');
  }

  return charger;
};

/**
 * Get booking history for a station with pagination
 * @param {number} stationId - Station ID
 * @param {number} page - Page number
 * @param {number} limit - Items per page
 * @returns {Promise<Object>} - Paginated booking history
 */
const getBookingHistory = async (stationId, page, limit) => {
  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Fetch total count and bookings in one query using findAndCountAll
  const { rows: bookings, count: totalCount } = await Booking.findAndCountAll({
    where: { stationId },
    attributes: [
      'id',
      'startTime',
      'endTime',
      'status',
      'totalCost',
      [sequelize.literal('"Station"."stationName"'), 'stationName'], // Get stationName as a literal value
    ],
    include: [
      {
        model: Station,
        attributes: [], // Don't fetch any other fields from Station model
      },
    ],
    limit,
    offset,
    order: [['startTime', 'DESC']],
    raw: true, // Raw query for better performance
  });

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / limit);

  return {
    bookings,
    pagination: {
      totalItems: totalCount,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1
    }
  };
};


const getBookingDetail = async (stationId, bookingId) => {
  const booking = await Booking.findOne({
    where: { id: bookingId, stationId },
    include: [
      {
        model: Station,
        attributes: ['stationName', "address"],
      },
      {
        model: Charger,
        attributes: ['id', 'pricePerHour'],
        include: [
          {
            model: Connector,
            attributes: ['id', 'name', 'type'],
          },
          {
            model: Power,
            attributes: ['id', 'value', 'unit'],
          },
        ],
      },
      {
        model: UserVehicle,
        attributes: ['brandName'],
        include: [
          {
            model: VehicleBrand,
            attributes: ['id', 'name'],
          },
          {
            model: VehicleModel,
            attributes: ['id', 'name'],
          },
        ],
      },
    ],
  });
  if (!booking) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Booking not found');
  }

  const data = booking.toJSON();


  return {
    id: data.id,
    startTime: data.startTime,
    endTime: data.endTime,
    status: data.status,
    station: {
      name: data.Station?.stationName,
      address: data.Station?.address
    },
    charger: data.charger
      ? {
        id: data.charger.id,
        pricePerHour: data.charger.pricePerHour,
        connector: data.charger.connector,
        power: data.charger.power,
      }
      : null,
    vehicle: data.userVehicle
      ? {
        brandName: data.userVehicle.brandName,
        brand: data.userVehicle.VehicleBrand.name,
        model: data.userVehicle.VehicleModel.name,
      }
      : null,
    payment: {
      totalCost: data.totalCost,
      paymentStatus: data.paymentStatus
    }
  };
};


const getBlockDates = async (stationId) => {
  const blocks = await StationBlock.findAll({
    where: { stationId },
    attributes: ['startDate', 'endDate', 'reason', 'createdAt'],
  });
  return blocks;
};

const toggleStationStatus = async (stationId, isEnabled, reason) => {
  const station = await Station.findByPk(stationId);
  if (!station) {
    throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');
  }

  if (station.isEnabled === isEnabled) {
    throw new ApiError(httpStatus.BAD_REQUEST, `Station is already ${isEnabled ? 'enabled' : 'disabled'}.`);
  }

  if (!isEnabled) {
    reason = reason || 'No reason provided';
  } else {
    reason = null;
  }

  await station.update({ isEnabled, reason });
};

module.exports = {
  getStations,
  approveStation,
  getStationById,
  getStationById,
  getChargerById,
  getBookingHistory,
  getBookingDetail,
  getBlockDates,
  toggleStationStatus
};
