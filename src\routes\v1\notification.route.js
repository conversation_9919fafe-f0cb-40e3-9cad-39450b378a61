const express = require('express');
// const auth = require('../../middlewares/auth');
const validate = require('../../middlewares/validate');
const notificationValidation = require('../../validations');
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const { notificationController } = require('../../controllers');

const router = express.Router();

/**
 * @api {get} v1/notifications Get user notifications
 * @apiDescription Get user notifications with pagination
 * @apiVersion 1.0.0
 * @apiName GetNotifications
 * @apiGroup Notification
 *
 * @apiParam  {Number{1-}}  [page=1]     List page
 * @apiParam  {Number{1-100}}  [limit=20]  Users per page
 *
 * @apiSuccess {Object[]} notifications List of notifications
 * @apiSuccess {Number} totalCount Total count of notifications
 * @apiSuccess {Number} currentPage Current page
 * @apiSuccess {Number} totalPages Total pages
 */
router.get('/', firebaseAuth,validate(notificationValidation.getNotifications),notificationController.getNotifications);
/**
 * @api {get} v1/notifications/unread/count Get unread notification count
 * @apiDescription Get count of unread notifications
 * @apiVersion 1.0.0
 * @apiName GetUnreadCount
 * @apiGroup Notification
 *
 * @apiSuccess {Number} count Count of unread notifications
 */
router.get('/unread/count', firebaseAuth, notificationController.getUnreadCount);

/**
 * @api {patch} v1/notifications/:notificationId/read Mark notification as read
 * @apiDescription Mark a notification as read
 * @apiVersion 1.0.0
 * @apiName MarkAsRead
 * @apiGroup Notification
 *
 * @apiParam  {String}  notificationId  Notification's unique id
 *
 * @apiSuccess {Object}  notification  Notification object
 */
router.patch('/:notificationId/read', firebaseAuth, validate(notificationValidation.markAsRead),notificationController.markAsRead);

/**
 * @api {patch} v1/notifications/read/all Mark all notifications as read
 * @apiDescription Mark all notifications as read
 * @apiVersion 1.0.0
 * @apiName MarkAllAsRead
 * @apiGroup Notification
 *
 * @apiSuccess {String}  message  Success message
 */
router.patch('/read/all', firebaseAuth, notificationController.markAllAsRead);

module.exports = router; 