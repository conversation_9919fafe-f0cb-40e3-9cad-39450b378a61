
const express = require('express');
const router = express.Router();
const {adminAuth} = require('../../../middlewares/adminAuth');
const revenueController = require('../../../controllers/admin/revenue.controller');
const validate = require('../../../middlewares/validate');
const revenueValidation = require('../../../validations/admin/revenue.validation');

/**
 * @api {get} v1/admin/revenue/summary Get revenue summary
 * @apiDescription Get revenue summary 
 * @apiName GetRevenueSummary
 * @apiSuccess {Object} data Revenue summary
 */
router.route('/summary').get(adminAuth, revenueController.getRevenueKPIs);


module.exports = router;

