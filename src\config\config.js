const Joi = require('joi');
const path = require('path');
const dotenv = require('dotenv');
const { Backup } = require('aws-sdk');

dotenv.config({path: path.join(__dirname, '../../.env')});

// schema of env files for validation
const envVarsSchema = Joi.object()
  .keys({
     NODE_ENV: Joi.string().valid("development").required(),
    PORT: Joi.number().default(8000),
    DB_CONNECTION:Joi.string().required(),
    DB_HOST:Joi.string().required(),
    DB_DATABASE:Joi.string().required(),
    DB_USERNAME:Joi.string().required(),
    DB_PASSWORD: Joi.string().allow('').default(''),
    FIREBASE_API_KEY: Joi.string().required(),
    FIREBASE_AUTH_DOMAIN: Joi.string().required(),
    FIREBASE_PROJECT_ID: Joi.string().required(),
    FIREBASE_STORAGE_BUCKET: Joi.string().required(),
    FIREBASE_MESSAGING_SENDER_ID: Joi.string().required(),
    FRONTEND_URL: Joi.string().required(),
    FIREBASE_APP_ID: Joi.string().required(),
    // Email configuration
    EMAIL_USER: Joi.string().description('Email username/address'),
    EMAIL_PASSWORD: Joi.string().description('Email password'),
    EMAIL_HOST: Joi.string().default('mail.privateemail.com').description('Email SMTP host'),
    EMAIL_PORT: Joi.number().default(465).description('Email SMTP port'),
    EMAIL_SECURE: Joi.boolean().default(true).description('Use secure connection'),
    EMAIL_FROM: Joi.string().default('<EMAIL>').description('Default from email address'),
    // ZainCash configuration
    ZAINCASH_MSISDN: Joi.string().description('ZainCash MSISDN'),
    ZAINCASH_SECRET: Joi.string().description('ZainCash secret key'),
    ZAINCASH_MERCHANT_ID: Joi.string().description('ZainCash merchant ID'),
    ZAINCASH_LANG: Joi.string().valid('en', 'ar').default('en').description('ZainCash language'),
    BACKEND_URL: Joi.string().required(),
    // FIREBASE_MEASUREMENT_ID: Joi.string().required(),
    // AWS_S3_SECRET_ACCESS_KEY: Joi.string().required(),
    // AWS_S3_REGION: Joi.string().required(),
    // AWS_S3_ACCESS_KEY_ID: Joi.string().required(),
    // AWS_S3_BUCKET: Joi.string().required(),
    // AWS_S3_PRIVATE_BUCKET: Joi.string().required(),
    JWT_SECRET: Joi.string().required(),
    JWT_EXPIRES_IN: Joi.number().default(30).description('minutes after which access tokens expire'),
    // JWT_COOKIE_EXPIRES_IN: Joi.number().required(),
    // SENDGRID_USERNAME: Joi.string().required(),
    // SENDGRID_PASSWORD: Joi.string().required(),
    // EMAIL_FROM: Joi.string().required(),
  })
  .unknown();

// validating the process.env object that contains all the env variables
const {value: envVars, error} = envVarsSchema.prefs({errors: {label: 'key'}}).validate(process.env);

// throw error if the validation fails or results into false
if (error) {
  throw new Error(`Config validation error: ${error.message}`);
}

module.exports = {
  env: envVars.NODE_ENV,
  port: envVars.PORT,
  clientUrl: envVars.FRONTEND_URL, // Used for redirects
  FRONTEND_URL: envVars.FRONTEND_URL,
  // Email configuration
  email: {
    port: envVars.EMAIL_PORT || 465,
    host: envVars.EMAIL_HOST || 'mail.privateemail.com',
    secure: envVars.EMAIL_SECURE !== false, // Default to true
    user: envVars.EMAIL_USER || '<EMAIL>',
    password: envVars.EMAIL_PASSWORD,
    from: envVars.EMAIL_FROM || '<EMAIL>',
  },
  // Firebase configuration
  FIREBASE_API_KEY: envVars.FIREBASE_API_KEY,
  FIREBASE_AUTH_DOMAIN: envVars.FIREBASE_AUTH_DOMAIN,
  FIREBASE_DATABASE_URL: envVars.FIREBASE_DATABASE_URL,
  FIREBASE_PROJECT_ID: envVars.FIREBASE_PROJECT_ID,
  FIREBASE_STORAGE_BUCKET: envVars.FIREBASE_STORAGE_BUCKET,
  FIREBASE_MESSAGING_SENDER_ID: envVars.FIREBASE_MESSAGING_SENDER_ID,
  FIREBASE_APP_ID: envVars.FIREBASE_APP_ID,
  FIREBASE_MEASUREMENT_ID: envVars.FIREBASE_MEASUREMENT_ID,
  serverUrl: envVars.BACKEND_URL,
  jwt: {
    secret: envVars.JWT_SECRET,
    accessExpirationMinutes: envVars.JWT_EXPIRES_IN,
  },
  SENDGRID_USERNAME: envVars.SENDGRID_USERNAME,
  SENDGRID_PASSWORD: envVars.SENDGRID_PASSWORD,
  EMAIL_FROM: envVars.EMAIL_FROM,
  // ZainCash configuration
  zaincash: {
    msisdn: envVars.ZAINCASH_MSISDN,
    secret: envVars.ZAINCASH_SECRET,
    merchantId: envVars.ZAINCASH_MERCHANT_ID,
    lang: envVars.ZAINCASH_LANG || 'en',
  },
  aws: {
      s3: {
          name: envVars.AWS_S3_BUCKET,
          pivateName: envVars.AWS_S3_PRIVATE_BUCKET,
          region: envVars.AWS_S3_REGION,
          accessKeyId: envVars.AWS_S3_ACCESS_KEY_ID,
          secretAccessKey: envVars.AWS_S3_SECRET_ACCESS_KEY,
      }
  },
  do:{
      name: envVars.DO_SPACES_BUCKET,
      pivateName: envVars.AWS_S3_PRIVATE_BUCKET,
      region: envVars.DO_SPACES_REGION,
      accessKeyId: envVars.DO_SPACES_KEY,
      secretAccessKey: envVars.DO_SPACES_SECRET,
      endpoint:envVars.DO_SPACES_ENDPOINT
  },
  mysql: {
      host: envVars.DB_HOST,
      port: envVars.PORT,
      database: envVars.DB_DATABASE,
      username: envVars.DB_USERNAME,
      password: envVars.DB_PASSWORD,
      dialect: envVars.DB_CONNECTION,
  },
  insertDummyCategories: false
};
