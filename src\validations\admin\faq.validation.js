const Joi = require('joi');
const moment = require('moment');

const createFAQ = {
  body: Joi.object().keys({
    question: Joi.string()
      .required()
      .min(5)
      .max(255)
      .messages({
        'string.base': 'Question must be a string',
        'string.empty': 'Question is required',
        'string.min': 'Question must be at least 5 characters long',
        'string.max': 'Question cannot exceed 255 characters',
        'any.required': 'Question is required'
      }),
    answer: Joi.string()
      .required()
      .min(10)
      .max(5000)
      .messages({
        'string.base': 'Answer must be a string',
        'string.empty': 'Answer is required',
        'string.min': 'Answer must be at least 10 characters long',
        'string.max': 'Answer cannot exceed 5000 characters',
        'any.required': 'Answer is required'
      }),
    type: Joi.string()
      .required()
      .valid('Account', 'Service', 'Booking', 'Payment', 'Technical', 'Other')
      .messages({
        'string.base': 'Type must be a string',
        'any.only': 'Type must be one of: Account, Service, Booking, Payment, Technical, Other',
        'any.required': 'Type is required'
      }),
    status: Joi.string()
      .valid('live', 'draft', 'paused')
      .default('draft')
      .messages({
        'string.base': 'Status must be a string',
        'any.only': 'Status must be one of: live, draft, paused'
      }),
    displayOrder: Joi.number()
      .integer()
      .min(0)
      .optional()
      .messages({
        'number.base': 'Display order must be a number',
        'number.integer': 'Display order must be an integer',
        'number.min': 'Display order must be a non-negative number'
      })
  })
};

const deleteFAQ = {
  params: Joi.object().keys({
    faqId: Joi.number().integer().required().messages({
      'number.base': 'FAQ ID must be a number',
      'number.integer': 'FAQ ID must be an integer',
      'any.required': 'FAQ ID is required'
    })
  })
};

const updateFAQ = {
  params: Joi.object().keys({
    faqId: Joi.number().integer().required().messages({
      'number.base': 'FAQ ID must be a number',
      'number.integer': 'FAQ ID must be an integer',
      'any.required': 'FAQ ID is required'
    })
  }),
  body: Joi.object().keys({
    question: Joi.string()
      .min(5)
      .max(255)
      .messages({
        'string.base': 'Question must be a string',
        'string.min': 'Question must be at least 5 characters long',
        'string.max': 'Question cannot exceed 255 characters'
      }),
    answer: Joi.string()
      .min(10)
      .max(5000)
      .messages({
        'string.base': 'Answer must be a string',
        'string.min': 'Answer must be at least 10 characters long',
        'string.max': 'Answer cannot exceed 5000 characters'
      }),
    type: Joi.string()
      .valid('Account', 'Service', 'Booking', 'Payment', 'Technical', 'Other')
      .messages({
        'string.base': 'Type must be a string',
        'any.only': 'Type must be one of: Account, Service, Booking, Payment, Technical, Other'
      }),
    status: Joi.string()
      .valid('live', 'draft', 'paused')
      .messages({
        'string.base': 'Status must be a string',
        'any.only': 'Status must be one of: live, draft, paused'
      })
  }).min(1).messages({
    'object.min': 'At least one field is required for update'
  })
};

const getFAQs = {
  query: Joi.object().keys({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be greater than or equal to 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be greater than or equal to 1',
        'number.max': 'Limit cannot exceed 100',
      }),
     sortBy: Joi.string()
          .optional()
          .default('createdAt:asc')
          .custom((value, helpers) => {
            // Validate sort format: field:direction,field2:direction2
            const sortPattern = /^[\w]+(:(asc|desc))?(,[\w]+(:(asc|desc))?)*$/i;
            if (!sortPattern.test(value)) {
              return helpers.error('string.sortFormat');
            }
    
            // Validate allowed fields
            const allowedFields = ['createdAt', 'updatedAt', 'status', 'type'];
    
            const sortParts = value.split(',');
            for (const part of sortParts) {
              const [field] = part.split(':');
              if (!allowedFields.includes(field)) {
                return helpers.error('string.sortField', {field});
              }
            }
    
            return value;
          })
          .messages({
            'string.sortFormat': 'Invalid sort format. Example: field:asc,field2:desc',
            'string.sortField': '{{#field}} is not a valid sort field',
          }),
    type: Joi.string()
      .valid('Account', 'Service', 'Booking', 'Payment', 'Technical', 'Other')
      .optional()
      .messages({
        'string.base': 'Type must be a string',
        'any.only': 'Type must be one of: Account, Service, Booking, Payment, Technical, Other'
      }),
    status: Joi.string()
      .valid('live', 'draft', 'paused')
      .optional()
      .messages({
        'string.base': 'Status must be a string',
        'any.only': 'Status must be one of: live, draft, paused'
      }),
    search: Joi.string()
      .optional()
      .trim()
      .messages({
        'string.base': 'Search query must be a string',
      }),
    fromDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'From date must be in YYYY-MM-DD format',
      }),
    toDate: Joi.string()
      .optional()
      .custom((value, helpers) => {
        if (!value) return value;

        // Validate date format using Moment.js
        if (!moment(value, 'YYYY-MM-DD', true).isValid()) {
          return helpers.error('string.dateFormat');
        }

        return value;
      })
      .messages({
        'string.dateFormat': 'To date must be in YYYY-MM-DD format',
      }),
  }),
};

module.exports = {
  createFAQ,
  updateFAQ,
  deleteFAQ,
  getFAQs
};

