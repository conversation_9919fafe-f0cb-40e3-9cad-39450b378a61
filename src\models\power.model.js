const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Power = sequelize.define(
  'powers',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    value: {
      type: DataTypes.FLOAT,
      allowNull: false,
    },
    unit: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'kW'
    }
  },
  {
    tableName: 'powers',
    timestamps: false
  }
);

module.exports = Power; 