'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('chargers', 'chargerId', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'UNKNOWN',
    });

    await queryInterface.addColumn('chargers', 'status', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'inactive',
    });

    await queryInterface.addColumn('chargers', 'lastUpdated', {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn('chargers', 'lastHeartbeat', {
      type: Sequelize.DATE,
      defaultValue: Sequelize.NOW,
    });

    await queryInterface.addColumn('chargers', 'lastMeterValue', {
      type: Sequelize.JSONB, 
      allowNull: true,
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('chargers', 'chargerId');
    await queryInterface.removeColumn('chargers', 'status');
    await queryInterface.removeColumn('chargers', 'lastUpdated');
    await queryInterface.removeColumn('chargers', 'lastHeartbeat');
    await queryInterface.removeColumn('chargers', 'lastMeterValue');
  }
};
