'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Fetch station IDs
      const stations = await queryInterface.sequelize.query(
        `SELECT id FROM stations;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      // Fetch amenity IDs
      const amenities = await queryInterface.sequelize.query(
        `SELECT id FROM amenities;`,
        { type: queryInterface.sequelize.QueryTypes.SELECT }
      );

      // Log results
      console.log('Fetched Stations:', stations);
      console.log('Fetched Amenities:', amenities);

      if (!stations.length || !amenities.length) {
        console.warn('No stations or amenities found. Skipping seeding.');
        return;
      }

      // Prepare data for insertion
      const stationAmenities = [];
      for (const station of stations) {
        for (const amenity of amenities) {
          stationAmenities.push({
            stationId: station.id,
            amenityId: amenity.id
          });
        }
      }

      console.log('Inserting station_amenities:', stationAmenities);

      // Bulk insert
      await queryInterface.bulkInsert('station_amenities', stationAmenities, {});

    } catch (error) {
      console.error('Error seeding station_amenities:', error);
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      await queryInterface.bulkDelete('station_amenities', null, {});
    } catch (error) {
      console.error('Error rolling back  station_amenities:', error);
    }
  }
};
