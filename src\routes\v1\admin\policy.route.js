const express = require('express');
const router = express.Router();
const { adminAuth, superAdminAuth } = require('../../../middlewares/adminAuth');
const policyController = require('../../../controllers/admin/policy.controller');
const validate = require('../../../middlewares/validate');
const policyValidation = require('../../../validations/admin/policy.validation');

/**
 * @api {post} v1/admin/policies Create a new policy
 * @apiDescription Create a new policy document (privacy, terms, etc.)
 * @apiName CreatePolicy
 * @apiGroup AdminPolicy
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  type        Policy type (privacy, subscription, penalty, cancellation)
 * @apiParam  {String}  userType    Target user type (all, user, vendor)
 * @apiParam  {String}  title       Policy title
 * @apiParam  {String}  description Policy content/description
 * @apiParam  {Boolean} [isActive=true] Whether the policy is active
 *
 * @apiSuccess {Object} policy Created policy
 */
router.post(
  '/',
  adminAuth,
  validate(policyValidation.createPolicy),
  policyController.createPolicy
);

/**
 * @api {get} v1/admin/policies Get all policies
 * @apiDescription Get all policies with pagination
 * @apiVersion 1.0.0
 * @apiName GetPolicies
 * @apiGroup AdminPolicy
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  [type]      Filter by policy type
 * @apiParam  {String}  [userType]  Filter by target user type
 * @apiParam  {Number}  [page=1]    Page number
 * @apiParam  {Number}  [limit=10]  Number of items per page
 *
 * @apiSuccess {Object[]} policies List of policies
 * @apiSuccess {Object} pagination Pagination info
 */
router.get(
  '/',
  adminAuth,
  validate(policyValidation.getPolicies),
  policyController.getPolicies
);

/**
 * @api {get} v1/admin/policies/:policyId Get policy by ID
 * @apiDescription Get policy details by ID
 * @apiVersion 1.0.0
 * @apiName GetPolicy
 * @apiGroup AdminPolicy
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  policyId    Policy ID
 *
 * @apiSuccess {Object} policy Policy details
 */
router.get(
  '/:policyId',
  adminAuth,
  validate(policyValidation.getPolicy),
  policyController.getPolicy
);

/**
 * @api {put} v1/admin/policies/:policyId Update policy
 * @apiDescription Update an existing policy
 * @apiVersion 1.0.0
 * @apiName UpdatePolicy
 * @apiGroup AdminPolicy
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  policyId    Policy ID
 * @apiParam  {String}  [title]     Policy title
 * @apiParam  {String}  [description] Policy content/description
 * @apiParam  {Boolean} [isActive]  Whether the policy is active
 *
 * @apiSuccess {Object} policy Updated policy
 */
router.put(
  '/:policyId',
  adminAuth,
  validate(policyValidation.updatePolicy),
  policyController.updatePolicy
);

/**
 * @api {patch} v1/admin/policies/:policyId/toggle-status Toggle policy status
 * @apiDescription Toggle a policy's active status
 * @apiVersion 1.0.0
 * @apiName TogglePolicyStatus
 * @apiGroup AdminPolicy
 * @apiPermission admin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  policyId    Policy ID
 *
 * @apiSuccess {Object} policy Updated policy
 */
router.patch(
  '/:policyId/toggle-status',
  adminAuth,
  validate(policyValidation.togglePolicyStatus),
  policyController.togglePolicyStatus
);

/**
 * @api {delete} v1/admin/policies/:policyId Delete policy
 * @apiDescription Delete a policy
 * @apiVersion 1.0.0
 * @apiName DeletePolicy
 * @apiGroup AdminPolicy
 * @apiPermission superAdmin
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam  {String}  policyId    Policy ID
 *
 * @apiSuccess {Object} message Success message
 */
router.delete(
  '/:policyId',
  superAdminAuth,
  validate(policyValidation.deletePolicy),
  policyController.deletePolicy
);

module.exports = router;