const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const {
  getUserNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  getUnreadNotificationCount
} = require('../microservices/notification.service');

/**
 * Get user notifications with pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getNotifications = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { page, limit } = req.query;

    const result = await getUserNotifications(userId, page, limit);
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'Notification fetched successfully',
    });
});

/**
 * Mark a notification as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const markAsRead = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { notificationId } = req.params;

    const result = await markNotificationAsRead(notificationId, userId);
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'Notification marked as read successfully',
    });
});

/**
 * Mark all notifications as read
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const markAllAsRead = catchAsync(async (req, res) => {
    const userId = req.user.id;

    const result = await markAllNotificationsAsRead(userId);
    res.status(httpStatus.OK).json({
      status: true,
      data: result,
      message: 'All notifications marked as read successfully',
    });
});

/**
 * Get unread notification count
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
const getUnreadCount = catchAsync(async (req, res) => {
    const userId = req.user.id;

    const result = await getUnreadNotificationCount(userId);
    res.status(httpStatus.OK).json({
      status: true,
      data: { count: result },
      message: 'Unread notification count fetched successfully',
    });
});

module.exports = {
  getNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount
}; 