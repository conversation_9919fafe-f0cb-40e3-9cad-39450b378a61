const moment = require('moment');

/**
 * Generate HTML template for ticket response email
 * @param {Object} ticket - The ticket object
 * @param {string} adminResponse - Admin's response to the ticket
 * @returns {string} HTML email template
 */
const generateTicketResponseEmail = (ticket, adminResponse) => {
  return `
    <div style="font-family: Arial, sans-serif; color: #333; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4a90e2;">Your Support Ticket Has Been Answered</h2>
      
      <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p><strong>Ticket Number:</strong> #${ticket.ticketNo}</p>
        <p><strong>Date Submitted:</strong> ${moment(ticket.createdAt).format('MMMM D, YYYY [at] h:mm A')}</p>
      </div>
      
      <div style="margin: 20px 0;">
        <h3>Your Original Query:</h3>
        <p style="background-color: #f9f9f9; padding: 10px; border-left: 4px solid #ddd;">${ticket.query}</p>
      </div>
      
      <div style="margin: 20px 0;">
        <h3>Our Response:</h3>
        <p style="background-color: #f0f7ff; padding: 10px; border-left: 4px solid #4a90e2;">${adminResponse}</p>
      </div>
      
      <p>If you have any further questions, please don't hesitate to reply to this email or create a new support ticket.</p>
      
      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
        <p style="color: #777; font-size: 14px;">Thank you for contacting our support team.</p>
        <p style="color: #777; font-size: 14px;">The EVIQ Support Team</p>
      </div>
    </div>
  `;
};

module.exports = {
  generateTicketResponseEmail
};