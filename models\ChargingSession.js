const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const ChargingSession = sequelize.define(
    'charging_sessions',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      sessionId: {
        type: DataTypes.UUID,
        defaultValue: DataTypes.UUIDV4,
        unique: true,
        allowNull: false,
      },
      chargerId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'chargers',
          key: 'id'
        }
      },
      connectorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'charger_connectors',
          key: 'id'
        }
      },
      transactionId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'OCPP Transaction ID'
      },
      idTag: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: 'RFID tag or user identifier'
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'User ID if authenticated'
      },
      status: {
        type: DataTypes.ENUM('Starting', 'Active', 'Suspended', 'Stopping', 'Completed', 'Faulted'),
        allowNull: false,
        defaultValue: 'Starting',
      },
      startTime: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      endTime: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      startMeterValue: {
        type: DataTypes.DECIMAL(15, 3),
        allowNull: true,
        comment: 'Starting energy meter value in Wh'
      },
      endMeterValue: {
        type: DataTypes.DECIMAL(15, 3),
        allowNull: true,
        comment: 'Ending energy meter value in Wh'
      },
      energyConsumed: {
        type: DataTypes.DECIMAL(15, 3),
        allowNull: true,
        comment: 'Total energy consumed in Wh'
      },
      duration: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Session duration in seconds'
      },
      cost: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Total cost of the session'
      },
      stopReason: {
        type: DataTypes.ENUM('EVDisconnected', 'PowerLoss', 'EmergencyStop', 'Other', 'Remote', 'Local', 'DeAuthorized'),
        allowNull: true,
      },
      lastActivity: {
        type: DataTypes.DATE,
        allowNull: true,
      }
    },
    {
      tableName: 'charging_sessions',
      timestamps: true,
      indexes: [
        { fields: ['sessionId'] },
        { fields: ['chargerId'] },
        { fields: ['connectorId'] },
        { fields: ['transactionId'] },
        { fields: ['idTag'] },
        { fields: ['userId'] },
        { fields: ['status'] },
        { fields: ['startTime'] },
        { fields: ['endTime'] }
      ]
    }
  );

  return ChargingSession;
};
