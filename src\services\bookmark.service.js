const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { Bookmark, Station, Charger, Connector, Amenity } = require('../models');


const addBookmark = async (userId, stationId) => {
    const station = await Station.findByPk(stationId);
    if (!station) throw new ApiError(httpStatus.NOT_FOUND, 'Station not found');

    const [bookmark, created] = await Bookmark.findOrCreate({
        where: { userId, stationId }
    });

    if (!created) throw new ApiError(httpStatus.BAD_REQUEST, 'Station already bookmarked');

    return bookmark;
};

const removeBookmark = async (userId, stationId) => {

    const deletedCount = await Bookmark.destroy({
        where: { userId, stationId }
    });

    if (!deletedCount) throw new ApiError(httpStatus.NOT_FOUND, 'Bookmark not found');
}

const getBookmarks = async (userId, page = 1, limit = 10) => {
    const offset = (page - 1) * limit;

    const { rows: bookmarks, count: totalCount } = await Bookmark.findAndCountAll({
        where: { userId },
        // attributes: { exclude: ["createdAt", "updatedAt"] },
        limit,
        offset,
        order: [['createdAt', 'DESC']], // Sort by latest bookmarks
        include: [
            {
                model: Station,
                attributes: { exclude: ["createdAt", "updatedAt", "operationalSchedule", "images"] },
                include: [
                    { model: Charger, include: [{ model: Connector }] },
                    { model: Amenity, as: "amenities",
                        attributes: { exclude: ["createdAt", "updatedAt"] }, through: { attributes: [] } }
                ]
            }
        ]
    });

    return {
        bookmarks,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        currentPage: page,
    };
}
module.exports = {
    addBookmark,
    removeBookmark,
    getBookmarks
}