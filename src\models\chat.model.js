const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Chat = sequelize.define(
  'Chat',
  {
    id: {
      type: DataTypes.INTEGER, 
      autoIncrement: true,    
      primaryKey: true,
    },
    userId: {
      type: DataTypes.INTEGER, 
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
    },
    bookingId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'bookings',
        key: 'id',
      },
    },
    status: {
      type: DataTypes.ENUM('active', 'closed'),
      defaultValue: 'active',
    },
    topic: {
      type: DataTypes.ENUM('booking', 'charging_station', 'penalty'),
      allowNull: false,
    },
    firebaseTopicId: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    lastMessageAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
  },
  {
    tableName: 'chats',
    timestamps: true,
  }
);

module.exports = Chat;