const httpStatus = require('http-status');
const catchAsync = require('../utils/catchAsync');
const ApiError = require('../utils/ApiError');
const { paymentService } = require('../services');
const logger = require('../config/logger');
const config = require('../config/config');

/**
 * Initialize a payment for wallet top-up using the specified payment provider
 */
const initializePayment = catchAsync(async (req, res) => {
  const userId = req.user.id;
  // const userId = 21;
  const { provider, walletId, amount } = req.body;

  logger.info(`Payment initialization request - userId: ${userId}, provider: ${provider}, walletId: ${walletId}, amount: ${amount}`);

  const paymentData = {
    userId,
    provider,
    walletId,
    amount
  };

  const result = await paymentService.initializePayment(paymentData);

  logger.info(`Payment initialized - provider: ${provider}, orderId: ${result.orderId}`);

  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Payment initialized successfully',
  });
});

/**
 * Handle payment callback from Zaincash
 */
const handleZaincashCallback = catchAsync(async (req, res) => {
  const { token } = req.query;

  if (!token) {
    logger.error('ZainCash callback received without token');
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token is required');
  }

  logger.info(`ZainCash payment callback received with token`);

  try {
    const result = await paymentService.handlePaymentCallback('zaincash', { token });

    logger.info(`ZainCash payment verified and processed - walletId: ${result.walletId}, amount: ${result.amount}`);

    // Redirect to the frontend with success status
    res.redirect(`${config.serverUrl}/wallet/payment/success?amount=${result.amount}&walletId=${result.walletId}`);
  } catch (error) {
    logger.error(`ZainCash payment verification failed: ${error.message}`);

    // Redirect to the frontend with error status
    res.redirect(`${config.serverUrl}wallet/payment/error?message=${encodeURIComponent(error.message)}`);
  }
});

/**
 * Handle payment callback from FIB
 *
 * Expected format:
 * {
 *   id: "payment_id",
 *   status: { ... payment status object ... }
 * }
 *
 * Response codes:
 * - 202 Accepted: Successfully processed
 * - 406 Not Acceptable: Invalid data or cannot process
 * - 500 Internal Server Error: Server error
 */
const handleFibCallback = catchAsync(async (req, res) => {
  // FIB sends payment status updates as POST requests with JSON body
  const { id, status } = req.body;

  if (!id || !status) {
    logger.error('FIB callback received with invalid data', { body: req.body });
    return res.status(httpStatus.NOT_ACCEPTABLE).send();
  }

  logger.info(`FIB payment callback received - paymentId: ${id}, status: ${status.status}`);

  try {
    // Transform the data to match our internal format
    const paymentData = {
      paymentId: id,
      ...status
    };

    const result = await paymentService.handlePaymentCallback('fib', paymentData);

    logger.info(`FIB payment verified and processed - walletId: ${result.walletId}, amount: ${result.amount}`);

    // Return 202 Accepted as per FIB documentation
    return res.status(httpStatus.ACCEPTED).send();
  } catch (error) {
    logger.error(`FIB payment verification failed: ${error.message}`);

    if (error instanceof ApiError && error.statusCode === httpStatus.BAD_REQUEST) {
      // Return 406 Not Acceptable for validation/business rule errors
      return res.status(httpStatus.NOT_ACCEPTABLE).send();
    }

    // Return 500 Internal Server Error for server errors
    return res.status(httpStatus.INTERNAL_SERVER_ERROR).send();
  }
});

/**
 * Handle redirect from FIB app after payment
 */
const handleFibRedirect = catchAsync(async (req, res) => {
  const { paymentId } = req.query;

  if (!paymentId) {
    logger.error('FIB redirect received without payment ID');
    return res.redirect(`${config.serverUrl}/wallet/payment/error?message=${encodeURIComponent('Missing payment ID')}`);
  }

  logger.info(`FIB payment redirect received - paymentId: ${paymentId}`);

  try {
    const result = await paymentService.handlePaymentCallback('fib', { paymentId });

    logger.info(`FIB payment verified and processed - walletId: ${result.walletId}, amount: ${result.amount}`);

    // Redirect to the frontend with success status
    return res.redirect(`${config.serverUrl}wallet/payment/success?amount=${result.amount}&walletId=${result.walletId}`);
  } catch (error) {
    logger.error(`FIB payment verification failed: ${error.message}`);

    // Redirect to the frontend with error status
    return res.redirect(`${config.clientUrl}wallet/payment/error?message=${encodeURIComponent(error.message)}`);
  }
});

module.exports = {
  initializePayment,
  handleZaincashCallback,
  handleFibCallback,
  handleFibRedirect
};
