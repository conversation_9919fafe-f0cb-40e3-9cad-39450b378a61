const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const  helpCenterService  = require('../../services/admin/helpCenter.service');


/**
 * Get all help center tickets with filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Paginated tickets
 */
const getTickets = catchAsync(async (req, res) => {
  const { 
    page, 
    limit, 
    sortBy, 
    search,
    fromDate, 
    toDate 
  } = req.query;
  
  const result = await helpCenterService.getTickets({
    page,
    limit,
    sortBy,
    search,
    fromDate,
    toDate
  });
  
  res.status(httpStatus.OK).json({
    status: true,
    data: result.tickets,
    pagination: result.pagination,
    message: 'Support tickets fetched successfully'
  });
});

/**
 * Delete a help center ticket
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message
 */
const deleteTicket = catchAsync(async (req, res) => {
  const { ticketId } = req.params;
  
  await helpCenterService.deleteTicket(ticketId);
  
  res.status(httpStatus.OK).json({
    status: true,
    message: 'Support ticket deleted successfully'
  });
});

/**
 * Answer a help center ticket
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated ticket
 */
const answerTicket = catchAsync(async (req, res) => {
  const { ticketId } = req.params;
  const { response } = req.body;
  const adminId = req.admin.id;
  
  const updatedTicket = await helpCenterService.answerTicket(ticketId, {
    response,
    adminId
  });
  
  res.status(httpStatus.OK).json({
    status: true,
    data: updatedTicket,
    message: 'Support ticket answered successfully'
  });
});

/**
 * Get help center statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Statistics data
 */
const getStatistics = catchAsync(async (req, res) => {
  const statistics = await helpCenterService.getHelpCenterStatistics();
  
  res.status(httpStatus.OK).json({
    status: true,
    data: statistics,
    message: 'Help center statistics fetched successfully'
  });
});

module.exports = {
  getTickets,
  deleteTicket,
  answerTicket,
  getStatistics
};


