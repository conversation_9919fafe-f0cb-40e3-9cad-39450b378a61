const express = require('express');
const router = express.Router();
const {adminAuth} = require('../../../middlewares/adminAuth');
const dashboardController = require('../../../controllers/admin/dashboard.controller');
const dashboardValidation = require('../../../validations/admin/dashboard.validation');
const validate = require('../../../middlewares/validate');

/**
 * @api {get} v1/admin/dashboard/summary Get dashboard summary
 * @apiDescription Get dashboard summary
 * @apiName GetDashboardSummary
 * @apiSuccess {Object} summary Dashboard summary
 */
router.route('/summary').get(adminAuth, dashboardController.getDashboardKPIs);

/**
 * @api {get} v1/admin/dashboard/revenue-chart Get revenue chart data
 * @apiDescription Get monthly revenue data for the current year
 * @apiName GetRevenueChartData
 * @apiSuccess {Object} data Revenue chart data
 */
router.route('/revenue-chart').get(adminAuth,validate(dashboardValidation.getRevenueChartData), dashboardController.getRevenueChartData);

/**
 * @api {get} v1/admin/dashboard/overview-chart Get overview chart data
 * @apiDescription Get daily data for stations, vendors, and users for the current month
 * @apiName GetOverviewChartData
 * @apiSuccess {Object} data Overview chart data
 */
router.route('/overview-chart').get(adminAuth, adminAuth,validate(dashboardValidation.getRevenueChartData),dashboardController.getOverviewChartData);
  
/**
 * @api {get} v1/admin/dashboard/users-chart Get users chart data
 * @apiDescription Get user growth data with monthly/daily breakdown
 * @apiName GetUsersChartData
 * @apiGroup AdminDashboard
 * @apiSuccess {Object} data Users chart data
 */
router.route('/users-chart').get(adminAuth, validate(dashboardValidation.getUsersChartData), dashboardController.getUsersChartData);

/**
 * @api {get} v1/admin/dashboard/pending-stations Get pending approval stations
 * @apiDescription Get list of stations pending approval
 * @apiName GetPendingStations
 * @apiGroup AdminDashboard
 * @apiSuccess {Object} data Pending stations list
 */
router.route('/pending-stations').get(adminAuth, dashboardController.getPendingStations);

/**
 * @api {get} v1/admin/dashboard/bookings/stats Get booking statistics
 * @apiDescription Get statistics about bookings including percentages of confirmed, cancelled by user, and cancelled by vendor
 * @apiName GetBookingStats
 *
 * @apiHeader {String} Authorization Admin's access token
 *
 * @apiParam {String} [timeFilter=all] Time filter (thisMonth, thisYear)
 *
 * @apiSuccess {Object} data Booking statistics
 * @apiSuccess {Number} data.totalBookings Total number of bookings
 * @apiSuccess {Number} data.confirmedPercentage Percentage of confirmed bookings
 * @apiSuccess {Number} data.cancelledByUserPercentage Percentage of bookings cancelled by users
 * @apiSuccess {Number} data.cancelledByVendorPercentage Percentage of bookings cancelled by vendors
 * @apiSuccess {Object} data.counts Raw counts of bookings by status
 * @apiSuccess {Boolean} status Success status
 * @apiSuccess {String} message Success message
 */
router.get(
  '/bookings/stats',
  adminAuth,
   validate(dashboardValidation.getBookingStats),
  dashboardController.getBookingStats
);
module.exports = router;
