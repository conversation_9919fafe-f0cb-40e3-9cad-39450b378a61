const moment = require('moment');
const { PaymentTransaction, Booking, VendorSubscription } = require('../../models');
const { TRANSACTION_TYPE, PAYMENT_STATUS, PAYMENT_PURPOSE, BOOKING_STATUS } = require('../../constants');
const { Op, fn, col, literal } = require('sequelize');
const { getYearStartAndEnd, getGrowthPercentage, countRecordsInYear } = require('../../utils/analytics.utils');
const { sequelize } = require('../../config/database');
const logger = require('../../config/logger');

/**
 * Calculate total revenue from payment transactions for a year
 * @param {number} year - Year to calculate revenue for
 * @returns {Promise<number>} Total revenue
 */
const calculateAnnualRevenue = async (year) => {
  const { startDate, endDate } = getYearStartAndEnd(year);
  const result = await PaymentTransaction.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('amount')), 0), 'totalRevenue']],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Calculate booking revenue for a year
 * @param {number} year - Year to calculate revenue for
 * @returns {Promise<number>} Booking revenue
 */
const calculateBookingRevenue = async (year) => {
  const { startDate, endDate } = getYearStartAndEnd(year);
  const result = await Booking.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('totalCost')), 0), 'totalRevenue']],
    where: {
      paymentStatus: PAYMENT_STATUS.PAID,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Calculate subscription revenue for a year from payment transactions
 * @param {number} year - Year to calculate revenue for
 * @returns {Promise<number>} Subscription revenue
 */
const calculateSubscriptionRevenue = async (year) => {
  const { startDate, endDate } = getYearStartAndEnd(year);
  const result = await PaymentTransaction.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('amount')), 0), 'totalRevenue']],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS,
      purpose: PAYMENT_PURPOSE.SUBSCRIPTION,
      createdAt: {
        [Op.between]: [startDate, endDate]
      }
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Calculate overall (all-time) total revenue from payment transactions
 * @returns {Promise<number>} Total all-time revenue
 */
const calculateOverallTotalRevenue = async () => {
  const result = await PaymentTransaction.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('amount')), 0), 'totalRevenue']],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Calculate overall (all-time) booking revenue
 * @returns {Promise<number>} Total all-time booking revenue
 */
const calculateOverallBookingRevenue = async () => {
  const result = await Booking.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('totalCost')), 0), 'totalRevenue']],
    where: {
      paymentStatus: PAYMENT_STATUS.PAID
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Calculate overall (all-time) subscription revenue
 * @returns {Promise<number>} Total all-time subscription revenue
 */
const calculateOverallSubscriptionRevenue = async () => {
  const result = await PaymentTransaction.findOne({
    attributes: [[fn('COALESCE', fn('SUM', col('amount')), 0), 'totalRevenue']],
    where: {
      transactionType: TRANSACTION_TYPE.CREDIT,
      status: PAYMENT_STATUS.SUCCESS,
      purpose: PAYMENT_PURPOSE.SUBSCRIPTION
    }
  });
  return parseFloat(result?.get('totalRevenue') || 0);
};

/**
 * Get revenue KPIs including total revenue, booking revenue, and subscription revenue
 * @returns {Promise<Object>} Revenue KPIs with growth percentages
 */
const getRevenueKPIs = async () => {
  logger.info('Fetching revenue KPIs');
  
  const currentYear = moment().year();
  const previousYear = currentYear - 1;
  
  // Run all queries in parallel for better performance
  const [
    overallTotalRevenue,
    currentTotalRevenue, 
    previousTotalRevenue,
    overallBookingRevenue,
    currentBookingRevenue, 
    previousBookingRevenue,
    overallSubscriptionRevenue,
    currentSubscriptionRevenue, 
    previousSubscriptionRevenue
  ] = await Promise.all([
    calculateOverallTotalRevenue(),
    calculateAnnualRevenue(currentYear),
    calculateAnnualRevenue(previousYear),
    calculateOverallBookingRevenue(),
    calculateBookingRevenue(currentYear),
    calculateBookingRevenue(previousYear),
    calculateOverallSubscriptionRevenue(),
    calculateSubscriptionRevenue(currentYear),
    calculateSubscriptionRevenue(previousYear)
  ]);
  
  return {
    totalRevenue: {
      overall: overallTotalRevenue,
      current: currentTotalRevenue,
      yearlyGrowth: getGrowthPercentage(currentTotalRevenue, previousTotalRevenue)
    },
    bookingRevenue: {
      overall: overallBookingRevenue,
      current: currentBookingRevenue,
      yearlyGrowth: getGrowthPercentage(currentBookingRevenue, previousBookingRevenue)
    },
    subscriptionRevenue: {
      overall: overallSubscriptionRevenue,
      current: currentSubscriptionRevenue,
      yearlyGrowth: getGrowthPercentage(currentSubscriptionRevenue, previousSubscriptionRevenue)
    }
  };
};

module.exports = {
  getRevenueKPIs,
  calculateAnnualRevenue,
  calculateBookingRevenue,
  calculateSubscriptionRevenue
};


