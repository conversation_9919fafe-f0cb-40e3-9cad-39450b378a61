'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('subscription_plans', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Name of the subscription plan (e.g., "Basic", "Premium", "Pro")',
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
        comment: 'Detailed description of the subscription plan benefits',
      },
      price: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
        validate: {
          min: 0,
        },
        comment: 'Price of the subscription plan',
      },
      durationValue: {
        type: Sequelize.INTEGER,
        allowNull: false,
        comment: 'Length of the subscription period (e.g., 1 for monthly, 3 for quarterly)',
      },
      durationType: {
        type: Sequelize.ENUM('month', 'year'),
        allowNull: false,
        comment: 'Unit of duration (e.g., "month", "year")',
      },
      currency: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'USD',
        comment: 'Currency of the price (e.g., USD, EUR)',
      },
      benefits: {
        type: Sequelize.JSON,
        allowNull: false,
        defaultValue: [],
        comment: 'Array of benefits included in the plan',
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether the plan is currently active and available for purchase',
      },
      displayOrder: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Order in which plans should be displayed (lower number = higher priority)',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
      }
    });

    // Add indexes
    await queryInterface.addIndex('subscription_plans', ['isActive']);
    await queryInterface.addIndex('subscription_plans', ['displayOrder']);

  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.dropTable('subscription_plans');
  }
};
