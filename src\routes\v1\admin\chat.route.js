const express = require('express');
const router = express.Router();
const { adminAuth } = require('../../../middlewares/adminAuth');
const  chatController  = require('../../../controllers/admin/chat.controller');

// Admin chat routes
router.post('/message', adminAuth, chatController.sendAdminMessage);
router.get('/:chatId/messages', adminAuth, chatController.getAdminMessages);
router.post('/close/:chatId', adminAuth, chatController.closeChat);

module.exports = router; 