const { User } = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const verifyOldPassword = require('../utils/authUtils').verifyOldPassword;
const admin = require('firebase-admin');

async function create(data) {
  try {
    const user = await User.create(data);
    return user;
  } catch (error) {
    // Handle error appropriately (log it, throw a custom error, etc.)
    console.error('Error in createCompany:', error);
    throw error;
  }
}

async function getUserByFirebaseUId(id) {
  try {
    const user = await User.findOne({ where: { firebaseUid: id } });
    return user;
  } catch (error) {
    // Handle error appropriately (log it, throw a custom error, etc.)
    console.error('Error in getUserByFirebaseUId:', error);
    throw error;
  }
}

async function changePassword(userId, email, oldPassword, newPassword) {

  const user = await User.findByPk(userId);
  if (!user) {
    throw new ApiError(httpStatus.NOT_FOUND,'User not found');
  }
  
  // Verify old password using Firebase Authentication
  const isPasswordValid = await verifyOldPassword(email, oldPassword);

  if (!isPasswordValid) {
    throw new ApiError(httpStatus.UNAUTHORIZED, 'Invalid old password');
  }
  // Update password in Firebase
  await admin.auth().updateUser(user.firebaseUid, { password: newPassword });
  return user;
}

module.exports = {
  create,
  getUserByFirebaseUId,
  changePassword
};
