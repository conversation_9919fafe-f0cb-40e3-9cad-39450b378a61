const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');
const { Op } = require('sequelize');

const UserVehicle = sequelize.define(
  'userVehicles',
  {
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    brandId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'vehicle_brands',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    brandName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    modelId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'vehicle_models',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    },
    modelName: {
      type: DataTypes.STRING,
      allowNull: true,
    },
    manufacturingUnitId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'manufacturing_units',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    batteryCapacityId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'battery_capacities',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      allowNull: true,  // Allow it to be NULL initially
    },
  },
  {
    tableName: 'user_vehicles',
    timestamps: true,
  }
);

// Ensure the first vehicle for a user is primary by default
UserVehicle.beforeCreate(async (vehicle, options) => {
  const existingPrimaryVehicle = await UserVehicle.findOne({
    where: { userId: vehicle.userId, isPrimary: true },
  });

  // If no primary vehicle exists, set the new vehicle as primary
  if (!existingPrimaryVehicle) {
    vehicle.isPrimary = true;
  } else {
    vehicle.isPrimary = false;  // If a primary vehicle exists, set the new vehicle as non-primary
  }
});

// Ensure only one primary vehicle per user
UserVehicle.beforeUpdate(async (vehicle, options) => {
  if (vehicle.changed('isPrimary') && vehicle.isPrimary) {
    // Set all other vehicles of the same user to non-primary
    await UserVehicle.update(
      { isPrimary: false },
      {
        where: {
          userId: vehicle.userId,
          id: { [Op.ne]: vehicle.id }, // Exclude the current vehicle
        },
      }
    );
  }
});


UserVehicle.paginate = paginate();

module.exports = UserVehicle;



