const {User} = require('../models');
const httpStatus = require('http-status');
const ApiError = require('../utils/ApiError');
const { fileUploadService } = require('../microservices');
const admin = require('firebase-admin');
;

const userValidator = async user => {
    if (!user) {
      throw new ApiError(httpStatus.NOT_FOUND, 'User not found.');
    } else if (user.isDeleted) {
      throw new ApiError(httpStatus.FORBIDDEN, 'User account has been deleted.');
    } else if (user.isBlocked) {
      throw new ApiError(httpStatus.FORBIDDEN, 'User has been blocked.');
    }
  };
const updateUserById = async (id, newDetails, file, isAdmin = false) => {
    const user = await User.findByPk(id);
    await userValidator(user);
    
    if (file) {
        const [profilePic] = await fileUploadService.s3Upload([file], 'userProfileImages').catch(err => {
          console.log(err);
          throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to upload userProfileImage picture');
        });
        if (user.profilePicKey) {
          const oldPicKey = user.profilePicKey;
          await fileUploadService
            .s3Delete(oldPicKey)
            .catch(err => console.log('Failed to delete thumbnail picture', oldPicKey));
        }
        console.log(profilePic)
        newDetails.profilePicKey = profilePic.key;
        newDetails.profilePicUrl = profilePic.url;
      }
    return await user.update(newDetails);
};

const getUserDetails = async (id) => {
  const user = await User.findByPk(id);
  return user;
}

/**
 * Create a user in the database
 * @param {Object} firebaseUser - Firebase user record
 * @param {Object} userData - Additional user data
 * @returns {Promise<User>} - Created user
 */
const createUser = async (firebaseUser, userData) => {
  try {
    const userObj = {
      email: firebaseUser.email,
      firebaseUid: firebaseUser.uid,
      profilePicUrl: firebaseUser.photoURL || null,
      phone: firebaseUser.phoneNumber || null,
      isEmailVerified: firebaseUser.emailVerified,
      firebaseSignInProvider: 'password',
      ...userData
    };
    
    const user = await User.create(userObj);
    return user;
  } catch (error) {
    console.error('Error creating user in database:', error);
    
    // If there's an error creating the user in the database, delete the Firebase user
    if (firebaseUser.uid) {
      try {
        await admin.auth().deleteUser(firebaseUser.uid);
      } catch (deleteError) {
        console.error('Error deleting Firebase user after database error:', deleteError);
      }
    }
    
    throw new ApiError(httpStatus.BAD_REQUEST, error.message || 'Failed to create user in database');
  }
};



module.exports = {
  updateUserById,
  getUserDetails,
  createUser,
 
};
  
