const paginate = () => {
  return async (model, filters, options) => {
    try {
      // Defining pagination parameters
      const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' } = options;
      const offset = (page - 1) * limit;

      // Define eager loading options based on filter conditions
      const include = [];
      Object.keys(filters).forEach(key => {
        if (key !== 'postPopulateFilters') {
          // Assuming filter key matches with associated model name
          include.push({ model: sequelize.models[key] });
        }
      });

      // Define order conditions
      const orderConditions = [[sortBy, sortOrder.toUpperCase()]];

      // Apply pagination, filtering, and eager loading
      const result = await model.findAndCountAll({
        where: filters,
        include: include,
        order: orderConditions,
        offset,
        limit,
      });

      // Calculate total pages
      const totalPages = Math.ceil(result.count / limit);

      return {
        page,
        limit,
        results: result.rows,
        totalPages,
        totalResults: result.count,
      };
    } catch (error) {
      throw error;
    }
  };
};

module.exports = { paginate };
