const { HelpCenterTicket } = require('../models');
const { Op } = require('sequelize');

/**
 * Generate a unique ticket number
 * @returns {Promise<string>} Unique ticket number in format #XXX-XXXX
 */
const generateTicketNumber = async () => {
  // Get current date
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  let newTicketNo;

  // Format: #YYMM-XXXX where XXXX is sequential
  const prefix = `#${year}${month}-`;

  // Find the highest ticket number with this prefix
  const highestTicket = await HelpCenterTicket.findOne({
    where: {
      ticketNo: {
        [Op.like]: `${prefix}%`
      }
    },
    order: [['ticketNo', 'DESC']],
    raw: true
  });

  if (highestTicket) {
    const lastNumber = parseInt(highestTicket.ticketNo.split('-')[1], 10);
    const nextNumber = (lastNumber + 1).toString().padStart(4, '0');
    newTicketNo = `${prefix}${nextNumber}`;
  } else {
    newTicketNo = `${prefix}0001`; // Start fresh
  }

  return newTicketNo;
};

module.exports = {
  generateTicketNumber
};