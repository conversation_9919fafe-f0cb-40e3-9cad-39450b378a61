const { DataTypes } = require('sequelize');
const { sequelize } = require('../config/database');

const Notification = sequelize.define('Notification', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
    },
    userId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id',
      },
      comment: 'User ID for user-specific notifications, null for admin notifications',
    },
    isAdminNotification: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      comment: 'Flag to indicate if this is an admin notification',
    },
    title: {
      type: DataTypes.STRING,
      allowNull: false,
    },
    body: {
      type: DataTypes.TEXT,
      allowNull: false,
    },
    type: {
      type: DataTypes.STRING,
      allowNull: false,
      comment: 'Type of notification (e.g., chat, booking, system)',
    },
    referenceId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: 'ID of the related entity (e.g., chatId, bookingId)',
    },
    referenceType: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Type of the related entity (e.g., chat, booking)',
    },
    isRead: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },
    data: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: 'Additional data related to the notification',
    },
    firebaseMessageId: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'ID of the Firebase message if applicable',
    },
    topic: {
      type: DataTypes.STRING,
      allowNull: true,
      comment: 'Firebase topic this notification was sent to',
    },
    recipientType: {
      type: DataTypes.ENUM('user', 'admin', 'both'),
      defaultValue: 'user',
      comment: 'Type of recipient (user, admin, or both)',
    }
}, {
    timestamps: true,
    tableName: 'notifications',
});

module.exports = Notification; 