'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('users', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      phone: {
        type: Sequelize.STRING,
        allowNull: true
      },
      email: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          isEmail: true
        }
      },
      profilePicKey: {
        type: Sequelize.STRING,
        allowNull: true
      },
      profilePicUrl: {  // Add with correct length from the start
        type: Sequelize.STRING(2048),
        allowNull: true,
      },
      dob: {
        type: Sequelize.DATE,
        allowNull: true
      },
      firebaseUid: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      firebaseSignInProvider: {
        type: Sequelize.STRING,
        allowNull: false
      },
      gender:{
        type: Sequelize.ENUM('male', 'female', 'other'),
        allowNull: true,
      },
      country:{
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

  
  },

  async down(queryInterface, Sequelize) {
   
    await queryInterface.dropTable('users');
  }
};