const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');


const BatteryCapacity = sequelize.define(
    'BatteryCapacity',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      capacity: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      unit: {
        type: DataTypes.STRING,
        allowNull: false,
        defaultValue: 'kWh',
      },
    },
    {
      tableName: 'battery_capacities',
      timestamps: false,
    }
  );
  
  BatteryCapacity.paginate = paginate();
  module.exports = BatteryCapacity;