'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.createTable('policies', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM('privacy', 'subscription', 'penalty', 'cancellation', 'terms'),
        allowNull: false,
        comment: 'Type of policy document',
      },
      userType: {
        type: Sequelize.ENUM('all', 'user', 'vendor'),
        allowNull: false,
        comment: 'Target user type for this policy',
      },
      title: {
        type: Sequelize.STRING,
        allowNull: false,
        comment: 'Policy title',
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: false,
        comment: 'Policy content/description',
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
        comment: 'Whether this policy is currently active',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    // Add constraint to ensure only one active policy per type/userType combination
    await queryInterface.addConstraint('policies', {
      fields: ['type', 'userType'],
      type: 'unique',
      where: {
        isActive: true,
      },
      name: 'unique_active_policy_per_type',
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */

    // Remove constraints first
    await queryInterface.removeConstraint('policies', 'unique_active_policy_per_type');
    // Drop the table
    await queryInterface.dropTable('policies');

    // Drop the ENUM types (PostgreSQL specific)
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_policies_type"');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_policies_userType"');
  },
};
