const express = require('express');
const router = express.Router();
const {firebaseAuth} = require('../../middlewares/firebaseAuth');
const validate = require('../../middlewares/validate');
const { bookingController, walletBookingController } = require('../../controllers');
const bookingValidation = require('../../validations/booking.validation');
const walletBookingValidation = require('../../validations/walletBooking.validation');


// Booking a charging slot
router.post("/:stationId/chargers/:chargerId",firebaseAuth,validate(bookingValidation.bookChargingSlot), bookingController.bookChargingSlot);

//Getting my bookings details
router.get("/vendor/my-bookings",firebaseAuth,validate(bookingValidation.getBookingDetails), bookingController.getVendorBookingDetails);

//Getting my bookings details
router.get("/user/my-bookings",firebaseAuth,validate(bookingValidation.getBookingDetails), bookingController.getUserBookingDetails);

//Get booking details by booking id
router.get("/:bookingId",firebaseAuth,validate(bookingValidation.getBookingDetailsById), bookingController.getBookingDetailsById);

//Cancel a booking
router.patch("/:bookingId/cancel",firebaseAuth,validate(bookingValidation.cancelBooking), bookingController.cancelBooking);

//change time slot
router.patch("/:bookingId/change-time-slot",firebaseAuth,validate(bookingValidation.changeTimeSlot), bookingController.changeTimeSlot);

//Get available time slots for a specific charger on a specific date
router.get("/stations/:stationId/chargers/:chargerId/available-slots", firebaseAuth, validate(bookingValidation.getAvailableTimeSlots), bookingController.getAvailableTimeSlots);

//Vendor confirms a pending booking
router.patch("/:bookingId/confirm", firebaseAuth, validate(bookingValidation.confirmBooking), bookingController.confirmBooking);

// Process booking payment using wallet
router.post("/:bookingId/pay", firebaseAuth, validate(walletBookingValidation.processBookingPayment), walletBookingController.processBookingPayment);

// Process booking refund to wallet
router.post("/:bookingId/refund", firebaseAuth, validate(walletBookingValidation.processBookingRefund), walletBookingController.processBookingRefund);

// Get station booking history with sorting and pagination
router.get("/stations/:stationId/history", firebaseAuth, validate(bookingValidation.getStationBookingHistory), bookingController.getStationBookingHistory);

module.exports = router;

