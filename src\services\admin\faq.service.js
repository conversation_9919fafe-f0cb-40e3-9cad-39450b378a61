
const {FAQ} = require('../../models');
const logger = require('../../config/logger');
const { Op } = require('sequelize');
const { sequelize } = require('../../config/database'); // Import sequelize instance
const moment = require('moment');
const ApiError = require('../../utils/ApiError');
const httpStatus = require('http-status');

/**
 * Create a new FAQ with unique displayOrder.
 * If displayOrder is provided and already exists,
 * increment displayOrder of existing FAQs >= the given order.
 * @param {Object} faqData - FAQ data
 * @param {string} faqData.question
 * @param {string} faqData.answer
 * @param {string} faqData.type
 * @param {string} faqData.status
 * @param {number} [faqData.displayOrder]
 * @returns {Promise<FAQ>}
 */
const createFAQ = async faqData => {
  logger.info('Creating new FAQ', {type: faqData.type, status: faqData.status});

  if (!faqData.displayOrder) {
    // If no displayOrder provided, assign highest + 1
    const highestOrder = (await FAQ.max('displayOrder')) || 0;
    faqData.displayOrder = highestOrder + 1;
  } else {
    // If displayOrder provided, shift existing FAQs to avoid duplicate order
    await FAQ.increment(
      {displayOrder: 1},
      {
        where: {
          displayOrder: {
            [Op.gte]: faqData.displayOrder,
          },
        },
      }
    );
  }

  const faq = await FAQ.create(faqData);
  logger.info('FAQ created successfully', {id: faq.id, type: faq.type});
  return faq;
};

/**
 * Delete a FAQ by ID
 * @param {number} faqId - FAQ ID to delete
 * @returns {Promise<boolean>} True if deleted successfully
 */
const deleteFAQ = async (faqId) => {
   const transaction = await sequelize.transaction();

  try {
    logger.info('Deleting FAQ', { id: faqId });

    const faq = await FAQ.findByPk(faqId, { transaction });

    if (!faq) {
      logger.warn('FAQ not found', { id: faqId });
      throw new ApiError(httpStatus.NOT_FOUND, 'FAQ not found');
    }

    await faq.destroy({ transaction });

    // Re-fetch remaining FAQs ordered by displayOrder
    const remainingFaqs = await FAQ.findAll({
      order: [['displayOrder', 'ASC']],
      transaction,
    });

    // Reassign sequential displayOrder values
    await Promise.all(
      remainingFaqs.map((item, index) =>
        item.update({ displayOrder: index + 1 }, { transaction })
      )
    );

    await transaction.commit();
    logger.info('FAQ deleted and reordered successfully', { id: faqId });

    return true;
  } catch (error) {
    await transaction.rollback();
    logger.error('Failed to delete FAQ', { error: error.message });
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to delete FAQ');
  }
};

/**
 * Update a FAQ by ID
 * @param {number} faqId - FAQ ID to update
 * @param {Object} updateData - Data to update
 * @param {string} [updateData.question] - Updated question
 * @param {string} [updateData.answer] - Updated answer
 * @param {string} [updateData.type] - Updated type
 * @param {string} [updateData.status] - Updated status
 * @returns {Promise<FAQ>} Updated FAQ
 */
const updateFAQ = async (faqId, updateData) => {

    logger.info('Updating FAQ', { id: faqId });
    const faq = await FAQ.findByPk(faqId);

    if (!faq) {
      logger.warn('FAQ not found', { id: faqId });
      throw new ApiError(httpStatus.NOT_FOUND, 'FAQ not found');
    }

    await faq.update(updateData);

    logger.info('FAQ updated successfully', { id: faqId });
    return faq; 
};

/**
 * Get FAQs with filtering and pagination
 * @param {Object} options - Filter and pagination options
 * @returns {Promise<Object>} - Paginated list of FAQs
 */
const getFAQs = async (options = {}) => {
  const { page, limit, sortBy, type, status, search, fromDate, toDate } = options;

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build where clause for filtering
  const where = {};

  // Add type filter
  if (type) {
    where.type = type;
  }

  // Add status filter
  if (status) {
    where.status = status;
  }

  // Add search filter (searches in question and answer fields)
  if (search) {
    where[Op.or] = [
      { question: { [Op.iLike]: `%${search}%` } },
      { answer: { [Op.iLike]: `%${search}%` } }
    ];
  }

  // Add date range filter
  if (fromDate || toDate) {
    where.createdAt = {};

    if (fromDate) {
      const fromDateObj = moment(fromDate)
        .startOf('day')
        .toDate();
      where.createdAt[Op.gte] = fromDateObj;
    }

    if (toDate) {
      const toDateObj = moment(toDate)
        .endOf('day')
        .toDate();
      where.createdAt[Op.lte] = toDateObj;
    }
  }

  // Parse sort options
  const order = [];
  const sortOptions = sortBy.split(',');

  for (const option of sortOptions) {
    const [field, direction = 'asc'] = option.split(':');
    order.push([field, direction.toUpperCase()]);
  }

  // Use findAndCountAll for better performance (single query)
  const { rows: faqs, count } = await FAQ.findAndCountAll({
    where,
    order,
    limit,
    offset,
    subQuery: false, // Optimize for complex queries
  });

  // Calculate total pages
  const totalPages = Math.ceil(count / limit);

  // Return paginated result
  return {
    faqs,
    pagination: {
      totalItems: count,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
      hasNextPage: page < totalPages,
      hasPreviousPage: page > 1,
    },
  };
};



module.exports = {
  createFAQ,
  updateFAQ,
  deleteFAQ,
  getFAQs
};




