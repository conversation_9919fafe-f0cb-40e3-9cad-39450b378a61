const { DataTypes, Sequelize } = require('sequelize');
const { paginate } = require('./plugins/paginate'); // Custom pagination plugin
const { sequelize } = require('../config/database'); // Import sequelize instance
const StationAmenity = sequelize.define('StationAmenity', {
    stationId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'stations',
        key: 'id',
      },
      allowNull: false,
    },
    amenityId: {
      type: DataTypes.INTEGER,
      references: {
        model: 'amenities', // Assuming amenities table exists
        key: 'id',
      },
      allowNull: false,
    },
  }, {
    tableName: 'station_amenities',
    timestamps: false, // No need for createdAt or updatedAt
  });
  
  module.exports = StationAmenity;