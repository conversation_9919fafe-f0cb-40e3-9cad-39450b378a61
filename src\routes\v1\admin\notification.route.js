const express = require('express');
const { adminAuth } = require('../../../middlewares/adminAuth');

const notificationController = require('../../../controllers/admin/notification.controller');

const router = express.Router();

/**
 * @api {get} v1/admin/notifications Get admin notifications
 * @apiDescription Get admin notifications with pagination
 * @apiVersion 1.0.0
 * @apiName GetAdminNotifications
 * @apiGroup AdminNotification
 *
 * @apiParam  {Number{1-}}  [page=1]     List page
 * @apiParam  {Number{1-100}}  [limit=20]  Notifications per page
 *
 * @apiSuccess {Object[]} notifications List of notifications
 * @apiSuccess {Number} totalCount Total count of notifications
 * @apiSuccess {Number} currentPage Current page
 * @apiSuccess {Number} totalPages Total pages
 */
router.get('/', adminAuth, notificationController.getNotifications);

/**
 * @api {get} v1/admin/notifications/unread/count Get unread notification count
 * @apiDescription Get count of unread admin notifications
 * @apiVersion 1.0.0
 * @apiName GetAdminUnreadCount
 * @apiGroup AdminNotification
 * @apiPermission admin
 *
 * @apiSuccess {Number} count Count of unread notifications
 */
router.get('/unread/count', adminAuth, notificationController.getUnreadCount);

/**
 * @api {patch} v1/admin/notifications/:notificationId/read Mark notification as read
 * @apiDescription Mark a notification as read
 * @apiVersion 1.0.0
 * @apiName MarkAdminNotificationAsRead
 * @apiGroup AdminNotification
 *
 * @apiParam  {String}  notificationId  Notification's unique id
 *
 * @apiSuccess {Object}  notification  Notification object
 */
router.patch('/:notificationId/read', adminAuth, notificationController.markAsRead);

/**
 * @api {patch} v1/admin/notifications/read/all Mark all notifications as read
 * @apiDescription Mark all admin notifications as read
 * @apiVersion 1.0.0
 * @apiName MarkAllAdminNotificationsAsRead
 * @apiGroup AdminNotification
 *
 * @apiSuccess {String}  message  Success message
 */
router.patch('/read/all', adminAuth, notificationController.markAllAsRead);

module.exports = router; 