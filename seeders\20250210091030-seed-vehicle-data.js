'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add seed commands here.
     *
     * Example:
     * await queryInterface.bulkInsert('People', [{
     *   name: '<PERSON>',
     *   isBetaMember: false
     * }], {});
    */
    await queryInterface.bulkInsert('vehicle_brands', [
      { name: 'Tesla' },
      { name: 'BMW' },
      { name: 'Audi' },
      { name: 'Mercedes' },
      { name: 'Porsche' }
    ], {});

    // Get inserted brands
    const brands = await queryInterface.sequelize.query(
      `SELECT id, name FROM vehicle_brands;`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // 2. Insert Models
    const models = [];
    for (const brand of brands) {
      switch(brand.name) {
        case 'Tesla':
          models.push(
            { name: 'Model S', brandId: brand.id },
            { name: 'Model 3', brandId: brand.id },
            { name: 'Model X', brandId: brand.id },
            { name: 'Model Y', brandId: brand.id }
          );
          break;
        case 'BMW':
          models.push(
            { name: 'i4', brandId: brand.id },
            { name: 'iX', brandId: brand.id },
            { name: 'i7', brandId: brand.id }
          );
          break;
        case 'Audi':
          models.push(
            { name: 'e-tron GT', brandId: brand.id },
            { name: 'Q4 e-tron', brandId: brand.id },
            { name: 'Q8 e-tron', brandId: brand.id }
          );
          break;
        case 'Mercedes':
          models.push(
            { name: 'EQS', brandId: brand.id },
            { name: 'EQE', brandId: brand.id },
            { name: 'EQB', brandId: brand.id }
          );
          break;
        case 'Porsche':
          models.push(
            { name: 'Taycan', brandId: brand.id },
            { name: 'Macan Electric', brandId: brand.id }
          );
          break;
      }
    }
    await queryInterface.bulkInsert('vehicle_models', models, {});

    // Get inserted models
    const vehicleModels = await queryInterface.sequelize.query(
      `SELECT id, name, "brandId" FROM vehicle_models;`,
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    // 3. Insert Trims
    const trims = [];
    for (const model of vehicleModels) {
      // Add different trim levels based on model
      if (model.name.includes('Tesla')) {
        trims.push(
          { name: 'Standard Range', modelId: model.id },
          { name: 'Long Range', modelId: model.id },
          { name: 'Performance', modelId: model.id }
        );
      } else {
        trims.push(
          { name: 'Base', modelId: model.id },
          { name: 'Premium', modelId: model.id },
          { name: 'Sport', modelId: model.id }
        );
      }
    }
    await queryInterface.bulkInsert('vehicle_trims', trims, {});

    // 4. Insert Manufacturing Units
    const manufacturingUnits = [
      { name: 'American Built', location: 'USA' },
      { name: 'Chinese Built', location: 'China' },
      { name: 'Germany Built', location: 'Germany' },
      { name: 'Japanese Built', location: 'Japan' }
    ];
    await queryInterface.bulkInsert('manufacturing_units', manufacturingUnits, {});

    // 5. Insert Battery Capacities
    const batteryCapacities = [
      { capacity: 60.0, unit: 'kWh' },
      { capacity: 75.0, unit: 'kWh' },
      { capacity: 85.0, unit: 'kWh' },
      { capacity: 95.0, unit: 'kWh' },
      { capacity: 100.0, unit: 'kWh' },
      { capacity: 120.0, unit: 'kWh' }
    ];
    await queryInterface.bulkInsert('battery_capacities', batteryCapacities, {});
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add commands to revert seed here.
     *
     * Example:
     * await queryInterface.bulkDelete('People', null, {});
     */
        await queryInterface.bulkDelete('battery_capacities', null, {});
    await queryInterface.bulkDelete('manufacturing_units', null, {});
    await queryInterface.bulkDelete('vehicle_trims', null, {});
    await queryInterface.bulkDelete('vehicle_models', null, {});
    await queryInterface.bulkDelete('vehicle_brands', null, {});

  }
};