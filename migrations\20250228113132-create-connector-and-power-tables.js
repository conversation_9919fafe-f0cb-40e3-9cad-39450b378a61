'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Create connectors table
    await queryInterface.createTable('connectors', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      type: {
        type: Sequelize.STRING,
        allowNull: false,
      }
    });

    // Create powers table
    await queryInterface.createTable('powers', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      value: {
        type: Sequelize.FLOAT,
        allowNull: false,
      },
      unit: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'kW'
      }
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('powers');
    await queryInterface.dropTable('connectors');
  }
}; 