const express = require('express');
const router = express.Router();

const authRoute = require('./auth.route');
const userRoute = require('./user.route');
const vehicleRoute = require('./vehicle.route');
const vendorRoute = require('./vendor.route');
const stationRoute = require('./station.route');
const bookingRoute = require('./booking.route');
const chatRoute = require('./chat.route');
const walletRoute = require('./wallet.route');
const notificationRoute = require('./notification.route');
const paymentRoutes = require('./payment.route');
const adminRoutes = require('./admin');
const subscriptionRoute = require('./subscription.route');
const vendorSubscriptionRoute = require('./vendorSubscription.route');
const helpCenterRoute = require('./helpCenter.route');

router.use('/auth', authRoute);
router.use('/', userRoute);
router.use('/vehicles', vehicleRoute);
router.use('/vendors', vendorRoute);
router.use('/stations', stationRoute);
router.use('/bookings', bookingRoute);
router.use('/chats', chatRoute);
router.use('/wallets', walletRoute);
router.use('/notifications', notificationRoute);
router.use('/payments', paymentRoutes);
router.use('/admin', adminRoutes);
router.use('/subscriptions', subscriptionRoute);
router.use('/vendor-subscriptions', vendorSubscriptionRoute);
router.use('/help-center', helpCenterRoute);

module.exports = router;
