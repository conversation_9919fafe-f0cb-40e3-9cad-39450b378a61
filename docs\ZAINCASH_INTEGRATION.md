# ZainCash Integration for EV.IQ Wallet

This document explains how to set up and use the ZainCash payment gateway integration for topping up user wallets.

## Overview

The integration allows users to top up their wallets using ZainCash, a popular mobile payment service in Iraq. The flow is as follows:

1. User initiates a top-up from the wallet page
2. Backend generates a ZainCash payment request
3. User is redirected to ZainCash to complete the payment
4. ZainCash redirects back to our application with the payment result
5. Backend verifies the payment and credits the user's wallet
6. User is shown a success or error page

## Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```
ZAINCASH_MSISDN=your_zaincash_msisdn
ZAINCASH_SECRET=your_zaincash_secret_key
ZAINCASH_MERCHANT_ID=your_zaincash_merchant_id
ZAINCASH_LANG=en
```

### Test vs Production

The system automatically uses the test or production ZainCash endpoints based on your `NODE_ENV` environment variable:

- `NODE_ENV=development` - Uses test endpoints
- `NODE_ENV=production` - Uses production endpoints

## API Endpoints

### Initialize Payment

```
POST /api/v1/payments/zaincash/initialize
```

**Request Body:**
```json
{
  "walletId": 123,
  "amount": 5000
}
```

**Response:**
```json
{
  "status": true,
  "data": {
    "paymentUrl": "https://test.zaincash.iq/transaction/pay?id=123456789",
    "transactionId": "123456789",
    "orderId": "wallet_123_1621234567890",
    "amount": 5000,
    "walletId": 123
  },
  "message": "Payment initialized successfully"
}
```

### Payment Callback

```
GET /api/v1/payments/zaincash/callback?token=jwt_token_from_zaincash
```

This endpoint is called by ZainCash after the payment is completed. It verifies the payment and credits the user's wallet.

## Frontend Integration

1. Add a "Top Up with ZainCash" button to your wallet page
2. When clicked, show a modal to enter the amount
3. Call the initialize payment API
4. Redirect the user to the returned payment URL
5. Handle the redirect back to your application after payment

## Testing

For testing, you can use the ZainCash test environment. The test environment doesn't require real money and allows you to simulate successful and failed payments.

## Troubleshooting

### Common Issues

1. **Payment Initialization Fails**
   - Check that your ZainCash credentials are correct
   - Ensure the amount is at least 250 IQD

2. **Callback Not Received**
   - Verify that your redirect URL is correctly configured
   - Check that your server is accessible from the internet

3. **Payment Verification Fails**
   - Ensure your secret key is correct
   - Check that the token hasn't been tampered with

### Logs

The system logs all ZainCash-related operations. Check the logs for detailed error messages:

```
grep "ZainCash" /path/to/your/logs
```

## Security Considerations

1. All payment initialization requests require authentication
2. Wallet ownership is verified before allowing top-up
3. Payment verification uses JWT with your secret key
4. Sensitive data is not exposed in URLs or logs

## Support

For issues with the ZainCash integration, contact:

- ZainCash Support: <EMAIL>
- EV.IQ Support: <EMAIL>
