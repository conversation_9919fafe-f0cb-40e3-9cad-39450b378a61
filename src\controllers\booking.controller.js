const catchAsync = require('../utils/catchAsync');
const { bookingService } = require('../services');
const httpStatus = require('http-status');
const { stationService } = require('../services');
const { ApiError } = require('../utils/ApiError');

const bookChargingSlot = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { stationId, chargerId } = req.params;
    const { vehicleId, arrivalTime, duration } = req.body;
    const result = await bookingService.bookChargingSlot(userId, stationId, chargerId, vehicleId, arrivalTime, duration);

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Slot booked successfully"
    });
});

const getVendorBookingDetails  = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { status, page, limit } = req.query;
    const result = await bookingService.getBookingDetails(userId, status, page, limit,true);

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Booking details fetched successfully"
    });
});

const getUserBookingDetails  = catchAsync(async (req, res) => {
    const userId = req.user.id;
    const { status, page, limit } = req.query;
    const result = await bookingService.getBookingDetails(userId, status, page, limit,false);

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Booking details fetched successfully"
    });
});

const getBookingDetailsById = catchAsync(async (req,res) => {
    const userId = req.user.id;
    const bookingId = req.params.bookingId;
    const result = await bookingService.getBookingDetailsById(userId, bookingId);

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Booking details fetched successfully"
    });
}
);

const cancelBooking = catchAsync(async (req,res) => {
    const userId = req.user.id;
    const bookingId = req.params.bookingId;
    const  {cancellationReason}  = req.body;
    await bookingService.cancelBooking(userId, bookingId,cancellationReason);

    res.status(httpStatus.OK).json({
        status: true,
        message: "Booking cancelled successfully"
    });
}
);

const changeTimeSlot = catchAsync(async (req,res) => {
    const userId = req.user.id;
    const bookingId = req.params.bookingId;
    const  {arrivalTime, duration,timeSlotChangeReason}  = req.body;
    const result = await bookingService.changeTimeSlot(userId, bookingId,arrivalTime,duration,timeSlotChangeReason);

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Time slot changed successfully"
    });
});

/**
 * Get available time slots for a station or specific charger on a specific date
 */
const getAvailableTimeSlots = catchAsync(async (req, res) => {
    const { stationId, chargerId } = req.params;
    const { date } = req.query;

    const availableSlots = await bookingService.getAvailableTimeSlots(stationId, date, chargerId);

    res.status(httpStatus.OK).json({
        status: true,
        data: availableSlots,
        message: 'Available time slots retrieved successfully'
    });
});

const confirmBooking = catchAsync(async (req, res) => {
    const { bookingId } = req.params;
    const userId = req.user.id;
    
    const updatedBooking = await bookingService.confirmBooking(userId, bookingId);

    res.status(httpStatus.OK).json({
        status: true,
        message: 'Booking confirmed successfully',
        data: updatedBooking
    });
});

const getStationBookingHistory = catchAsync(async (req, res) => {
    const { stationId } = req.params;
    const userId = req.user.id;
    const { page, limit, timeFilter = 'all',sortBy,sortOrder} = req.query;
    
    const result = await bookingService.getStationBookingHistory(stationId, userId, {
        page,
        limit,
        sortBy,
        sortOrder,
        timeFilter
    });

    res.status(httpStatus.OK).json({
        status: true,
        data: result,
        message: "Station booking history fetched successfully"
    });
});

module.exports = {
    bookChargingSlot,
    getVendorBookingDetails,
    getUserBookingDetails,
    getBookingDetailsById,
    cancelBooking,
    changeTimeSlot,
    getAvailableTimeSlots,
    confirmBooking,
    getStationBookingHistory
};