const Joi = require('joi');
const {objectId, validateMediaExtension
} = require('./custom.validation');


const updateDetails = {
  body: Joi.object().keys({
    name: Joi.string().trim(),
    phone: Joi.string().trim(),
    hqAddress: Joi.string().trim(),
    hqCity: Joi.string().trim(),
    hqZipCode: Joi.string().trim(),
    hqState: Joi.string().trim(),
    thumbnail: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'image');
    }, 'Thumbnail Validation'),
  }),
};

const additionalResources = {
  body: Joi.object().keys({
    demoVideo: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'video');
    }, 'Demo video Validation'),
    featuredVideo: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'video');
    }, 'Featured video Validation'),
    videoCall: Joi.string().custom((value, helpers) => {
      return validateMediaExtension(value, helpers, 'video');
    }, 'Video call Validation'),
  }),
};


module.exports = {
  updateDetails,
  additionalResources
};
