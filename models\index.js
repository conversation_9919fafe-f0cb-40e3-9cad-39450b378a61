const { sequelize } = require('../config/database');

// Import all model definitions
// const ChargerModel = require('./Charger');
const ChargerConnectorModel = require('./ChargerConnector');
const ChargingSessionModel = require('./ChargingSession');
const MeterValueModel = require('./MeterValue');
const SessionEventModel = require('./SessionEvent');
const TransactionModel = require('./Transaction');

// Initialize models
const Charger = require('./Charger');
const ChargerConnector = require('./ChargerConnector');
const Connector = require('./Connectors');
const ChargingSession = ChargingSessionModel(sequelize);
const MeterValue = MeterValueModel(sequelize);
const SessionEvent = SessionEventModel(sequelize);
const Transaction = TransactionModel(sequelize);

// ==================== RELATIONSHIPS ====================

// Charger -> ChargerConnector (One-to-Many)
Charger.hasMany(ChargerConnector, {
  foreignKey: 'chargerId',
  as: 'connectors'
});
ChargerConnector.belongsTo(Charger, {
  foreignKey: 'chargerId',
  as: 'charger'
});

// Charger -> ChargingSession (One-to-Many)
Charger.hasMany(ChargingSession, {
  foreignKey: 'chargerId',
  as: 'sessions'
});
ChargingSession.belongsTo(Charger, {
  foreignKey: 'chargerId',
  as: 'charger'
});

// ChargerConnector -> ChargingSession (One-to-Many)
ChargerConnector.hasMany(ChargingSession, {
  foreignKey: 'connectorId',
  as: 'sessions'
});
ChargingSession.belongsTo(ChargerConnector, {
  foreignKey: 'connectorId',
  as: 'connector'
});

// ChargingSession -> MeterValue (One-to-Many)
ChargingSession.hasMany(MeterValue, {
  foreignKey: 'sessionId',
  as: 'meterValues'
});
MeterValue.belongsTo(ChargingSession, {
  foreignKey: 'sessionId',
  as: 'session'
});

// ChargingSession -> SessionEvent (One-to-Many)
ChargingSession.hasMany(SessionEvent, {
  foreignKey: 'sessionId',
  as: 'events'
});
SessionEvent.belongsTo(ChargingSession, {
  foreignKey: 'sessionId',
  as: 'session'
});

// ChargingSession -> Transaction (One-to-One)
ChargingSession.hasOne(Transaction, {
  foreignKey: 'sessionId',
  as: 'transaction'
});
Transaction.belongsTo(ChargingSession, {
  foreignKey: 'sessionId',
  as: 'session'
});

// Charger -> MeterValue (One-to-Many)
Charger.hasMany(MeterValue, {
  foreignKey: 'chargerId',
  as: 'meterValues'
});
MeterValue.belongsTo(Charger, {
  foreignKey: 'chargerId',
  as: 'charger'
});

// Charger -> Transaction (One-to-Many)
Charger.hasMany(Transaction, {
  foreignKey: 'chargerId',
  as: 'transactions'
});
Transaction.belongsTo(Charger, {
  foreignKey: 'chargerId',
  as: 'charger'
});

// ChargerConnector.belongsTo(Charger, {
//   foreignKey: 'chargerId',
//   as: 'charger'
// });
  ChargerConnector.belongsTo(Connector, {
    foreignKey: 'connectorId',
    as: 'connector'
  });
// Export all models and sequelize instance
module.exports = {
    sequelize,
    Charger,
    ChargerConnector,
    Connector,
    ChargingSession,
    MeterValue,
    SessionEvent,
    Transaction
};
