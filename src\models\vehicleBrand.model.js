const { DataTypes } = require('sequelize');
const { paginate } = require('./plugins/paginate');
const { sequelize } = require('../config/database');

const VehicleBrand = sequelize.define(
  'VehicleBrand',
  {
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
    },
  },
  {
    tableName: 'vehicle_brands',
    timestamps: false, // No createdAt/updatedAt fields needed
  }
);

// Attach the custom pagination plugin

VehicleBrand.paginate = paginate();

module.exports = VehicleBrand;
