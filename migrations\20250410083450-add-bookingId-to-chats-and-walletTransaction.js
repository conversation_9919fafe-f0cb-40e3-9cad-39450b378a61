'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('chats', 'bookingId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'bookings', // name of the bookings table
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
    await queryInterface.addColumn('wallet_transactions', 'bookingId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'bookings', // name of the bookings table
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('chats', 'bookingId');
    await queryInterface.removeColumn('wallet_transactions', 'bookingId');
  }
};
