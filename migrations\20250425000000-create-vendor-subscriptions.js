'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('vendor_subscriptions', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      vendorId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'vendors',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      subscriptionPlanId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'subscription_plans',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: false,
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: true,
      },
      autoRenew: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      inGracePeriod: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      renewedFromId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'vendor_subscriptions',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      renewalAttempts: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      status:{
        type: Sequelize.ENUM(
          'active',
          'expired',
          'canceled',
          'inGracePeriod',
          'pendingRenewal',
          'failedRenewal'
        ),
        allowNull: false,
        defaultValue: 'active',
        comment: 'Current status of the vendor subscription'
      }
    });

    await queryInterface.addIndex('vendor_subscriptions', ['vendorId']);
    await queryInterface.addIndex('vendor_subscriptions', ['subscriptionPlanId']);
    await queryInterface.addIndex('vendor_subscriptions', ['isActive']);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('vendor_subscriptions');
  },
};
