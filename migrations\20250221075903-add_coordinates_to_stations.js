'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('stations', 'latitude', {
      type: Sequelize.DOUBLE,
      allowNull: false,
  });

  await queryInterface.addColumn('stations', 'longitude', {
      type: Sequelize.DOUBLE,
      allowNull: false,
  });

  await queryInterface.removeColumn('stations', 'location');
  // ✅ Optional: Create an index for faster geolocation searches
  await queryInterface.addIndex('stations', ['latitude', 'longitude']);
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeIndex('stations', ['latitude', 'longitude']);
    await queryInterface.removeColumn('stations', 'latitude');
    await queryInterface.removeColumn('stations', 'longitude');
    await queryInterface.addColumn('stations', 'location', {
      type: Sequelize.JSONB,
      allowNull: false,
  });
  }
};
