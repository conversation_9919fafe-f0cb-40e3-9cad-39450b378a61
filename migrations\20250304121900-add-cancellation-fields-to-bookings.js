'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.addColumn('bookings', 'cancellationReason', {
      type: Sequelize.STRING,
      allowNull: true, // Optional field
    });

    await queryInterface.addColumn('bookings', 'cancellationTime', {
      type: Sequelize.DATE,
      allowNull: true, // Optional field
    });

    await queryInterface.addColumn('bookings', 'refundAmount', {
      type: Sequelize.FLOAT,
      allowNull: true, // Optional field
    });
  },

  async down (queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.removeColumn('bookings', 'cancellationReason');
    await queryInterface.removeColumn('bookings', 'cancellationTime');
    await queryInterface.removeColumn('bookings', 'refundAmount');
  }
};
