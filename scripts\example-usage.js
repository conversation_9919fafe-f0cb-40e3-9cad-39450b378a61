const { sendEmail } = require('../src/microservices/email.service');

// Example usage
async function sendWelcomeEmail(user) {
  await sendEmail({
    to: user.email,
    subject: 'Welcome to EVIQ',
    html: `
      <div style="font-family: Arial, sans-serif; color: #333;">
        <h1>Welcome to EV-IQ</h1>
        <p>Hello ${user.name},</p>
        <p>Thank you for joining our platform. We're excited to have you on board!</p>
        <p>Best regards,<br>The EVIQ Team</p>
      </div>
    `
  });
}

// Test function
async function testEmailSending() {
  try {
    const testUser = {
      name: 'Test',
      email: '<EMAIL>' // Replace with a real email to test
    };
    
    console.log(`Sending test email to ${testUser.email}...`);
    await sendWelcomeEmail(testUser);
    console.log('Test email sent successfully!');
  } catch (error) {
    console.error('Failed to send test email:', error);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEmailSending().then(() => {
    console.log('Test completed.');
  });
}

// Export for use in other files
module.exports = {
  sendWelcomeEmail
};
