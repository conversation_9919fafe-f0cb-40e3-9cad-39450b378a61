const { fileUploadService } = require('../microservices');

const uploadAllOrRollback = async (files, folder) => {
    if (!files?.length) return [];
  
    const MAX_CONCURRENT = 3;
    const uploaded = [];
    const errors = [];
  
    try {
      // Process uploads in batches
      for (let i = 0; i < files.length; i += MAX_CONCURRENT) {
        const batch = files.slice(i, i + MAX_CONCURRENT);
        
        const results = await Promise.allSettled(
          batch.map(file => fileUploadService.s3Upload([file], folder))
        );
  
        // Process batch results
        results.forEach((result, index) => {
          if (result.status === 'fulfilled') {
            uploaded.push(...result.value);
          } else {
            errors.push({
              file: batch[index].originalname,
              error: result.reason.message
            });
          }
        });
  
        // Immediate failure if any upload in batch fails
        if (errors.length) {
          throw new Error('Partial upload failure detected');
        }
      }
  
      return uploaded;
  
    } catch (error) {
      // Rollback all successful uploads
      if (uploaded.length > 0) {
        const rollbackResults = await Promise.allSettled(
          uploaded.map(image => fileUploadService.s3Delete(image.key))
        );
  
        // Track failed rollbacks
        const failedRollbacks = rollbackResults.filter(r => r.status === 'rejected');
        if (failedRollbacks.length > 0) {
          console.error('Failed to rollback:', failedRollbacks.map(r => r.reason));
        }
      }
  
      // Combine all errors for reporting
      const allErrors = [
        ...errors,
        ...(uploaded.length > 0 ? [`Rolled back ${uploaded.length} files`] : [])
      ];
  
      throw new Error(
        `Upload failed: ${allErrors.join(' | ')}. Original error: ${error.message}`
      );
    }
  };

  module.exports = uploadAllOrRollback