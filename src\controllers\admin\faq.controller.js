const httpStatus = require('http-status');
const catchAsync = require('../../utils/catchAsync');
const faqService = require('../../services/admin/faq.service');

/**
 * Create a new FAQ
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Created FAQ
 */
const createFAQ = catchAsync(async (req, res) => {
  const faq = await faqService.createFAQ(req.body);
  
  res.status(httpStatus.CREATED).json({
    status: true,
    data: faq,
    message: 'FAQ created successfully'
  });
});

/**
 * Delete a FAQ
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Success message
 */
const deleteFAQ = catchAsync(async (req, res) => {
  const { faqId } = req.params;
  
  await faqService.deleteFAQ(faqId);
  
  res.status(httpStatus.OK).json({
    status: true,
    message: 'FAQ deleted successfully'
  });
});

/**
 * Update a FAQ
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Updated FAQ
 */
const updateFAQ = catchAsync(async (req, res) => {
  const { faqId } = req.params;
  const updateData = req.body;
  
  const updatedFAQ = await faqService.updateFAQ(faqId, updateData);
  
  res.status(httpStatus.OK).json({
    status: true,
    data: updatedFAQ,
    message: 'FAQ updated successfully'
  });
});

/**
 * Get FAQs with filtering and pagination
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {Object} Paginated FAQs
 */
const getFAQs = catchAsync(async (req, res) => {
  const { 
    page, 
    limit, 
    sortBy, 
    type, 
    status, 
    search,
    fromDate, 
    toDate 
  } = req.query;
  
  const result = await faqService.getFAQs({
    page,
    limit,
    sortBy,
    type,
    status,
    search,
    fromDate,
    toDate
  });
  
  res.status(httpStatus.OK).json({
    status: true,
    data: result.faqs,
    pagination: result.pagination,
    message: 'FAQs fetched successfully'
  });
});

module.exports = {
  createFAQ,
  updateFAQ,
  deleteFAQ,
  getFAQs
};



