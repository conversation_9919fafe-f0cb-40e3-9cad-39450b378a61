const Joi = require('joi');

const initializeChat = {
  body: Joi.object().keys({
    topic: Joi.string().required().valid('booking', 'charging_station', 'penalty'),
    bookingId: Joi.number().when('topic', {
      is: 'booking',
      then: Joi.required(),
      otherwise: Joi.optional(),
    }),
  }),
};

const sendMessage = {
  body: Joi.object().keys({
    chatId: Joi.number().integer().required(),
    message: Joi.string().required().min(1).max(1000),
  }),
};

const getMessages = {
  params: Joi.object().keys({
    chatId: Joi.number().integer().required(),
  }),
  query: Joi.object().keys({
    page: Joi.number().integer().min(1),
    limit: Joi.number().integer().min(1).max(100),
  }),
};

const closeChat = {
  params: Joi.object().keys({
    chatId: Joi.number().integer().required(),
  }),
};

module.exports = {
  initializeChat,
  sendMessage,
  getMessages,
  closeChat,
};