const sgMail = require("@sendgrid/mail");
const config = require("../config/config");
sgMail.setApiKey(config.SENDGRID_PASSWORD);

async function sendEmail(emailData) {
    const mailOptions = {
      from: '<EMAIL>',
      to: emailData.to,
      subject: emailData.subject,
      html: emailData.html,
    };
  
    sgMail.send(mailOptions)
    .then(() => console.log('Email sent successfully'))
    .catch(error => console.error(error));
}

module.exports = {
    sendEmail,
};

