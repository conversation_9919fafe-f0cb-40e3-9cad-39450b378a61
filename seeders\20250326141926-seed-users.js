const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const filePath = path.join(__dirname, '../public_users_2025-03-26_170920.csv');
    const users = [];

    return new Promise((resolve, reject) => {
      fs.createReadStream(filePath)
        .pipe(csv())
        .on('data', (row) => {
          users.push({
            name: row.name,
            phone: row.phone || null,
            email: row.email,
            profilePicKey: row.profilePicKey || null,
            profilePicUrl: row.profilePicUrl || null,
            dob: row.dob ? new Date(row.dob) : null,
            firebaseUid: row.firebaseUid,
            firebaseSignInProvider: row.firebaseSignInProvider,
            gender: row.gender && row.gender !== '' ? row.gender : null,  // ✅ Fix applied here
            country: row.country || null,
            isVendor: row.isVendor === 'true', // Convert to boolean
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        })
        .on('end', async () => {
          try {
            await queryInterface.bulkInsert('users', users);
            console.log('✅ Users seeded successfully!');
            resolve();
          } catch (error) {
            reject(error);
          }
        })
        .on('error', (error) => reject(error));
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('users', null, {});
  }
};
