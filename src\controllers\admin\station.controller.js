const catchAsync = require('../../utils/catchAsync');
const stationService = require('../../services/admin/station.service');
const httpStatus = require('http-status');
const logger = require('../../config/logger');

/**
 * Get all stations with optional filters
 */

const getStations = catchAsync(async (req, res) => {
  const {search, status, sortBy, sortOrder, page, limit} = req.query;

  const filters = {
    search,
    status,
    sortBy,
    sortOrder,
    page,
    limit,
  };

  const result = await stationService.getStations(filters);

  res.status(httpStatus.OK).send({
    status: true,
    data: result.stations,
    pagination: result.pagination,
    message: 'Stations fetched successfully',
  });
});

/**
 * Approve a station if the vendor's KYC is approved
 */
const approveStation = catchAsync(async (req, res) => {
  const {stationId} = req.params;

  const station = await stationService.approveStation(stationId);

  return res.status(httpStatus.OK).json({
    status: true,
    data: station,
    message: 'Station approved successfully',
  });
});

/**
 * get station by id
 */

const getStationById = catchAsync(async (req, res) => {
  const {stationId} = req.params;

  const station = await stationService.getStationById(stationId);

  res.status(httpStatus.OK).json({
    status: true,
    data: station,
    message: 'Station fetched successfully',
  });
});

/**
 * get chargers of stations
 */
const getChargerById = catchAsync(async (req, res) => {
  const {chargerId, stationId} = req.params;
  const charger = await stationService.getChargerById(chargerId, stationId);

  res.status(httpStatus.OK).json({
    status: true,
    data: charger,
    message: 'Charger details fetched successfully',
  });
});

/*
Get booking history of station
*/

const getBookingHistory = catchAsync(async (req, res) => {
  const {stationId} = req.params;
  const { page, limit } = req.query;
  const bookings = await stationService.getBookingHistory(stationId, page, limit);

  res.status(httpStatus.OK).json({
    status: true,
    data: bookings,
    message: 'Booking history fetched successfully',
  });
});

const getBookingDetail = catchAsync(async (req, res) => {
  const {stationId, bookingId} = req.params;
  const booking = await stationService.getBookingDetail(stationId, bookingId);

  res.status(httpStatus.OK).json({
    status: true,
    data: booking,
    message: 'Booking detail fetched successfully',
  });
});

/*
Get block dates of station
*/
const getBlockDates = catchAsync(async (req, res) => {

  const {stationId} = req.params;
  const result = await stationService.getBlockDates(stationId);
  res.status(httpStatus.OK).json({
    status: true,
    data: result,
    message: 'Station block dates fetched successfully',
  });
  
});


const toggleStationStatus = catchAsync(async (req, res) => {
  const {stationId} = req.params;
  const {isEnabled, reason} = req.body;

  logger.info('Toggling station status', {
    stationId,
    isEnabled,
    reason,
  });

  await stationService.toggleStationStatus(stationId, isEnabled, reason);

  logger.info(`Station ${isEnabled ? 'enabled' : 'disabled'} successfully`, {
    stationId
  });

  res.status(httpStatus.OK).json({
    status: true,
    message: 'Station enabled/disabled successfully',
  });
});

module.exports = {
  getStations,
  approveStation,
  getStationById,
  getChargerById,
  getBookingHistory,
  getBookingDetail,
  getBlockDates,
  toggleStationStatus
};
