const httpStatus = require('http-status');
const jwt = require('jsonwebtoken');
const axios = require('axios');
const ApiError = require('../utils/ApiError');
const { walletService } = require('./index');
const config = require('../config/config');
const WalletTransaction = require('../models/walletTransaction.model');
const {WALLET_TRANSACTION_TYPE,WALLET_TRANSACTION_STATUS} = require('../constants');

// ZainCash API URLs
const TEST_INIT_URL = 'https://test.zaincash.iq/transaction/init';
const TEST_REQUEST_URL = 'https://test.zaincash.iq/transaction/pay?id=';
const PROD_INIT_URL = 'https://api.zaincash.iq/transaction/init';
const PROD_REQUEST_URL = 'https://api.zaincash.iq/transaction/pay?id=';

// Determine which URLs to use based on environment
const initUrl = config.env === 'production' ? PROD_INIT_URL : TEST_INIT_URL;
const requestUrl = config.env === 'production' ? PROD_REQUEST_URL : TEST_REQUEST_URL;

// Service type identifier
const serviceType = 'ev-iq-wallet';

/**
 * Initialize a ZainCash payment
 * @param {Object} paymentData - Payment data
 * @param {number} paymentData.amount - Amount to pay (in IQD)
 * @param {number} paymentData.walletId - Wallet ID to credit
 * @param {string} paymentData.userId - User ID making the payment
 * @returns {Promise<Object>} - Payment URL and transaction ID
 */
const initializePayment = async (paymentData) => {
  const { amount, walletId, userId } = paymentData;

  if (!amount || amount < 250) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Amount must be at least 250 IQD');
  }

  if (!walletId) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Wallet ID is required');
  }

  // Verify wallet ownership
  const isAuthorized = await walletService.checkWalletOwnership(walletId, userId);
  if (!isAuthorized) {
    throw new ApiError(httpStatus.FORBIDDEN, 'You are not authorized to top up this wallet');
  }

  // Generate a unique order ID (combination of wallet ID and timestamp)
  const orderId = `wallet_${walletId}_${Date.now()}`;

  // Set the token expire time
  const time = Date.now();

  // Set the redirect URL to our new payment gateway callback endpoint
  // const redirectUrl = `${config.clientUrl}v1/payments/wallet/zaincash/callback`;
  const redirectUrl = `${config.serverUrl}/v1/payments/wallet/zaincash/callback`;

  // Build the transaction data to be encoded in a JWT token
  const data = {
    amount: amount,
    serviceType: serviceType,
    msisdn: config.zaincash.msisdn,
    orderId: orderId,
    redirectUrl: redirectUrl,
    iat: time,
    exp: time + 60 * 60 * 4, // Token expires in 4 hours
  };

  // Encode the data with JWT
  const token = jwt.sign(data, config.zaincash.secret);

  // Prepare the payment data to be sent to ZainCash API
  const postData = {
    token: token,
    merchantId: config.zaincash.merchantId,
    lang: config.zaincash.lang || 'en'
  };

  try {
    // Initialize the ZainCash transaction
    const response = await axios.post(initUrl, postData, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    await WalletTransaction.create({
      walletId,
      userId,
      amount,
      balanceAfter: 0,
      type: WALLET_TRANSACTION_TYPE.TOPUP,
      status: WALLET_TRANSACTION_TYPE.PENDING,
      referenceId: orderId
    });
    // Get the operation ID from the response
    const operationId = response.data.id;

    if (!operationId) {
      throw new ApiError(httpStatus.BAD_GATEWAY, 'Failed to initialize ZainCash payment');
    }

    // Return the payment URL and transaction details
    return {
      paymentUrl: requestUrl + operationId,
      transactionId: operationId,
      orderId: orderId,
      amount: amount,
      walletId: walletId
    };
  } catch (error) {
    if (error.response) {
      throw new ApiError(
        httpStatus.BAD_GATEWAY,
        `ZainCash API error: ${error.response.data.message || 'Unknown error'}`
      );
    }
    throw error;
  }
};

/**
 * Verify a ZainCash payment callback
 * @param {string} token - JWT token from ZainCash callback
 * @returns {Promise<Object>} - Verified payment data
 */
const verifyPayment = async (token) => {
  if (!token) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token is required');
  }

  try {
    // Verify and decode the JWT token
    const decoded = jwt.verify(token, config.zaincash.secret);

    // Extract the necessary data
    const { id, msisdn, operationid, orderid, status, msg } = decoded;

    if (!id || !orderid || !status) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid payment data');
    }

    // Get the original transaction
    const pendingTransaction = await WalletTransaction.findOne({
      where: {
        referenceId: orderid,
        status: WALLET_TRANSACTION_STATUS.PENDING
      }
    });

    if (!pendingTransaction) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Original transaction not found');
    }

    if (status !== 'success') {
      // Update transaction for failed payment
      await pendingTransaction.update({
        status: WALLET_TRANSACTION_STATUS.FAILED,
        description: `ZainCash payment failed: ${msg || 'Unknown error'}`,
        metadata: {
          paymentProvider: 'zaincash',
          failureReason: msg,
        },
        processedAt: new Date()
      });

      throw new ApiError(
        httpStatus.PAYMENT_REQUIRED,
        `Payment failed: ${msg || 'Unknown error'}`
      );
    }

    // Use walletService to handle the successful top-up
    const result = await walletService.topUpWallet(
      pendingTransaction.walletId,
      pendingTransaction.amount,
      {
        paymentMethod: 'zaincash',
        orderId: orderid,
        transactionId: operationid,
        paymentStatus: WALLET_TRANSACTION_STATUS.COMPLETED
      },
      orderid
    );

    return {
      success: true,
      walletId: pendingTransaction.walletId,
      amount: pendingTransaction.amount,
      transactionId: operationid,
      orderId: orderid,
      wallet: result.wallet,
      transaction: result.transaction
    };
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Invalid payment token');
    }
    if (error.name === 'TokenExpiredError') {Fredirect
      throw new ApiError(httpStatus.BAD_REQUEST, 'Payment token expired');
    }
    throw error;
  }
};

module.exports = {
  initializePayment,
  verifyPayment
};


