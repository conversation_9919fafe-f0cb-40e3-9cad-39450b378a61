const Joi = require('joi');

const getRevenueChartData = {
  query: Joi.object().keys({
    filterType: Joi.string().valid('month', 'year').default('year')
  })
};

const getOverviewChartData = {
  query: Joi.object().keys({
    filterType: Joi.string().valid('month', 'year').default('year')
  })
};

const getUsersChartData = {
  query: Joi.object().keys({
    filterType: Joi.string().valid('month', 'year').default('year')
  })
};

const getBookingStats = {
  query: Joi.object().keys({
    timeFilter: Joi.string().valid('thisMonth', 'thisYear').default('thisMonth')
  })
};

module.exports = {
  getRevenueChartData,
  getOverviewChartData,
  getUsersChartData,
  getBookingStats
};
