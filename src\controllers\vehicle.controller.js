const catchAsync = require('../utils/catchAsync');
const { vehicleService } = require('../services');

const httpStatus = require('http-status');

const getUserVehicles = catchAsync(async (req, res) => {
  const userVehicles = await vehicleService.getUserVehicles(req.user.id, req.query);
  res.status(httpStatus.OK).json({
    status: true,
    data: userVehicles,
    message: "User vehicles fetched successfully"
  });
});
const addUserVehicle = catchAsync(async (req, res) => {
    const user_id = req.user.id
    const userVehicle = await vehicleService.addUserVehicle(req.body,user_id);
    res.status(httpStatus.CREATED).json({
      status: true,
      data: userVehicle,
      message: "Vehicle added successfully."
    });
  });

  const updateUserVehicle = catchAsync(async (req, res) => {
    const user_id = req.user.id
    const userVehicle = await vehicleService.updateUserVehicle(req.body,user_id);
    res.status(httpStatus.OK).json({
      status: true,
      data: userVehicle,
      message: "Vehicle updated successfully."
    });

  });
  
  const deleteUserVehicle = catchAsync(async (req, res) => {  
    if(!req.params.vehicleId){
      res.status(httpStatus.BAD_REQUEST).json({
        status: false,
        message: "Vehicle ID is required."
      });
    }
    const userVehicle = await vehicleService.deleteUserVehicle(req.params.vehicleId,req.user.id);
    res.status(httpStatus.OK).json({
      status: true,
      data: userVehicle,
      message: "Vehicle deleted successfully."
    });
  });

const getAllBrands = catchAsync(async (req, res) => {
  const brands = await vehicleService.getAllBrands();
  res.status(httpStatus.OK).json({
    status: true,
    data: brands,
    message: "Brands fetched successfully"
  });
});

const getModelsByBrand = catchAsync(async (req, res) => {
  const { brandId } = req.params;
  const models = await vehicleService.getModelsByBrand(brandId);
  res.status(httpStatus.OK).json({
    status: true,
    data: models,
    message: "Models fetched successfully"
  });
});

const getTrimsByModel = catchAsync(async (req, res) => {
  const { modelId } = req.params;
  const trims = await vehicleService.getTrimsByModel(modelId);
  res.status(httpStatus.OK).json({
    status: true,
    data: trims,
    message: "Trims fetched successfully"
  });
});

const getAllManufacturingUnits = catchAsync(async (req, res) => {
  const units = await vehicleService.getAllManufacturingUnits();
  res.status(httpStatus.OK).json({
    status: true,
    data: units,
    message: "Manufacturing units fetched successfully"
  });
});

const getAllBatteryCapacities = catchAsync(async (req, res) => {
  const batteries = await vehicleService.getAllBatteryCapacities();
  res.status(httpStatus.OK).json({
    status: true,
    data: batteries,
    message: "Battery capacities fetched successfully"
  });
});

const createVehiclePrimary = catchAsync(async (req, res) => {
  const userVehicle = await vehicleService.createVehiclePrimary(req.body, req.user.id);
  res.status(httpStatus.OK).json({
    status: true,
    data: userVehicle,
    message: "Primary vehicle created successfully."
  });
});
module.exports = {
  getAllBrands,
  getModelsByBrand,
  getTrimsByModel,
  getAllManufacturingUnits,
  getAllBatteryCapacities,
  addUserVehicle,
  getUserVehicles,
  deleteUserVehicle,
  createVehiclePrimary,
  updateUserVehicle
};
