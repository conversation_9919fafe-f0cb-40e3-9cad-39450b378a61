const express = require('express');
const router = express.Router();
const { adminAuth } = require('../../../middlewares/adminAuth');
const stationController= require('../../../controllers/admin/station.controller')
const validate = require('../../../middlewares/validate');
const stationValidation = require('../../../validations/admin/station.validation');

router
.route('/')
.get(adminAuth, validate(stationValidation.getStations),stationController.getStations);

router
.route('/:stationId')
.get(adminAuth, validate(stationValidation.getStationById),stationController.getStationById);

router
.route('/:stationId/chargers/:chargerId')
.get(adminAuth, validate(stationValidation.getChargerById),stationController.getChargerById);

router
.route('/:stationId/bookings')
.get(adminAuth, validate(stationValidation.getBookingHistory),stationController.getBookingHistory);

router
.route('/:stationId/bookings/:bookingId')
.get(adminAuth, validate(stationValidation.getBookingDetail),stationController.getBookingDetail);


router
.route('/:stationId/blocks')
.get(adminAuth, validate(stationValidation.getBlockDates),stationController.getBlockDates);

router
  .route('/:stationId/approve')
  .patch(adminAuth, validate(stationValidation.approveStation), stationController.approveStation);

router
.route('/:stationId/toggle-status')
.patch(adminAuth, validate(stationValidation.toggleStationStatus), stationController.toggleStationStatus);

module.exports = router;