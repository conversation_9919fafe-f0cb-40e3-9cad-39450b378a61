const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
  const MeterValue = sequelize.define(
    'meter_values',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      sessionId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'charging_sessions',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      chargerId: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'chargers',
          key: 'id'
        }
      },
      connectorId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      transactionId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      timestamp: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      measurand: {
        type: DataTypes.ENUM(
          'Energy.Active.Import.Register',
          'Energy.Active.Export.Register',
          'Energy.Reactive.Import.Register',
          'Energy.Reactive.Export.Register',
          'Power.Active.Import',
          'Power.Active.Export',
          'Power.Reactive.Import',
          'Power.Reactive.Export',
          'Current.Import',
          'Current.Export',
          'Voltage',
          'Temperature',
          'SoC',
          'Frequency'
        ),
        allowNull: false,
      },
      value: {
        type: DataTypes.DECIMAL(15, 3),
        allowNull: false,
      },
      unit: {
        type: DataTypes.STRING(10),
        allowNull: false,
      },
      phase: {
        type: DataTypes.ENUM('L1', 'L2', 'L3', 'N', 'L1-N', 'L2-N', 'L3-N', 'L1-L2', 'L2-L3', 'L3-L1'),
        allowNull: true,
      },
      location: {
        type: DataTypes.ENUM('Cable', 'EV', 'Inlet', 'Outlet', 'Body'),
        allowNull: true,
      },
      context: {
        type: DataTypes.ENUM('Interruption.Begin', 'Interruption.End', 'Sample.Clock', 'Sample.Periodic', 'Transaction.Begin', 'Transaction.End', 'Trigger', 'Other'),
        allowNull: true,
      }
    },
    {
      tableName: 'meter_values',
      timestamps: true,
      indexes: [
        { fields: ['sessionId'] },
        { fields: ['chargerId'] },
        { fields: ['connectorId'] },
        { fields: ['transactionId'] },
        { fields: ['timestamp'] },
        { fields: ['measurand'] },
        { fields: ['timestamp', 'measurand'] }
      ]
    }
  );

  return MeterValue;
};
